@extends('layouts.tutor')

@section('title', '<PERSON><PERSON>')

@section('content')
<div class="p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold text-gray-900">Edit Kursus</h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Mode
                    </span>
                </div>
                <p class="text-gray-600 mt-1">Perbarui informasi kursus Anda</p>
                <div class="flex items-center mt-2 space-x-4">
                    <div class="flex items-center text-sm text-blue-600">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        {{ $course->title }}
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ $course->category->name }}
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('tutor.courses') }}" class="btn border-gray-300 text-gray-700 hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <!-- Course Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 class="text-lg font-semibold text-gray-900">Informasi Kursus</h2>
            <p class="text-sm text-gray-600 mt-1">Perbarui detail kursus Anda di bawah ini</p>
        </div>

        <div class="p-6">
            <form action="{{ route('tutor.update-course', $course) }}" method="POST" enctype="multipart/form-data" class="space-y-8" id="courseForm">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Dasar</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="course_title" class="block text-sm font-medium text-gray-700 mb-2">Judul Kursus *</label>
                            <div class="relative">
                                <input type="text" id="course_title" name="course_title" value="{{ old('course_title', $course->title) }}"
                                       placeholder="Contoh: Belajar React.js untuk Pemula"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_title') border-red-500 @enderror"
                                       data-validation="required|min:10|max:255">
                                <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="course_category" class="block text-sm font-medium text-gray-700 mb-2">Kategori *</label>
                            <div class="relative">
                                <select id="course_category" name="course_category"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_category') border-red-500 @enderror"
                                        data-validation="required">
                                    <option value="">Pilih kategori</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('course_category', $course->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="validation-icon absolute right-10 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Course Description -->
                <div>
                    <label for="course_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Kursus *</label>
                    <div class="relative">
                        <textarea id="course_description" name="course_description" rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none @error('course_description') border-red-500 @enderror"
                                  placeholder="Jelaskan secara detail tentang kursus ini, apa yang akan dipelajari siswa, dan manfaat yang akan didapat..."
                                  data-validation="required|min:50|max:1000">{{ old('course_description', $course->description) }}</textarea>
                        <div class="validation-icon absolute right-3 top-3 hidden">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <div class="validation-message text-sm hidden"></div>
                        <div class="text-xs text-gray-500">
                            <span id="description-count">{{ strlen(old('course_description', $course->description)) }}</span>/1000 karakter
                        </div>
                    </div>
                    @error('course_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course Details -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Detail Kursus</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="course_level" class="block text-sm font-medium text-gray-700 mb-2">Level *</label>
                            <div class="relative">
                                <select id="course_level" name="course_level"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_level') border-red-500 @enderror"
                                        data-validation="required">
                                    <option value="">Pilih level</option>
                                    <option value="beginner" {{ old('course_level', $course->level) == 'beginner' ? 'selected' : '' }}>Pemula</option>
                                    <option value="intermediate" {{ old('course_level', $course->level) == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                                    <option value="advanced" {{ old('course_level', $course->level) == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                                </select>
                                <div class="validation-icon absolute right-10 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_level')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="course_duration" class="block text-sm font-medium text-gray-700 mb-2">Estimasi Durasi (Jam) *</label>
                            <div class="relative">
                                <input type="number" id="course_duration" name="course_duration" value="{{ old('course_duration', $course->duration) }}"
                                       placeholder="20"
                                       min="1" max="500" step="1"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_duration') border-red-500 @enderror">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <span class="text-gray-500 text-sm">jam</span>
                                </div>
                            </div>
                            <!-- Inline validation message like blog system -->
                            <div id="course_duration_validation" class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_duration')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Kursus *</label>
                            <div class="space-y-3" data-validation="required" data-validation-group="course_type">
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="free" {{ old('course_type', $course->is_free ? 'free' : 'paid') == 'free' ? 'checked' : '' }}
                                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Gratis</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="paid" {{ old('course_type', $course->is_free ? 'free' : 'paid') == 'paid' ? 'checked' : '' }}
                                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Berbayar</span>
                                </label>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="price-field" class="{{ old('course_type', $course->is_free ? 'free' : 'paid') == 'free' ? 'hidden' : '' }}">
                            <label for="course_price" class="block text-sm font-medium text-gray-700 mb-2">Harga Kursus (IDR) *</label>
                            <div class="relative">
                                <input type="number" id="course_price" name="course_price" value="{{ old('course_price', $course->price) }}"
                                       placeholder="30000" min="30000" step="1000"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_price') border-red-500 @enderror"
                                       data-validation="required_if:course_type,paid|min:30000|numeric|step:1000">
                                <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            <p class="mt-1 text-xs text-gray-500">
                                Harga minimum IDR 30.000 dan harus kelipatan 1000. Platform fee 5%, tutor mendapat 60% (tanpa referral) atau 80% (dengan referral).
                            </p>
                            @error('course_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Tujuan Pembelajaran</h3>
                    <div id="learning-outcomes-container">
                        @if(old('learning_outcomes', $course->learning_outcomes))
                            @foreach(old('learning_outcomes', $course->learning_outcomes) as $index => $outcome)
                                <div class="learning-outcome-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="learning_outcomes[]" value="{{ $outcome }}"
                                               placeholder="Contoh: Memahami konsep dasar React.js"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="learning-outcome-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="learning_outcomes[]" value=""
                                           placeholder="Contoh: Memahami konsep dasar React.js"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addLearningOutcome()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Tujuan Pembelajaran
                    </button>
                </div>

                <!-- Requirements -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Prasyarat</h3>
                    <div id="requirements-container">
                        @if(old('requirements', $course->requirements))
                            @foreach(old('requirements', $course->requirements) as $index => $requirement)
                                <div class="requirement-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="requirements[]" value="{{ $requirement }}"
                                               placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="requirement-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="requirements[]" value=""
                                           placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addRequirement()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Prasyarat
                    </button>
                </div>

                <!-- Target Audience -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Target Audience</h3>
                    <div id="target-audience-container">
                        @if(old('target_audience', $course->target_audience))
                            @foreach(old('target_audience', $course->target_audience) as $index => $audience)
                                <div class="target-audience-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="target_audience[]" value="{{ $audience }}"
                                               placeholder="Contoh: Pemula yang ingin belajar web development"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="target-audience-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="target_audience[]" value=""
                                           placeholder="Contoh: Pemula yang ingin belajar web development"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addTargetAudience()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Target Audience
                    </button>
                </div>

                <!-- Thumbnail Upload -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Thumbnail Kursus</h3>
                    <div class="space-y-4">
                        @if($course->thumbnail)
                            <div class="flex items-center space-x-4">
                                <img src="{{ Storage::url($course->thumbnail) }}" alt="Current thumbnail" class="w-20 h-20 object-cover rounded-lg border border-gray-200">
                                <div>
                                    <p class="text-sm text-gray-600">Thumbnail saat ini</p>
                                    <p class="text-xs text-gray-500">Upload gambar baru untuk menggantinya</p>
                                </div>
                            </div>
                        @endif

                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                            <input type="file" id="thumbnail" name="thumbnail" accept="image/*" class="hidden" onchange="previewThumbnail(this)">
                            <div id="thumbnail-upload-area" onclick="document.getElementById('thumbnail').click()" class="cursor-pointer">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium text-emerald-600 hover:text-emerald-500">Klik untuk upload</span>
                                        atau drag & drop gambar
                                    </p>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF hingga 10MB</p>
                                </div>
                            </div>
                            <div id="thumbnail-preview" class="hidden mt-4">
                                <img id="thumbnail-preview-img" src="" alt="Preview" class="mx-auto max-w-xs max-h-48 rounded-lg border border-gray-200">
                                <button type="button" onclick="clearThumbnail()" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                    Hapus gambar
                                </button>
                            </div>
                        </div>
                        @error('thumbnail')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6 border-t border-gray-200">
                    <a href="{{ route('tutor.courses') }}" class="btn bg-gray-500 text-white hover:bg-gray-600 border-gray-500">
                        Batal
                    </a>
                    <button type="submit" class="btn bg-emerald-600 text-white hover:bg-emerald-700">
                        Simpan Perubahan
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle price field visibility
function togglePriceField() {
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;
    const priceField = document.getElementById('price-field');
    
    if (courseType === 'paid') {
        priceField.classList.remove('hidden');
    } else {
        priceField.classList.add('hidden');
    }
}

// Character counter for description
document.getElementById('course_description').addEventListener('input', function() {
    const count = this.value.length;
    document.getElementById('description-count').textContent = count;
});

// Form validation
function validateFormBeforeSubmit() {
    // Basic validation
    const title = document.getElementById('course_title').value.trim();
    const category = document.getElementById('course_category').value;
    const description = document.getElementById('course_description').value.trim();
    const level = document.getElementById('course_level').value;
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;
    
    if (!title || title.length < 10) {
        alert('Judul kursus minimal 10 karakter');
        return false;
    }
    
    if (!category) {
        alert('Pilih kategori kursus');
        return false;
    }
    
    if (!description || description.length < 50) {
        alert('Deskripsi kursus minimal 50 karakter');
        return false;
    }
    
    if (!level) {
        alert('Pilih level kursus');
        return false;
    }
    
    if (!courseType) {
        alert('Pilih tipe kursus');
        return false;
    }
    
    if (courseType === 'paid') {
        const price = document.getElementById('course_price').value;
        if (!price || price < 30000 || price % 1000 !== 0) {
            alert('Harga kursus minimal IDR 30.000 dan harus kelipatan 1000');
            return false;
        }
    }
    
    return true;
}

// Dynamic field management functions
function addLearningOutcome() {
    const container = document.getElementById('learning-outcomes-container');
    const div = document.createElement('div');
    div.className = 'learning-outcome-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="learning_outcomes[]" value=""
                   placeholder="Contoh: Memahami konsep dasar React.js"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeLearningOutcome(button) {
    button.closest('.learning-outcome-item').remove();
}

function addRequirement() {
    const container = document.getElementById('requirements-container');
    const div = document.createElement('div');
    div.className = 'requirement-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="requirements[]" value=""
                   placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeRequirement(button) {
    button.closest('.requirement-item').remove();
}

function addTargetAudience() {
    const container = document.getElementById('target-audience-container');
    const div = document.createElement('div');
    div.className = 'target-audience-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="target_audience[]" value=""
                   placeholder="Contoh: Pemula yang ingin belajar web development"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeTargetAudience(button) {
    button.closest('.target-audience-item').remove();
}

// Thumbnail preview functions
function previewThumbnail(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('thumbnail-preview-img').src = e.target.result;
            document.getElementById('thumbnail-preview').classList.remove('hidden');
            document.getElementById('thumbnail-upload-area').classList.add('hidden');
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function clearThumbnail() {
    document.getElementById('thumbnail').value = '';
    document.getElementById('thumbnail-preview').classList.add('hidden');
    document.getElementById('thumbnail-upload-area').classList.remove('hidden');
}

// Form submission handler
document.getElementById('courseForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Basic validation
    const title = document.getElementById('course_title').value.trim();
    const category = document.getElementById('course_category').value;
    const description = document.getElementById('course_description').value.trim();
    const level = document.getElementById('course_level').value;
    const duration = document.getElementById('course_duration').value.trim();
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;

    if (!title || title.length < 10) {
        alert('Judul kursus minimal 10 karakter');
        return false;
    }

    if (!category) {
        alert('Pilih kategori kursus');
        return false;
    }

    if (!description || description.length < 50) {
        alert('Deskripsi kursus minimal 50 karakter');
        return false;
    }

    if (!level) {
        alert('Pilih level kursus');
        return false;
    }

    if (!duration) {
        alert('Estimasi durasi harus diisi');
        return false;
    }

    if (!courseType) {
        alert('Pilih tipe kursus');
        return false;
    }

    if (courseType === 'paid') {
        const price = document.getElementById('course_price').value;
        if (!price || price < 30000 || price % 1000 !== 0) {
            alert('Harga kursus minimal IDR 30.000 dan harus kelipatan 1000');
            return false;
        }
    }

    // Submit the form
    this.submit();
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    togglePriceField();
});
</script>
@endsection
