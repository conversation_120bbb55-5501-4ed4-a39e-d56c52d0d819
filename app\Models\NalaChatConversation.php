<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class NalaChatConversation extends Model
{
    use HasFactory;

    protected $table = 'nala_chat_conversations';
    
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'user_id',
        'title',
        'session_id',
        'status',
        'started_route',
        'started_context',
        'context_data',
        'message_count',
        'last_message_at',
    ];

    protected $casts = [
        'context_data' => 'array',
        'last_message_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the user that owns the conversation
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all messages for this conversation
     */
    public function messages(): HasMany
    {
        return $this->hasMany(NalaChatMessage::class, 'conversation_id');
    }

    /**
     * Get the latest messages for this conversation
     */
    public function latestMessages($limit = 10)
    {
        return $this->messages()
            ->where('status', '!=', 'deleted')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();
    }

    /**
     * Generate a title from the first user message
     */
    public function generateTitle()
    {
        $firstMessage = $this->messages()
            ->where('sender', 'user')
            ->where('status', '!=', 'deleted')
            ->orderBy('created_at', 'asc')
            ->first();

        if ($firstMessage) {
            $title = Str::limit($firstMessage->content, 50, '...');
            $this->update(['title' => $title]);
            return $title;
        }

        return 'Chat dengan Nala';
    }

    /**
     * Update message count and last message timestamp
     */
    public function updateStats()
    {
        $messageCount = $this->messages()
            ->where('status', '!=', 'deleted')
            ->count();

        $lastMessage = $this->messages()
            ->where('status', '!=', 'deleted')
            ->orderBy('created_at', 'desc')
            ->first();

        $this->update([
            'message_count' => $messageCount,
            'last_message_at' => $lastMessage ? $lastMessage->created_at : null,
        ]);
    }

    /**
     * Archive this conversation
     */
    public function archive()
    {
        $this->update(['status' => 'archived']);
    }

    /**
     * Delete this conversation (soft delete by changing status)
     */
    public function softDelete()
    {
        $this->update(['status' => 'deleted']);
    }

    /**
     * Scope for active conversations
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for user's conversations
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
