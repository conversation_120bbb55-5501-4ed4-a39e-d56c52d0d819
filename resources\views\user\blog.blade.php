@extends('layouts.user')

@section('title', 'Blog - Ngambiskuy')

@section('content')
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Artikel Tersimpan</h1>
                <p class="text-gray-600 mt-1">Koleksi artikel yang telah Anda simpan untuk dibaca nanti</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('blog.index') }}" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Je<PERSON>jahi Artikel Baru
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Tersimpan</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['total_saved'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kategori</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['categories_saved'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Waktu Baca</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['total_read_time'] }} menit</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Saved Articles -->
    <div class="bg-white rounded-lg shadow-sm mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Artikel Tersimpan</h2>
        </div>
        <div class="p-6">
            @if($savedArticles->count() > 0)
                <div class="space-y-6">
                    @foreach($savedArticles as $article)
                        <div class="flex space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                            @if($article->featured_image)
                                <div class="w-24 h-24 flex-shrink-0">
                                    <img src="{{ asset('storage/' . $article->featured_image) }}"
                                         alt="{{ $article->title }}"
                                         class="w-full h-full object-cover rounded-lg">
                                </div>
                            @else
                                <div class="w-24 h-24 flex-shrink-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif

                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                        <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                        </svg>
                                        Tersimpan
                                    </span>
                                    @if($article->category)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                                            {{ $article->category->name }}
                                        </span>
                                    @endif
                                    @if($article->is_featured)
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                            Unggulan
                                        </span>
                                    @endif
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $article->title }}</h3>
                                <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $article->excerpt }}</p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        <span>{{ $article->author->name }}</span>
                                        <span>•</span>
                                        <span>{{ $article->read_time_text }}</span>
                                        <span>•</span>
                                        <span>Disimpan {{ \Carbon\Carbon::parse($article->pivot->saved_at)->diffForHumans() }}</span>
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <button onclick="unsaveArticle('{{ $article->slug }}')" class="text-red-600 hover:text-red-800 text-sm">
                                            Hapus
                                        </button>
                                        <a href="{{ route('blog.show', $article) }}" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                            Baca →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Artikel Tersimpan</h3>
                    <p class="text-gray-600 mb-6">Simpan artikel menarik untuk dibaca nanti. Mulai jelajahi artikel yang tersedia.</p>
                    <a href="{{ route('blog.index') }}" class="btn btn-primary">
                        Jelajahi Artikel
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Recommendations -->
    @if($savedArticles->count() > 0 && $featuredPosts->count() > 0)
        <div class="bg-white rounded-lg shadow-sm mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Rekomendasi Untuk Anda</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($featuredPosts as $post)
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:bg-gray-100 transition-colors">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                    Unggulan
                                </span>
                                @if($post->category)
                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                                        {{ $post->category->name }}
                                    </span>
                                @endif
                            </div>

                            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $post->title }}</h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $post->excerpt }}</p>

                            <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                <span>{{ $post->author->name }}</span>
                                <span>{{ $post->read_time_text }}</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                @php
                                    $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                @endphp
                                @if($isSaved)
                                    <button onclick="unsaveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Tersimpan
                                    </button>
                                @else
                                    <button onclick="saveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                        </svg>
                                        Simpan
                                    </button>
                                @endif
                                <a href="{{ route('blog.show', $post) }}" class="flex-1 text-center px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                                    Baca
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Discovery Section (if no saved articles) -->
    @if($savedArticles->count() == 0)
        <div class="bg-white rounded-lg shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Artikel Terbaru</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($recentPosts as $post)
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:bg-gray-100 transition-colors">
                            <div class="flex items-center space-x-2 mb-3">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                        {{ $post->category->name }}
                                    </span>
                                @endif
                                @if($post->is_featured)
                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                        Unggulan
                                    </span>
                                @endif
                            </div>

                            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $post->title }}</h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $post->excerpt }}</p>

                            <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                <span>{{ $post->author->name }}</span>
                                <span>{{ $post->read_time_text }}</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                @php
                                    $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                @endphp
                                @if($isSaved)
                                    <button onclick="unsaveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Tersimpan
                                    </button>
                                @else
                                    <button onclick="saveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                        </svg>
                                        Simpan
                                    </button>
                                @endif
                                <a href="{{ route('blog.show', $post) }}" class="flex-1 text-center px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                                    Baca
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-8 text-center">
                    <a href="{{ route('blog.index') }}" class="btn btn-outline">
                        Lihat Semua Artikel
                    </a>
                </div>
            </div>
        </div>
    @endif

    <!-- Reading Tips -->
    @if(!auth()->user()->hasActiveMembership())
        <div class="mt-8 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6 border border-purple-200">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-purple-900 mb-2">Tingkatkan Pengalaman Membaca</h3>
                    <p class="text-purple-700 mb-4">
                        Dengan NALA Membership, dapatkan akses ke fitur AI yang dapat membantu Anda memahami artikel dengan lebih baik,
                        membuat ringkasan, dan mendapatkan rekomendasi bacaan yang dipersonalisasi.
                    </p>
                    <a href="{{ route('user.membership') }}" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        Upgrade ke NALA Membership
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
async function saveArticle(articleSlug) {
    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            showNotification('Artikel berhasil disimpan!', 'success');
            // Refresh page to update the view
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menyimpan artikel', 'error');
        }
    } catch (error) {
        console.error('Error saving article:', error);
        showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
    }
}

async function unsaveArticle(articleSlug) {
    if (!confirm('Apakah Anda yakin ingin menghapus artikel ini dari daftar simpan?')) {
        return;
    }

    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Artikel berhasil dihapus dari daftar simpan!', 'success');
            // Refresh page to update the view
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menghapus artikel', 'error');
        }
    } catch (error) {
        console.error('Error unsaving article:', error);
        showNotification('Terjadi kesalahan saat menghapus artikel', 'error');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}
</script>
@endsection
