<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\NalaAIController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Nala AI Assistant Routes
Route::middleware(['web'])->group(function () {
    Route::post('/nala-chat', [\App\Http\Controllers\Nala\ChatController::class, 'chat'])->name('api.nala.chat');
    Route::post('/nala-test-chat', [\App\Http\Controllers\Nala\ChatController::class, 'testChat'])->name('api.nala.test-chat');

    // Chat history management routes (require authentication)
    Route::middleware(['auth'])->group(function () {
        Route::get('/nala-chat/history', [NalaAIController::class, 'getChatHistory'])->name('api.nala.history');
        Route::get('/nala-chat/conversation/{conversationId}', [NalaAIController::class, 'getConversation'])->name('api.nala.conversation');
        Route::delete('/nala-chat/conversation/{conversationId}', [NalaAIController::class, 'deleteConversation'])->name('api.nala.delete-conversation');
        Route::delete('/nala-chat/clear-history', [NalaAIController::class, 'clearChatHistory'])->name('api.nala.clear-history');
        Route::post('/nala-chat/update-profile', [\App\Http\Controllers\Nala\UserProfileController::class, 'updateProfileFromConversation'])->name('api.nala.update-profile');
    });
});
