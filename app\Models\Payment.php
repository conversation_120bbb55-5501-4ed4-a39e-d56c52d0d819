<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Payment extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'payment_type',
        'payable_id',
        'payable_type',
        'transaction_id',
        'external_transaction_id',
        'amount',
        'platform_fee',
        'tutor_earnings',
        'currency',
        'status',
        'paid_at',
        'failed_at',
        'cancelled_at',
        'refunded_at',
        'payment_method',
        'payment_gateway',
        'gateway_response',
        'notes',
        'receipt_url',
        'metadata',
        'referrer_id',
        'referral_bonus',
        'has_referral_bonus',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'tutor_earnings' => 'decimal:2',
        'paid_at' => 'datetime',
        'failed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'refunded_at' => 'datetime',
        'gateway_response' => 'array',
        'metadata' => 'array',
        'referral_bonus' => 'decimal:2',
        'has_referral_bonus' => 'boolean',
    ];

    /**
     * Get the user that made the payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the referrer user.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the payable model (membership, course, etc.).
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include failed payments.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope a query by payment type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('payment_type', $type);
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the payment failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'paid_at' => now(),
        ]);
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(string $reason = null): void
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'notes' => $reason,
        ]);
    }

    /**
     * Mark payment as cancelled.
     */
    public function markAsCancelled(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'notes' => $reason,
        ]);
    }

    /**
     * Mark payment as refunded.
     */
    public function markAsRefunded(string $reason = null): void
    {
        $this->update([
            'status' => 'refunded',
            'refunded_at' => now(),
            'notes' => $reason,
        ]);
    }

    /**
     * Get the formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'IDR ' . number_format($this->amount, 0, ',', '.');
    }

    /**
     * Get the formatted platform fee.
     */
    public function getFormattedPlatformFeeAttribute(): string
    {
        return 'IDR ' . number_format($this->platform_fee, 0, ',', '.');
    }

    /**
     * Get the formatted tutor earnings.
     */
    public function getFormattedTutorEarningsAttribute(): string
    {
        return 'IDR ' . number_format($this->tutor_earnings, 0, ',', '.');
    }

    /**
     * Calculate platform fee based on amount.
     */
    public static function calculatePlatformFee(float $amount): float
    {
        return $amount * 0.05; // 5% platform fee
    }

    /**
     * Calculate tutor earnings based on amount and referral status.
     */
    public static function calculateTutorEarnings(float $amount, bool $hasReferral = false): float
    {
        $platformFee = self::calculatePlatformFee($amount);
        $afterPlatformFee = $amount - $platformFee;
        
        $percentage = $hasReferral ? 0.80 : 0.60; // 80% with referral, 60% without
        return $afterPlatformFee * $percentage;
    }

    /**
     * Get payment type name in Indonesian.
     */
    public function getPaymentTypeNameAttribute(): string
    {
        return match($this->payment_type) {
            'membership' => 'Membership NALA',
            'course' => 'Kursus',
            'certification' => 'Sertifikasi',
            'exam' => 'Ujian',
            default => ucfirst($this->payment_type)
        };
    }

    /**
     * Get status name in Indonesian.
     */
    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Menunggu',
            'processing' => 'Diproses',
            'completed' => 'Selesai',
            'failed' => 'Gagal',
            'cancelled' => 'Dibatalkan',
            'refunded' => 'Dikembalikan',
            default => ucfirst($this->status)
        };
    }
}
