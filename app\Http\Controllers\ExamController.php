<?php

namespace App\Http\Controllers;

use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\ExamEnrollment;
use App\Models\ExamAnswer;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExamController extends Controller
{
    /**
     * Display a listing of published exams.
     */
    public function index(Request $request)
    {
        $query = Exam::with(['tutor', 'category'])
            ->published()
            ->orderBy('created_at', 'desc');

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by difficulty
        if ($request->filled('difficulty')) {
            $query->where('difficulty_level', $request->difficulty);
        }

        // Filter by price
        if ($request->filled('price_filter')) {
            switch ($request->price_filter) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'paid':
                    $query->where('price', '>', 0);
                    break;
                case 'under_50k':
                    $query->where('price', '>', 0)->where('price', '<', 50000);
                    break;
                case 'under_100k':
                    $query->where('price', '>', 0)->where('price', '<', 100000);
                    break;
                case 'over_100k':
                    $query->where('price', '>=', 100000);
                    break;
            }
        }

        // Search by title or description
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $exams = $query->paginate(12);
        $categories = Category::orderBy('name')->get();

        return view('exams.index', compact('exams', 'categories'));
    }

    /**
     * Display the specified exam.
     */
    public function show(Exam $exam)
    {
        // Only show published exams to public
        if (!$exam->is_published) {
            abort(404, 'Ujian tidak ditemukan.');
        }

        $exam->load(['tutor', 'category', 'questions']);

        $stats = [
            'total_questions' => $exam->questions->count(),
            'total_points' => $exam->questions->sum('points'),
            'total_enrollments' => $exam->enrollments->count(),
            'average_score' => $exam->attempts->avg('score_percentage') ?? 0,
        ];

        $userEnrollment = null;
        $userAttempts = collect();
        $canTakeExam = false;

        if (Auth::check()) {
            $userEnrollment = $exam->enrollments()->where('user_id', Auth::id())->first();
            if ($userEnrollment) {
                $userAttempts = $exam->attempts()
                    ->where('user_id', Auth::id())
                    ->orderBy('created_at', 'desc')
                    ->get();
                $canTakeExam = $userAttempts->count() < $exam->max_attempts;
            }
        }

        return view('exams.show', compact('exam', 'stats', 'userEnrollment', 'userAttempts', 'canTakeExam'));
    }

    /**
     * Enroll user in the exam.
     */
    public function enroll(Request $request, Exam $exam)
    {
        if (!$exam->is_published) {
            return back()->with('error', 'Ujian tidak tersedia.');
        }

        $user = Auth::user();

        // Check if already enrolled
        $existingEnrollment = $exam->enrollments()->where('user_id', $user->id)->first();
        if ($existingEnrollment) {
            return back()->with('info', 'Anda sudah terdaftar dalam ujian ini.');
        }

        try {
            DB::beginTransaction();

            // Create enrollment
            $enrollment = ExamEnrollment::create([
                'exam_id' => $exam->id,
                'user_id' => $user->id,
                'amount_paid' => $exam->price,
                'payment_status' => $exam->price == 0 ? 'paid' : 'pending',
                'enrolled_at' => now(),
                'is_active' => true,
            ]);

            // If free exam, mark as paid immediately
            if ($exam->price == 0) {
                $enrollment->update(['payment_status' => 'paid']);
            }

            DB::commit();

            if ($exam->price == 0) {
                return redirect()->route('exams.take', $exam)
                    ->with('success', 'Berhasil mendaftar ujian gratis! Anda dapat langsung mengerjakan ujian.');
            } else {
                // Redirect to payment page (to be implemented)
                return back()->with('success', 'Berhasil mendaftar ujian. Silakan lakukan pembayaran untuk mengakses ujian.');
            }

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat mendaftar ujian. Silakan coba lagi.');
        }
    }

    /**
     * Show the exam taking interface.
     */
    public function take(Request $request, Exam $exam)
    {
        if (!$exam->is_published) {
            abort(404, 'Ujian tidak tersedia.');
        }

        $user = Auth::user();

        // Check enrollment
        $enrollment = $exam->enrollments()
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->where('payment_status', 'paid')
            ->first();

        if (!$enrollment) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Anda belum terdaftar atau belum melakukan pembayaran untuk ujian ini.');
        }

        // Check attempt limit
        $attemptCount = $exam->attempts()->where('user_id', $user->id)->count();
        if ($attemptCount >= $exam->max_attempts) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Anda telah mencapai batas maksimal percobaan untuk ujian ini.');
        }

        // Check if there's an ongoing attempt
        $ongoingAttempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if ($ongoingAttempt) {
            // Check if attempt has expired
            $timeLimit = $exam->time_limit * 60; // Convert to seconds
            $timeElapsed = now()->diffInSeconds($ongoingAttempt->started_at);

            if ($timeElapsed >= $timeLimit) {
                // Auto-submit expired attempt
                $this->autoSubmitAttempt($ongoingAttempt);
                return redirect()->route('exams.result', [$exam, $ongoingAttempt])
                    ->with('warning', 'Waktu ujian telah habis. Ujian telah otomatis diselesaikan.');
            }

            $attempt = $ongoingAttempt;
        } else {
            // Create new attempt
            $attempt = ExamAttempt::create([
                'exam_id' => $exam->id,
                'user_id' => $user->id,
                'attempt_number' => $attemptCount + 1,
                'started_at' => now(),
                'status' => 'in_progress',
                'total_questions' => $exam->questions->count(),
                'max_points' => $exam->questions->sum('points'),
            ]);
        }

        $exam->load(['questions.options']);

        // Shuffle questions if enabled (only for new attempts)
        $questions = $exam->shuffle_questions && !$ongoingAttempt
            ? $exam->questions->shuffle()
            : $exam->questions;

        // Get current question index from request, default to 1
        $currentQuestionIndex = max(1, min($request->get('question', 1), $questions->count()));
        $currentQuestion = $questions->skip($currentQuestionIndex - 1)->first();

        // Get existing answers for this attempt
        $existingAnswers = ExamAnswer::where('attempt_id', $attempt->id)
            ->get()
            ->keyBy('question_id')
            ->map(function ($answer) {
                // For multiple choice/true-false, return the selected option ID
                if ($answer->selected_option_id) {
                    return $answer->selected_option_id;
                }
                // For short answer, return the text
                if ($answer->answer_text) {
                    return $answer->answer_text;
                }
                // Return null if no answer
                return null;
            })
            ->filter(function ($value) {
                // Only keep non-null values
                return $value !== null;
            })
            ->toArray();

        Log::info('Retrieved existing answers', [
            'attempt_id' => $attempt->id,
            'answers_count' => count($existingAnswers),
            'answers' => $existingAnswers
        ]);

        $timeRemaining = $exam->time_limit * 60 - now()->diffInSeconds($attempt->started_at);

        return view('exams.take', compact(
            'exam',
            'attempt',
            'questions',
            'currentQuestion',
            'currentQuestionIndex',
            'existingAnswers',
            'timeRemaining'
        ));
    }

    /**
     * Save answer for a specific question during exam navigation.
     */
    public function saveAnswer(Request $request, Exam $exam)
    {
        $user = Auth::user();

        // Get the current attempt
        $attempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if (!$attempt) {
            return response()->json(['error' => 'No active exam attempt found'], 404);
        }

        $validated = $request->validate([
            'question_id' => 'required|exists:exam_questions,id',
            'selected_option_id' => 'nullable|exists:exam_question_options,id',
            'answer_text' => 'nullable|string',
        ]);

        // Save or update the answer
        $answer = ExamAnswer::updateOrCreate(
            [
                'attempt_id' => $attempt->id,
                'question_id' => $validated['question_id'],
            ],
            [
                'selected_option_id' => $validated['selected_option_id'] ?? null,
                'answer_text' => $validated['answer_text'] ?? null,
                'answered_at' => now(),
            ]
        );

        // Log the save for debugging
        Log::info('Answer saved', [
            'attempt_id' => $attempt->id,
            'question_id' => $validated['question_id'],
            'selected_option_id' => $validated['selected_option_id'] ?? null,
            'answer_text' => $validated['answer_text'] ?? null,
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Submit the exam attempt.
     */
    public function submit(Request $request, Exam $exam)
    {
        $user = Auth::user();

        // Get the ongoing attempt
        $attempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if (!$attempt) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Tidak ada ujian yang sedang berlangsung.');
        }

        try {
            DB::beginTransaction();

            $answers = $request->input('answers', []);
            $correctAnswers = 0;
            $totalPoints = 0;

            // Process each answer
            foreach ($answers as $questionId => $answerData) {
                $question = $exam->questions()->find($questionId);
                if (!$question) continue;

                $isCorrect = false;
                $pointsEarned = 0;

                if ($question->type === 'multiple_choice' || $question->type === 'true_false') {
                    $selectedOptionId = $answerData['selected_option'] ?? null;
                    $selectedOption = $question->options()->find($selectedOptionId);

                    if ($selectedOption && $selectedOption->is_correct) {
                        $isCorrect = true;
                        $pointsEarned = $question->points;
                        $correctAnswers++;
                    }

                    ExamAnswer::create([
                        'attempt_id' => $attempt->id,
                        'question_id' => $question->id,
                        'selected_option_id' => $selectedOptionId,
                        'is_correct' => $isCorrect,
                        'points_earned' => $pointsEarned,
                        'answered_at' => now(),
                    ]);
                } elseif ($question->type === 'short_answer') {
                    $answerText = $answerData['answer_text'] ?? '';

                    ExamAnswer::create([
                        'attempt_id' => $attempt->id,
                        'question_id' => $question->id,
                        'answer_text' => $answerText,
                        'is_correct' => null, // To be graded manually
                        'points_earned' => 0, // To be assigned after grading
                        'answered_at' => now(),
                    ]);
                }

                $totalPoints += $pointsEarned;
            }

            // Calculate score
            $scorePercentage = $attempt->max_points > 0
                ? ($totalPoints / $attempt->max_points) * 100
                : 0;

            $isPassed = $scorePercentage >= $exam->passing_score;

            // Update attempt
            $attempt->update([
                'completed_at' => now(),
                'submitted_at' => now(),
                'time_taken' => now()->diffInSeconds($attempt->started_at),
                'answered_questions' => count($answers),
                'correct_answers' => $correctAnswers,
                'score_percentage' => $scorePercentage,
                'total_points' => $totalPoints,
                'is_passed' => $isPassed,
                'status' => 'completed',
            ]);

            // Update enrollment stats
            $enrollment = $exam->enrollments()->where('user_id', $user->id)->first();
            if ($enrollment) {
                $enrollment->update([
                    'attempts_used' => $enrollment->attempts_used + 1,
                    'best_score' => max($enrollment->best_score ?? 0, $scorePercentage),
                    'has_passed' => $enrollment->has_passed || $isPassed,
                    'last_attempt_at' => now(),
                ]);

                if ($isPassed && !$enrollment->passed_at) {
                    $enrollment->update(['passed_at' => now()]);
                }
            }

            DB::commit();

            return redirect()->route('exams.result', [$exam, $attempt])
                ->with('success', 'Ujian berhasil diselesaikan!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat menyimpan jawaban. Silakan coba lagi.');
        }
    }

    /**
     * Show exam result.
     */
    public function result(Exam $exam, ExamAttempt $attempt)
    {
        $user = Auth::user();

        // Ensure the attempt belongs to the authenticated user
        if ($attempt->user_id !== $user->id || $attempt->exam_id !== $exam->id) {
            abort(403, 'Akses tidak diizinkan.');
        }

        $attempt->load(['answers.question', 'answers.selectedOption']);

        $enrollment = $exam->enrollments()->where('user_id', $user->id)->first();

        return view('exams.result', compact('exam', 'attempt', 'enrollment'));
    }

    /**
     * Auto-submit an expired attempt.
     */
    private function autoSubmitAttempt(ExamAttempt $attempt)
    {
        $attempt->update([
            'completed_at' => now(),
            'submitted_at' => now(),
            'time_taken' => $attempt->exam->time_limit * 60,
            'status' => 'expired',
        ]);

        // Calculate score based on answered questions
        $answers = $attempt->answers;
        $correctAnswers = $answers->where('is_correct', true)->count();
        $totalPoints = $answers->sum('points_earned');
        $scorePercentage = $attempt->max_points > 0
            ? ($totalPoints / $attempt->max_points) * 100
            : 0;

        $attempt->update([
            'correct_answers' => $correctAnswers,
            'score_percentage' => $scorePercentage,
            'total_points' => $totalPoints,
            'is_passed' => $scorePercentage >= $attempt->exam->passing_score,
        ]);
    }
}
