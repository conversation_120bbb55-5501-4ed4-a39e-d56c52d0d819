<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_answers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('attempt_id'); // Foreign key to exam_attempts table
            $table->uuid('question_id'); // Foreign key to exam_questions table
            $table->uuid('selected_option_id')->nullable(); // Foreign key to exam_question_options table (for multiple choice)

            // Answer Information
            $table->text('answer_text')->nullable(); // For short answer and essay questions
            $table->boolean('is_correct')->nullable(); // Whether the answer is correct (calculated)
            $table->integer('points_earned')->default(0); // Points earned for this answer
            $table->timestamp('answered_at')->nullable(); // When this question was answered

            // Grading (for essay questions)
            $table->text('feedback')->nullable(); // Feedback from grader
            $table->uuid('graded_by')->nullable(); // Foreign key to users table (tutor who graded)
            $table->timestamp('graded_at')->nullable(); // When this answer was graded

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('attempt_id')->references('id')->on('exam_attempts')->onDelete('cascade');
            $table->foreign('question_id')->references('id')->on('exam_questions')->onDelete('cascade');
            $table->foreign('selected_option_id')->references('id')->on('exam_question_options')->onDelete('set null');
            $table->foreign('graded_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index('attempt_id');
            $table->index('question_id');
            $table->index('selected_option_id');
            $table->index('is_correct');
            $table->index(['attempt_id', 'question_id']); // Composite index for faster lookups
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_answers');
    }
};
