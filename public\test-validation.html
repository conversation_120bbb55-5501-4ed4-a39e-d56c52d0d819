<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Validation Test - Ngambiskuy</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/validation-styles.css">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Real-time Validation Test</h1>

        <form id="testForm" class="space-y-6 bg-white p-6 rounded-lg shadow">
            <!-- Course Title -->
            <div>
                <label for="course_title" class="block text-sm font-medium text-gray-700 mb-2">
                    Course Title <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="text" id="course_title" name="course_title"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                           placeholder="Enter course title (min 10 characters)"
                           data-validation="required|min:10|max:255">
                    <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- Course Description -->
            <div>
                <label for="course_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Course Description <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <textarea id="course_description" name="course_description" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none"
                              placeholder="Enter course description (min 50 characters)"
                              data-validation="required|min:50|max:1000"></textarea>
                    <div class="validation-icon absolute right-3 top-3 hidden">
                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex justify-between items-center mt-1">
                    <div class="validation-message text-sm hidden"></div>
                    <div class="text-xs text-gray-500">
                        <span id="description-count">0</span>/1000 characters (minimum 50)
                    </div>
                </div>
            </div>

            <!-- Course Price -->
            <div>
                <label for="course_price" class="block text-sm font-medium text-gray-700 mb-2">
                    Course Price (IDR) <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="number" id="course_price" name="course_price"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                           placeholder="30000" min="30000"
                           data-validation="required|numeric|min:30000">
                    <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="validation-message mt-1 text-sm hidden"></div>
                <p class="text-sm text-gray-600 mt-1">Minimum price: IDR 30,000</p>
            </div>

            <!-- Email Test -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="email" id="email" name="email"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                           placeholder="<EMAIL>"
                           data-validation="required|email">
                    <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- URL Test -->
            <div>
                <label for="website" class="block text-sm font-medium text-gray-700 mb-2">
                    Website URL
                </label>
                <div class="relative">
                    <input type="url" id="website" name="website"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                           placeholder="https://example.com"
                           data-validation="url">
                    <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button type="submit" class="w-full bg-emerald-600 text-white py-3 px-4 rounded-lg hover:bg-emerald-700 transition-colors">
                    Test Validation
                </button>
            </div>
        </form>

        <!-- Test Results -->
        <div id="test-results" class="mt-8 p-4 bg-gray-100 rounded-lg hidden">
            <h3 class="font-semibold text-gray-900 mb-2">Validation Results:</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        // Real-time validation system
        class FormValidator {
            constructor(formId) {
                this.form = document.getElementById(formId);
                this.validationRules = {
                    required: (value) => value.trim() !== '',
                    min: (value, param) => value.length >= parseInt(param),
                    max: (value, param) => value.length <= parseInt(param),
                    numeric: (value) => !isNaN(value) && value !== '',
                    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                    url: (value) => /^https?:\/\/.+/.test(value),
                };
                this.init();
            }

            init() {
                const fields = this.form.querySelectorAll('[data-validation]');
                fields.forEach(field => {
                    this.addFieldListeners(field);
                });

                // Character counter for description
                const descriptionField = document.getElementById('course_description');
                if (descriptionField) {
                    descriptionField.addEventListener('input', this.updateCharacterCount.bind(this));
                    this.updateCharacterCount();
                }
            }

            addFieldListeners(field) {
                const events = ['input', 'blur', 'change'];
                events.forEach(event => {
                    field.addEventListener(event, () => {
                        setTimeout(() => this.validateField(field), 100);
                    });
                });
            }

            validateField(field) {
                const rules = field.dataset.validation.split('|');
                const value = field.value;

                let isValid = true;
                let errorMessage = '';

                for (const rule of rules) {
                    const [ruleName, param] = rule.split(':');

                    if (!this.validationRules[ruleName]) continue;

                    const ruleValid = this.validationRules[ruleName](value, param);

                    if (!ruleValid) {
                        isValid = false;
                        errorMessage = this.getErrorMessage(ruleName, param, field);
                        break;
                    }
                }

                this.updateFieldUI(field, isValid, errorMessage);
                return isValid;
            }

            getErrorMessage(ruleName, param, field) {
                const fieldName = field.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || 'Field';

                const messages = {
                    required: `${fieldName} is required`,
                    min: `${fieldName} minimum ${param} characters`,
                    max: `${fieldName} maximum ${param} characters`,
                    numeric: `${fieldName} must be a number`,
                    email: `Invalid email format`,
                    url: `Invalid URL format`
                };

                if (ruleName === 'min' && field.type === 'number') {
                    return `${fieldName} minimum ${param}`;
                }

                return messages[ruleName] || `${fieldName} is invalid`;
            }

            updateFieldUI(field, isValid, errorMessage) {
                const container = field.closest('div');

                // Remove existing validation feedback
                const existingFeedback = container.querySelector('.validation-feedback');
                if (existingFeedback) {
                    existingFeedback.remove();
                }

                // Update field styling
                field.classList.remove('border-red-500', 'border-green-500', 'validation-field');
                field.classList.add('validation-field');

                if (field.value.trim() !== '') {
                    field.classList.add(isValid ? 'valid' : 'invalid');
                }

                // Create enhanced validation feedback
                if (field.value.trim() !== '' || field.dataset.validation.includes('required')) {
                    this.createValidationFeedback(field, isValid, errorMessage, container);
                }
            }

            createValidationFeedback(field, isValid, errorMessage, container) {
                const feedback = document.createElement('div');
                feedback.className = 'validation-feedback';

                if (isValid && field.value.trim() !== '') {
                    feedback.classList.add('success');
                    feedback.innerHTML = `
                        <div class="validation-feedback-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <strong>Great!</strong> This field looks good.
                        </div>
                    `;
                } else if (!isValid && errorMessage) {
                    feedback.classList.add('error');
                    feedback.innerHTML = `
                        <div class="validation-feedback-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <strong>Needs attention:</strong> ${errorMessage}
                        </div>
                    `;
                }

                if (feedback.innerHTML) {
                    container.appendChild(feedback);
                }
            }

            updateCharacterCount() {
                const descriptionField = document.getElementById('course_description');
                const counter = document.getElementById('description-count');

                if (descriptionField && counter) {
                    const count = descriptionField.value.length;
                    counter.textContent = count;

                    const counterContainer = counter.parentElement;
                    counterContainer.classList.remove('text-red-500', 'text-green-600', 'text-gray-500');

                    if (count < 50) {
                        counterContainer.classList.add('text-gray-500');
                    } else if (count > 1000) {
                        counterContainer.classList.add('text-red-500');
                    } else {
                        counterContainer.classList.add('text-green-600');
                    }
                }
            }

            validateForm() {
                const fields = this.form.querySelectorAll('[data-validation]');
                let isFormValid = true;

                fields.forEach(field => {
                    if (!this.validateField(field)) {
                        isFormValid = false;
                    }
                });

                return isFormValid;
            }
        }

        // Initialize validator
        document.addEventListener('DOMContentLoaded', function() {
            const validator = new FormValidator('testForm');

            // Handle form submission
            document.getElementById('testForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const isValid = validator.validateForm();
                const resultsDiv = document.getElementById('test-results');
                const resultsContent = document.getElementById('results-content');

                resultsDiv.classList.remove('hidden');

                if (isValid) {
                    resultsContent.innerHTML = '<p class="text-green-600 font-semibold">✅ All validations passed!</p>';
                } else {
                    resultsContent.innerHTML = '<p class="text-red-600 font-semibold">❌ Some validations failed. Please check the form.</p>';
                }
            });
        });
    </script>
</body>
</html>
