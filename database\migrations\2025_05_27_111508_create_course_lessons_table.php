<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_lessons', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('course_id'); // Foreign key to courses table
            $table->uuid('chapter_id'); // Foreign key to course_chapters table

            // Lesson Information
            $table->string('title'); // Lesson title (e.g., "1.1 Apa itu Programming?")
            $table->string('slug'); // URL-friendly slug
            $table->text('description')->nullable(); // Lesson description
            $table->longText('content')->nullable(); // Lesson text content
            $table->integer('sort_order')->default(0); // Order within chapter
            $table->integer('duration_minutes')->default(0); // Lesson duration

            // Media
            $table->string('video_url')->nullable(); // Video URL (YouTube, Vimeo, etc.)
            $table->string('video_file')->nullable(); // Uploaded video file path
            $table->json('attachments')->nullable(); // Additional files (PDFs, code files, etc.)

            // Lesson Type
            $table->enum('type', ['video', 'text', 'quiz', 'assignment'])->default('video');

            // Quiz/Assignment specific fields
            $table->json('quiz_settings')->nullable(); // Quiz configuration (time limit, attempts, etc.)
            $table->json('assignment_settings')->nullable(); // Assignment configuration (due date, file types, etc.)

            // Status
            $table->boolean('is_published')->default(false);
            $table->boolean('is_free')->default(false); // Whether this lesson is free (for paid courses)
            $table->boolean('is_preview')->default(false); // Whether this is a preview lesson

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('chapter_id')->references('id')->on('course_chapters')->onDelete('cascade');

            // Indexes
            $table->index('course_id');
            $table->index('chapter_id');
            $table->index('sort_order');
            $table->index('is_published');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_lessons');
    }
};
