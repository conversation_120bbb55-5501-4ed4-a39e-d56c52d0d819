@extends('layouts.app')

@section('title', '<PERSON><PERSON>: ' . $exam->title . ' - Ngambis<PERSON>')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON></h1>
                    <p class="text-gray-600">{{ $exam->title }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('exams.show', $exam) }}" class="btn btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <PERSON><PERSON><PERSON> ke <PERSON>
                    </a>
                    @if($attempt->is_passed && $exam->certificate_enabled)
                        @auth
                            @if(auth()->user()->hasActiveMembership())
                                <button class="btn bg-yellow-600 hover:bg-yellow-700 text-white">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                    </svg>
                                    Download Sertifikat
                                </button>
                            @else
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium text-blue-900">NALA Membership Required</div>
                                            <div class="text-xs text-blue-700">Upgrade to Basic or higher to download certificates</div>
                                        </div>
                                    </div>
                                    <a href="{{ route('payment.pricing') }}" class="mt-2 inline-block text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        Upgrade Now
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                                <div class="text-sm text-gray-600 text-center">
                                    <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-700">Login</a> untuk download sertifikat
                                </div>
                            </div>
                        @endauth
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Result Summary -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="text-center mb-6">
                        @if($attempt->is_passed)
                            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h2 class="text-3xl font-bold text-green-600 mb-2">Selamat! Anda Lulus</h2>
                            <p class="text-gray-600">Anda telah berhasil menyelesaikan ujian dengan baik</p>
                        @else
                            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h2 class="text-3xl font-bold text-red-600 mb-2">Belum Lulus</h2>
                            <p class="text-gray-600">Jangan menyerah! Anda masih bisa mencoba lagi</p>
                        @endif
                    </div>

                    <!-- Score Display -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold {{ $attempt->is_passed ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($attempt->score_percentage, 1) }}%
                            </div>
                            <div class="text-sm text-gray-600">Skor Anda</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-blue-600">{{ $attempt->correct_answers }}</div>
                            <div class="text-sm text-gray-600">Benar</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-gray-600">{{ $attempt->answered_questions }}</div>
                            <div class="text-sm text-gray-600">Dijawab</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-purple-600">{{ $exam->passing_score }}%</div>
                            <div class="text-sm text-gray-600">Batas Lulus</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                            <span>Progress Skor</span>
                            <span>{{ number_format($attempt->score_percentage, 1) }}% dari {{ $exam->passing_score }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="h-3 rounded-full transition-all duration-500 {{ $attempt->is_passed ? 'bg-green-500' : 'bg-red-500' }}"
                                 style="width: {{ min($attempt->score_percentage, 100) }}%"></div>
                        </div>
                    </div>

                    <!-- Attempt Info -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Percobaan:</span>
                            <span class="font-medium ml-2">{{ $attempt->attempt_number }} dari {{ $exam->max_attempts }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Waktu:</span>
                            <span class="font-medium ml-2">{{ gmdate('H:i:s', $attempt->time_taken) }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Selesai:</span>
                            <span class="font-medium ml-2">{{ $attempt->completed_at->format('d M Y, H:i') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Question Review -->
                @if($exam->show_results_immediately)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Review Jawaban</h3>

                        <div class="space-y-6">
                            @foreach($attempt->answers as $index => $answer)
                                <div class="border border-gray-200 rounded-lg p-6">
                                    <!-- Question Header -->
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex items-center">
                                            <span class="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                                                {{ $index + 1 }}
                                            </span>
                                            <div>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {{ ucfirst(str_replace('_', ' ', $answer->question->type)) }}
                                                </span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                                    {{ $answer->question->points }} poin
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center">
                                            @if($answer->is_correct)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Benar
                                                </span>
                                            @elseif($answer->is_correct === false)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Salah
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Perlu Review
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Question Text -->
                                    <div class="mb-4">
                                        <h4 class="font-medium text-gray-900 mb-2">{{ $answer->question->question }}</h4>
                                    </div>

                                    <!-- Answer Options -->
                                    @if($answer->question->type === 'multiple_choice' || $answer->question->type === 'true_false')
                                        <div class="space-y-2">
                                            @foreach($answer->question->options as $optionIndex => $option)
                                                <div class="flex items-center p-3 rounded-lg border
                                                    @if($answer->selected_option_id == $option->id)
                                                        @if($option->is_correct)
                                                            border-green-300 bg-green-50
                                                        @else
                                                            border-red-300 bg-red-50
                                                        @endif
                                                    @elseif($option->is_correct)
                                                        border-green-300 bg-green-50
                                                    @else
                                                        border-gray-200
                                                    @endif
                                                ">
                                                    <div class="flex items-center">
                                                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                                                            {{ chr(65 + $optionIndex) }}
                                                        </span>
                                                        <span class="text-gray-900">{{ $option->option_text }}</span>
                                                    </div>
                                                    <div class="ml-auto flex items-center space-x-2">
                                                        @if($answer->selected_option_id == $option->id)
                                                            <span class="text-xs font-medium text-blue-600">Jawaban Anda</span>
                                                        @endif
                                                        @if($option->is_correct)
                                                            <span class="text-xs font-medium text-green-600">Jawaban Benar</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @elseif($answer->question->type === 'short_answer')
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <div class="text-sm text-gray-600 mb-2">Jawaban Anda:</div>
                                            <div class="text-gray-900">{{ $answer->answer_text ?: 'Tidak dijawab' }}</div>
                                        </div>
                                    @endif

                                    <!-- Explanation -->
                                    @if($answer->question->explanation)
                                        <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                                            <div class="text-sm font-medium text-blue-900 mb-1">Penjelasan:</div>
                                            <div class="text-sm text-blue-800">{{ $answer->question->explanation }}</div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Next Steps Card -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-900 mb-4">Langkah Selanjutnya</h3>

                    @if($attempt->is_passed)
                        <div class="space-y-3">
                            @if($exam->certificate_enabled)
                                @auth
                                    @if(auth()->user()->hasActiveMembership())
                                        <button class="w-full btn bg-yellow-600 hover:bg-yellow-700 text-white">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                            </svg>
                                            Download Sertifikat
                                        </button>
                                    @else
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <div class="text-center">
                                                <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                </svg>
                                                <div class="text-sm font-medium text-blue-900 mb-1">NALA Membership Required</div>
                                                <div class="text-xs text-blue-700 mb-3">Upgrade to Basic or higher to download exam certificates</div>
                                                <a href="{{ route('payment.pricing') }}" class="inline-block text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                                    Upgrade Now
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                @else
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                                        <div class="text-sm text-gray-600">
                                            <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-700">Login</a> untuk download sertifikat
                                        </div>
                                    </div>
                                @endauth
                            @endif
                            <a href="{{ route('exams.index') }}" class="w-full btn btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Cari Ujian Lain
                            </a>
                        </div>
                    @else
                        <div class="space-y-3">
                            @if($enrollment->attempts_used < $exam->max_attempts)
                                <a href="{{ route('exams.take', $exam) }}" class="w-full btn bg-blue-600 hover:bg-blue-700 text-white">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Coba Lagi
                                </a>
                                <p class="text-xs text-gray-500 text-center">
                                    Sisa {{ $exam->max_attempts - $enrollment->attempts_used }} percobaan
                                </p>
                            @else
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <p class="text-sm text-gray-600">Anda telah mencapai batas maksimal percobaan</p>
                                </div>
                            @endif
                            <a href="{{ route('exams.index') }}" class="w-full btn btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Cari Ujian Lain
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Exam Info -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-900 mb-4">Info Ujian</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Total Soal</span>
                            <span class="font-medium">{{ $attempt->total_questions }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Batas Waktu</span>
                            <span class="font-medium">{{ $exam->time_limit }} menit</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Waktu Anda</span>
                            <span class="font-medium">{{ gmdate('H:i:s', $attempt->time_taken) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Nilai Lulus</span>
                            <span class="font-medium">{{ $exam->passing_score }}%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Skor Anda</span>
                            <span class="font-medium {{ $attempt->is_passed ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($attempt->score_percentage, 1) }}%
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Performance Stats -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="font-semibold text-gray-900 mb-4">Statistik Performa</h3>
                    <div class="space-y-4">
                        <!-- Accuracy -->
                        <div>
                            <div class="flex items-center justify-between text-sm mb-2">
                                <span class="text-gray-600">Akurasi</span>
                                <span class="font-medium">{{ $attempt->answered_questions > 0 ? number_format(($attempt->correct_answers / $attempt->answered_questions) * 100, 1) : 0 }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $attempt->answered_questions > 0 ? ($attempt->correct_answers / $attempt->answered_questions) * 100 : 0 }}%"></div>
                            </div>
                        </div>

                        <!-- Completion -->
                        <div>
                            <div class="flex items-center justify-between text-sm mb-2">
                                <span class="text-gray-600">Kelengkapan</span>
                                <span class="font-medium">{{ number_format(($attempt->answered_questions / $attempt->total_questions) * 100, 1) }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ ($attempt->answered_questions / $attempt->total_questions) * 100 }}%"></div>
                            </div>
                        </div>

                        <!-- Time Efficiency -->
                        <div>
                            <div class="flex items-center justify-between text-sm mb-2">
                                <span class="text-gray-600">Efisiensi Waktu</span>
                                <span class="font-medium">{{ number_format((($exam->time_limit * 60 - $attempt->time_taken) / ($exam->time_limit * 60)) * 100, 1) }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: {{ (($exam->time_limit * 60 - $attempt->time_taken) / ($exam->time_limit * 60)) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection