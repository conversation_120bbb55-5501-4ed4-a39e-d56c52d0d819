<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MembershipPlan;

class MembershipPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'description' => 'Akses terbatas ke fitur NALA dengan pesan yang dibatasi',
                'type' => 'individual',
                'price' => 0,
                'price_per_user' => null,
                'duration_months' => 1,
                'is_free' => true,
                'nala_prompts' => null, // Limited messages (throttled)
                'has_unlimited_nala' => false,
                'has_ice_full' => false, // Limited
                'has_ai_teaching_assistants_courses' => false, // Limited
                'has_ai_teaching_assistants_tryout' => false,
                'has_free_certifications' => false,
                'has_blog_access' => false,
                'career_path_predictor' => 'basic',
                'has_priority_support' => false,
                'is_team_plan' => false,
                'minimum_users' => 1,
                'maximum_users' => 1,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 1,
                'color' => '#6B7280',
                'features_list' => [
                    'Limited Messages (Throttled)',
                    'Limited Intelligent Course Engine (ICE)',
                    'Limited AI Teaching Assistants (Courses)',
                    'Basic Career Path Predictor'
                ],
            ],
            [
                'name' => 'Basic',
                'slug' => 'basic',
                'description' => 'Akses penuh ke fitur dasar NALA dengan 100 prompts per hari',
                'type' => 'individual',
                'price' => 89000,
                'price_per_user' => null,
                'duration_months' => 1,
                'is_free' => false,
                'nala_prompts' => 100,
                'has_unlimited_nala' => false,
                'has_ice_full' => true,
                'has_ai_teaching_assistants_courses' => true,
                'has_ai_teaching_assistants_tryout' => true,
                'has_free_certifications' => true,
                'has_blog_access' => true,
                'career_path_predictor' => 'basic',
                'has_priority_support' => false,
                'is_team_plan' => false,
                'minimum_users' => 1,
                'maximum_users' => 1,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 2,
                'color' => '#3B82F6',
                'features_list' => [
                    '100 NALA Prompts per day',
                    'Full Intelligent Course Engine (ICE)',
                    'Full AI Teaching Assistants (Courses)',
                    'AI Teaching Assistants (Tryout)',
                    'Free Certifications for Free Courses',
                    'Blog Access & Creation',
                    'Basic Career Path Predictor'
                ],
            ],
            [
                'name' => 'Standard',
                'slug' => 'standard',
                'description' => 'Fitur lengkap dengan 300 prompts per hari dan Career Path Predictor yang ditingkatkan',
                'type' => 'individual',
                'price' => 129000,
                'price_per_user' => null,
                'duration_months' => 1,
                'is_free' => false,
                'nala_prompts' => 300,
                'has_unlimited_nala' => false,
                'has_ice_full' => true,
                'has_ai_teaching_assistants_courses' => true,
                'has_ai_teaching_assistants_tryout' => true,
                'has_free_certifications' => true,
                'has_blog_access' => true,
                'career_path_predictor' => 'enhanced',
                'has_priority_support' => false,
                'is_team_plan' => false,
                'minimum_users' => 1,
                'maximum_users' => 1,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 3,
                'color' => '#10B981',
                'features_list' => [
                    '300 NALA Prompts per day',
                    'Full Intelligent Course Engine (ICE)',
                    'Full AI Teaching Assistants (Courses)',
                    'AI Teaching Assistants (Tryout)',
                    'Free Certifications for Free Courses',
                    'Blog Access & Creation',
                    'Enhanced Career Path Predictor'
                ],
            ],
            [
                'name' => 'Pro',
                'slug' => 'pro',
                'description' => 'Paket premium dengan 400 prompts per hari, Job Board Integration, dan Priority Support',
                'type' => 'individual',
                'price' => 179000,
                'price_per_user' => null,
                'duration_months' => 1,
                'is_free' => false,
                'nala_prompts' => 400,
                'has_unlimited_nala' => false,
                'has_ice_full' => true,
                'has_ai_teaching_assistants_courses' => true,
                'has_ai_teaching_assistants_tryout' => true,
                'has_free_certifications' => true,
                'has_blog_access' => true,
                'career_path_predictor' => 'enhanced_with_job_board',
                'has_priority_support' => true,
                'is_team_plan' => false,
                'minimum_users' => 1,
                'maximum_users' => 1,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 4,
                'color' => '#8B5CF6',
                'features_list' => [
                    '400 NALA Prompts per day',
                    'Full Intelligent Course Engine (ICE)',
                    'Full AI Teaching Assistants (Courses)',
                    'AI Teaching Assistants (Tryout)',
                    'Free Certifications for Free Courses',
                    'Blog Access & Creation',
                    'Enhanced with Job Board Integration',
                    'Priority Support'
                ],
            ],
        ];

        foreach ($plans as $plan) {
            MembershipPlan::create($plan);
        }
    }
}
