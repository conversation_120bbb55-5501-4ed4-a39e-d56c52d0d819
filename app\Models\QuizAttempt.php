<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QuizAttempt extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'quiz_id',
        'attempt_number',
        'started_at',
        'submitted_at',
        'completed_at',
        'time_taken',
        'total_questions',
        'correct_answers',
        'score_percentage',
        'total_points',
        'max_points',
        'status',
        'is_passed',
        'question_order',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
        'time_taken' => 'integer',
        'total_questions' => 'integer',
        'correct_answers' => 'integer',
        'score_percentage' => 'decimal:2',
        'total_points' => 'integer',
        'max_points' => 'integer',
        'attempt_number' => 'integer',
        'is_passed' => 'boolean',
        'question_order' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the quiz attempt.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the quiz that this attempt belongs to.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(LessonQuiz::class, 'quiz_id');
    }

    /**
     * Get the answers for this attempt.
     */
    public function answers(): HasMany
    {
        return $this->hasMany(QuizAnswer::class, 'attempt_id');
    }

    /**
     * Check if the attempt is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if the attempt is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the attempt is submitted.
     */
    public function isSubmitted(): bool
    {
        return $this->status === 'submitted';
    }

    /**
     * Get the score as a percentage.
     */
    public function getScorePercentageAttribute($value): float
    {
        return round($value, 2);
    }

    /**
     * Calculate and update the score.
     */
    public function calculateScore(): void
    {
        $totalQuestions = $this->answers()->count();
        $correctAnswers = $this->answers()->where('is_correct', true)->count();
        $totalPoints = $this->answers()->sum('points_earned');
        $maxPoints = $this->answers()->sum('max_points');

        $this->update([
            'total_questions' => $totalQuestions,
            'correct_answers' => $correctAnswers,
            'total_points' => $totalPoints,
            'max_points' => $maxPoints,
            'score_percentage' => $maxPoints > 0 ? ($totalPoints / $maxPoints) * 100 : 0,
            'is_passed' => $maxPoints > 0 ? (($totalPoints / $maxPoints) * 100) >= $this->quiz->passing_score : false,
        ]);
    }
}
