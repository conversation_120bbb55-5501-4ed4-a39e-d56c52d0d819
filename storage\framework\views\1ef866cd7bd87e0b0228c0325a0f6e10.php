<header class="bg-white shadow-sm border-b sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-3">
                <img src="<?php echo e(asset('images/logo.svg')); ?>" alt="Ngambiskuy Logo" class="w-10 h-10">
                <span class="text-xl font-bold text-gray-900">Ngambiskuy</span>
            </a>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8">
                <a href="<?php echo e(route('courses.index')); ?>" class="text-gray-700 hover:text-primary transition-colors font-medium">Belajar</a>
                <a href="<?php echo e(route('tutor.register.terms')); ?>" class="text-gray-700 hover:text-primary transition-colors font-medium">Mengajar</a>
                <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="text-gray-700 hover:text-primary transition-colors font-medium">Coba Gratis</a>
                <a href="<?php echo e(route('blog.index')); ?>" class="text-gray-700 hover:text-primary transition-colors font-medium">Blog</a>
            </nav>

            <!-- Right side buttons -->
            <div class="hidden md:flex items-center space-x-4">
                <?php if(auth()->guard()->check()): ?>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-700">Halo, <?php echo e(Auth::user()->name); ?></span>

                        <?php if(Auth::user()->isTutor()): ?>
                            <a href="<?php echo e(route('tutor.dashboard')); ?>" class="btn btn-primary">Dashboard Tutor</a>
                        <?php elseif(Auth::user()->hasTutorProfile()): ?>
                            <a href="<?php echo e(route('tutor.register.status')); ?>" class="btn btn-outline">Status Aplikasi</a>
                            <a href="<?php echo e(route('user.dashboard')); ?>" class="btn btn-primary">Dashboard</a>
                        <?php else: ?>
                            <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline">Jadi Pengajar</a>
                            <a href="<?php echo e(route('user.dashboard')); ?>" class="btn btn-primary">Dashboard</a>
                        <?php endif; ?>

                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-ghost">Keluar</button>
                        </form>
                    </div>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-ghost">Masuk</a>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-primary">Daftar</a>
                    <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline">Jadi Pengajar</a>
                <?php endif; ?>
            </div>

            <!-- Mobile menu button -->
            <button class="md:hidden mobile-menu-toggle" type="button">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden mobile-menu hidden py-4 border-t">
            <div class="flex flex-col space-y-4">
                <a href="<?php echo e(route('courses.index')); ?>" class="text-gray-700 hover:text-primary font-medium">Belajar</a>
                <a href="<?php echo e(route('tutor.register.terms')); ?>" class="text-gray-700 hover:text-primary font-medium">Mengajar</a>
                <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="text-gray-700 hover:text-primary font-medium">Coba Gratis</a>
                <a href="<?php echo e(route('blog.index')); ?>" class="text-gray-700 hover:text-primary font-medium">Blog</a>
                <div class="flex flex-col space-y-2 pt-4 border-t">
                    <?php if(auth()->guard()->check()): ?>
                        <span class="text-gray-700 px-3 py-2">Halo, <?php echo e(Auth::user()->name); ?></span>

                        <?php if(Auth::user()->isTutor()): ?>
                            <a href="<?php echo e(route('tutor.dashboard')); ?>" class="btn btn-primary w-full">Dashboard Tutor</a>
                        <?php elseif(Auth::user()->hasTutorProfile()): ?>
                            <a href="<?php echo e(route('tutor.register.status')); ?>" class="btn btn-outline w-full">Status Aplikasi</a>
                            <a href="<?php echo e(route('user.dashboard')); ?>" class="btn btn-primary w-full">Dashboard</a>
                        <?php else: ?>
                            <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline w-full">Jadi Pengajar</a>
                            <a href="<?php echo e(route('user.dashboard')); ?>" class="btn btn-primary w-full">Dashboard</a>
                        <?php endif; ?>

                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-ghost w-full justify-start">Keluar</button>
                        </form>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="btn btn-ghost w-full justify-start">Masuk</a>
                        <a href="<?php echo e(route('register')); ?>" class="btn btn-primary w-full">Daftar</a>
                        <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline w-full">Jadi Pengajar</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/partials/header.blade.php ENDPATH**/ ?>