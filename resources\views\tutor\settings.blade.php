@extends('layouts.tutor')

@section('title', '<PERSON>gat<PERSON><PERSON> - <PERSON>')

@section('content')
<div class="p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold text-gray-900">Pen<PERSON><PERSON><PERSON></h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Settings Hub
                    </span>
                </div>
                <p class="text-gray-600 mt-1">Kelola preferensi dan pengaturan akun tutor Anda</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('tutor.dashboard') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="space-y-6">
        <!-- Profile Picture Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Foto Profil</h2>

                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0">
                        @if($user->profile_picture)
                            <img src="{{ $user->getProfilePictureUrl() }}" alt="Profile" class="w-32 h-32 rounded-full object-cover border-4 border-gray-200">
                        @else
                            <div class="w-32 h-32 bg-gradient-to-br from-primary to-orange-500 rounded-full flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>

                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Foto Profil Tutor</h3>
                        <p class="text-sm text-gray-600 mb-4">
                            Foto profil yang baik akan meningkatkan kepercayaan siswa. Gunakan foto yang jelas dan profesional.
                            <br>Format yang didukung: JPG, PNG. Maksimal 2MB.
                        </p>

                        <!-- Profile Picture Upload Form -->
                        <form action="{{ route('tutor.profile.update') }}" method="POST" enctype="multipart/form-data" class="space-y-3">
                            @csrf
                            @method('PUT')

                            @if(session('profile_success'))
                                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                                    {{ session('profile_success') }}
                                </div>
                            @endif

                            @if($errors->has('profile_picture'))
                                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                                    {{ $errors->first('profile_picture') }}
                                </div>
                            @endif

                            <div class="flex space-x-3">
                                <input type="file" id="tutor_profile_picture" name="profile_picture" accept="image/jpeg,image/png,image/jpg" class="hidden" onchange="this.form.submit()">
                                <label for="tutor_profile_picture" class="btn bg-emerald-600 hover:bg-emerald-700 text-white cursor-pointer">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ $user->profile_picture ? 'Ganti Foto' : 'Upload Foto' }}
                                </label>

                                @if($user->profile_picture)
                                    <button type="submit" name="delete_profile_picture" value="1" class="btn border-red-300 text-red-600 hover:bg-red-50">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus Foto
                                    </button>
                                @endif
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        <!-- Tutor Profile Settings -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Profil Tutor</h2>

                <form action="{{ route('tutor.profile.update') }}" method="POST" class="space-y-6">
                    @csrf
                    @method('PUT')

                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">Nama Tampilan</label>
                            <input type="text" id="display_name" name="display_name" value="{{ $user->name }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        </div>
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Gelar/Title</label>
                            <input type="text" id="title" name="title" placeholder="Contoh: Senior Developer, UI/UX Expert"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        </div>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Singkat</label>
                        <textarea id="description" name="description" rows="4"
                                  placeholder="Deskripsi singkat yang akan muncul di kartu profil dan hasil pencarian..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">{{ Auth::user()->tutorProfile->description ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                    </div>

                    <div>
                        <label for="long_description" class="block text-sm font-medium text-gray-700 mb-2">Tentang Saya (Deskripsi Lengkap)</label>
                        <textarea id="long_description" name="long_description" rows="8"
                                  placeholder="Ceritakan secara detail tentang latar belakang, pengalaman mengajar, metodologi pembelajaran, keahlian khusus, dan hal-hal yang membuat Anda unik sebagai pengajar..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">{{ Auth::user()->tutorProfile->long_description ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Deskripsi lengkap untuk halaman profil publik Anda. Maksimal 10.000 karakter</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="experience_years" class="block text-sm font-medium text-gray-700 mb-2">Pengalaman (Tahun)</label>
                            <select id="experience_years" name="experience_years"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="">Pilih pengalaman</option>
                                <option value="1-2">1-2 tahun</option>
                                <option value="3-5">3-5 tahun</option>
                                <option value="6-10">6-10 tahun</option>
                                <option value="10+">10+ tahun</option>
                            </select>
                        </div>
                        <div>
                            <label for="specialization" class="block text-sm font-medium text-gray-700 mb-2">Spesialisasi</label>
                            <select id="specialization" name="specialization"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="">Pilih spesialisasi</option>
                                <option value="programming">Programming</option>
                                <option value="design">Design</option>
                                <option value="ai">AI & Machine Learning</option>
                                <option value="data">Data Science</option>
                                <option value="mobile">Mobile Development</option>
                                <option value="business">Business</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="social_links" class="block text-sm font-medium text-gray-700 mb-2">Social Media</label>
                        <div class="space-y-3">
                            <input type="url" placeholder="LinkedIn Profile"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <input type="url" placeholder="GitHub Profile"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <input type="url" placeholder="Portfolio Website"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Save Profile Button -->
                    <div class="flex justify-end space-x-4">
                        <button type="button" class="btn border-gray-300 text-gray-600 hover:bg-gray-50">
                            Batal
                        </button>
                        <button type="submit" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Profil
                        </button>
                    </div>
                </form>
            </div>

        <!-- Course Settings -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Pengaturan Kursus</h2>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Kategori Kursus Default</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="default_categories[]" value="programming" class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                                <span class="ml-2 text-sm text-gray-700">Programming</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="default_categories[]" value="design" class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                                <span class="ml-2 text-sm text-gray-700">Design</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="default_categories[]" value="ai" class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                                <span class="ml-2 text-sm text-gray-700">AI/ML</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="default_categories[]" value="data" class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                                <span class="ml-2 text-sm text-gray-700">Data Science</span>
                            </label>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="default_price_range" class="block text-sm font-medium text-gray-700 mb-2">Range Harga Default</label>
                            <select id="default_price_range" name="default_price_range"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="">Pilih range harga</option>
                                <option value="free">Gratis</option>
                                <option value="100000-300000">Rp 100.000 - 300.000</option>
                                <option value="300000-500000">Rp 300.000 - 500.000</option>
                                <option value="500000+">Rp 500.000+</option>
                            </select>
                        </div>
                        <div>
                            <label for="course_language" class="block text-sm font-medium text-gray-700 mb-2">Bahasa Kursus Default</label>
                            <select id="course_language" name="course_language"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="id">Bahasa Indonesia</option>
                                <option value="en">English</option>
                                <option value="both">Bilingual</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Notifikasi Tutor</h2>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Enrollment Baru</h3>
                            <p class="text-sm text-gray-600">Notifikasi ketika ada siswa baru mendaftar</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Pertanyaan Siswa</h3>
                            <p class="text-sm text-gray-600">Notifikasi untuk Q&A dan diskusi kursus</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Review & Rating</h3>
                            <p class="text-sm text-gray-600">Notifikasi untuk review dan rating kursus</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Laporan Bulanan</h3>
                            <p class="text-sm text-gray-600">Ringkasan performa dan penghasilan bulanan</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>
                </div>
            </div>

        <!-- AI Assistant Settings -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 mb-6">AI Assistant</h2>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">AI Course Builder</h3>
                            <p class="text-sm text-gray-600">Gunakan AI untuk membantu membuat kurikulum kursus</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Smart Pricing</h3>
                            <p class="text-sm text-gray-600">Saran harga optimal berdasarkan analisis pasar</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Content Suggestions</h3>
                            <p class="text-sm text-gray-600">Saran konten dan materi pembelajaran</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-500"></div>
                        </label>
                    </div>
                </div>
            </div>

        <!-- Account Security -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Keamanan Akun</h2>

            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Password</h3>
                        <p class="text-sm text-gray-600">Terakhir diubah {{ $user->updated_at->format('d M Y') }}</p>
                    </div>
                    <button class="btn border-gray-300 text-gray-600 hover:bg-gray-50">
                        Ubah Password
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Two-Factor Authentication</h3>
                        <p class="text-sm text-gray-600">Tambahkan lapisan keamanan ekstra</p>
                    </div>
                    <button class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50">
                        Aktifkan 2FA
                    </button>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button class="btn bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg hover:shadow-xl">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Simpan Pengaturan
            </button>
        </div>
        </div>
    </div>
</div>
@endsection
