<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use App\Http\Controllers\Nala\ChatController;

class NalaAIController extends Controller
{
    /**
     * Handle Nala AI chat requests - Delegate to ChatController
     */
    public function chat(Request $request)
    {
        return app(ChatController::class)->chat($request);
    }

    /**
     * Get user's chat history
     */
    public function getChatHistory(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $conversations = NalaChatConversation::forUser(auth()->id())
            ->active()
            ->with(['messages' => function ($query) {
                $query->active()->orderBy('created_at', 'desc')->limit(1);
            }])
            ->orderBy('last_message_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'conversations' => $conversations
        ]);
    }

    /**
     * Get specific conversation with messages
     */
    public function getConversation(Request $request, $conversationId)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $conversation = NalaChatConversation::forUser(auth()->id())
            ->with(['messages' => function ($query) {
                $query->active()->orderBy('created_at', 'asc');
            }])
            ->find($conversationId);

        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'conversation' => $conversation
        ]);
    }

    /**
     * Delete conversation
     */
    public function deleteConversation(Request $request, $conversationId)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $conversation = NalaChatConversation::forUser(auth()->id())->find($conversationId);

        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found'
            ], 404);
        }

        $conversation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Conversation deleted successfully'
        ]);
    }

    /**
     * Clear all chat history for user
     */
    public function clearChatHistory(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        NalaChatConversation::forUser(auth()->id())->delete();

        return response()->json([
            'success' => true,
            'message' => 'Chat history cleared successfully'
        ]);
    }

    /**
     * Auto-update user profile from conversation
     */
    public function updateProfileFromConversation(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $request->validate([
            'field' => 'required|string',
            'value' => 'required',
            'conversation_id' => 'nullable|string|exists:nala_chat_conversations,id'
        ]);

        $user = auth()->user();
        $field = $request->input('field');
        $value = $request->input('value');

        // Map conversation fields to database fields
        $fieldMapping = [
            'job_title' => 'job_title',
            'company' => 'company',
            'experience_years' => 'experience_years',
            'skills' => 'skills',
            'learning_interests' => 'minat_belajar',
            'career_goals' => 'career_goals',
            'industry_interests' => 'industry_interests',
            'salary_expectations' => 'salary_expectations',
            'work_preferences' => 'work_preferences',
            'education_level' => 'pendidikan',
            'location' => 'location',
        ];

        if (!isset($fieldMapping[$field])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid field'
            ], 400);
        }

        $dbField = $fieldMapping[$field];

        // Handle JSON fields - Laravel will auto-cast these based on model casts
        $jsonFields = ['skills', 'minat_belajar', 'career_goals', 'industry_interests', 'salary_expectations', 'work_preferences'];
        if (in_array($dbField, $jsonFields)) {
            $value = is_array($value) ? $value : [$value];
        }

        // Update user profile using DB query builder
        DB::table('users')
            ->where('id', $user->id)
            ->update([$dbField => $value]);

        // Log the profile update in conversation metadata
        $conversation = NalaChatConversation::find($request->input('conversation_id'));
        if ($conversation && $conversation->user_id === $user->id) {
            $metadata = $conversation->context_data ?? [];
            $metadata['profile_updates'] = $metadata['profile_updates'] ?? [];
            $metadata['profile_updates'][] = [
                'field' => $field,
                'value' => $value,
                'updated_at' => now()->toISOString()
            ];
            $conversation->update(['context_data' => $metadata]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'field' => $field,
            'value' => $value
        ]);
    }
}