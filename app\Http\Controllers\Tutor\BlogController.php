<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display a listing of the tutor's blog posts.
     */
    public function index()
    {
        $user = Auth::user();

        $blogPosts = BlogPost::with(['category'])
            ->where('author_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $stats = [
            'total_posts' => BlogPost::where('author_id', $user->id)->count(),
            'published_posts' => BlogPost::where('author_id', $user->id)->published()->count(),
            'draft_posts' => BlogPost::where('author_id', $user->id)->where('status', 'draft')->count(),
            'total_views' => BlogPost::where('author_id', $user->id)->sum('views_count'),
        ];

        return view('tutor.blogs.index', compact('blogPosts', 'stats'));
    }

    /**
     * Show the form for creating a new blog post.
     */
    public function create()
    {
        $categories = Category::orderBy('name')->get();
        return view('tutor.blogs.create', compact('categories'));
    }

    /**
     * Store a newly created blog post in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
            'is_featured' => 'boolean',
            'status' => 'required|in:draft,published',
        ]);

        $user = Auth::user();

        // Handle featured image upload
        $featuredImagePath = null;
        if ($request->hasFile('featured_image')) {
            $featuredImagePath = $request->file('featured_image')->store('blog-images', 'public');
        }

        // Process tags
        $tags = null;
        if ($request->filled('tags')) {
            $tags = array_map('trim', explode(',', $request->tags));
        }

        // Create blog post
        $blogPost = BlogPost::create([
            'author_id' => $user->id,
            'category_id' => $validated['category_id'],
            'title' => $validated['title'],
            'slug' => Str::slug($validated['title']),
            'excerpt' => $validated['excerpt'],
            'content' => $validated['content'],
            'featured_image' => $featuredImagePath,
            'meta_title' => $validated['meta_title'] ?? $validated['title'],
            'meta_description' => $validated['meta_description'] ?? $validated['excerpt'],
            'tags' => $tags,
            'status' => $validated['status'],
            'is_featured' => $request->boolean('is_featured'),
            'published_at' => $validated['status'] === 'published' ? now() : null,
        ]);

        $message = $validated['status'] === 'published' 
            ? 'Blog post berhasil dibuat dan dipublikasikan!' 
            : 'Blog post berhasil disimpan sebagai draft.';

        return redirect()->route('tutor.blogs.show', $blogPost)->with('success', $message);
    }

    /**
     * Display the specified blog post.
     */
    public function show(BlogPost $blogPost)
    {
        // Ensure the blog post belongs to the authenticated tutor
        if ($blogPost->author_id !== Auth::id()) {
            abort(403, 'Unauthorized access to blog post.');
        }

        $blogPost->load(['category']);

        return view('tutor.blogs.show', compact('blogPost'));
    }

    /**
     * Show the form for editing the specified blog post.
     */
    public function edit(BlogPost $blogPost)
    {
        // Ensure the blog post belongs to the authenticated tutor
        if ($blogPost->author_id !== Auth::id()) {
            abort(403, 'Unauthorized access to blog post.');
        }

        $categories = Category::orderBy('name')->get();
        
        return view('tutor.blogs.edit', compact('blogPost', 'categories'));
    }

    /**
     * Update the specified blog post in storage.
     */
    public function update(Request $request, BlogPost $blogPost)
    {
        // Ensure the blog post belongs to the authenticated tutor
        if ($blogPost->author_id !== Auth::id()) {
            abort(403, 'Unauthorized access to blog post.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
            'is_featured' => 'boolean',
            'status' => 'required|in:draft,published',
        ]);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($blogPost->featured_image) {
                \Storage::disk('public')->delete($blogPost->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')->store('blog-images', 'public');
        }

        // Process tags
        if ($request->filled('tags')) {
            $validated['tags'] = array_map('trim', explode(',', $request->tags));
        } else {
            $validated['tags'] = null;
        }

        // Update slug if title changed
        if ($validated['title'] !== $blogPost->title) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set published_at if status changed to published
        if ($validated['status'] === 'published' && $blogPost->status !== 'published') {
            $validated['published_at'] = now();
        } elseif ($validated['status'] !== 'published') {
            $validated['published_at'] = null;
        }

        $validated['is_featured'] = $request->boolean('is_featured');
        $validated['meta_title'] = $validated['meta_title'] ?? $validated['title'];
        $validated['meta_description'] = $validated['meta_description'] ?? $validated['excerpt'];

        $blogPost->update($validated);

        $message = $validated['status'] === 'published' 
            ? 'Blog post berhasil diperbarui dan dipublikasikan!' 
            : 'Blog post berhasil diperbarui sebagai draft.';

        return redirect()->route('tutor.blogs.show', $blogPost)->with('success', $message);
    }

    /**
     * Remove the specified blog post from storage.
     */
    public function destroy(BlogPost $blogPost)
    {
        // Ensure the blog post belongs to the authenticated tutor
        if ($blogPost->author_id !== Auth::id()) {
            abort(403, 'Unauthorized access to blog post.');
        }

        // Delete featured image if exists
        if ($blogPost->featured_image) {
            \Storage::disk('public')->delete($blogPost->featured_image);
        }

        $blogPost->delete();

        return redirect()->route('tutor.blogs')->with('success', 'Blog post berhasil dihapus.');
    }

    /**
     * Toggle the publish status of the blog post.
     */
    public function togglePublish(BlogPost $blogPost)
    {
        // Ensure the blog post belongs to the authenticated tutor
        if ($blogPost->author_id !== Auth::id()) {
            abort(403, 'Unauthorized access to blog post.');
        }

        $newStatus = $blogPost->status === 'published' ? 'draft' : 'published';
        $publishedAt = $newStatus === 'published' ? now() : null;

        $blogPost->update([
            'status' => $newStatus,
            'published_at' => $publishedAt,
        ]);

        $message = $newStatus === 'published'
            ? 'Blog post berhasil dipublikasikan!'
            : 'Blog post berhasil disembunyikan dari publik.';

        return back()->with('success', $message);
    }
}
