<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\LessonProgress;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use Carbon\Carbon;

class CertificateTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating certificate test data...');

        // Get test users (students)
        $students = User::where('is_tutor', false)
            ->where('is_admin', false)
            ->limit(3)
            ->get();

        if ($students->isEmpty()) {
            $this->command->warn('No student users found. Please run UserSeeder first.');
            return;
        }

        // Get published courses with chapters and lessons
        $courses = Course::with(['chapters.lessons'])
            ->where('status', 'published')
            ->limit(3)
            ->get();

        if ($courses->isEmpty()) {
            $this->command->warn('No published courses found. Please run CourseSeeder first.');
            return;
        }

        foreach ($students as $student) {
            foreach ($courses as $course) {
                // Create course enrollment
                $enrollment = CourseEnrollment::firstOrCreate([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                ], [
                    'status' => 'active',
                    'amount_paid' => $course->is_free ? 0 : $course->price,
                    'payment_method' => $course->is_free ? null : 'bank_transfer',
                    'payment_reference' => $course->is_free ? null : 'TEST-' . strtoupper(substr($student->id, 0, 8)),
                    'enrolled_at' => Carbon::now()->subDays(rand(7, 30)),
                ]);

                // Get all lessons for this course
                $lessons = $course->chapters->flatMap(function ($chapter) {
                    return $chapter->lessons;
                });

                if ($lessons->isEmpty()) {
                    continue;
                }

                // For the first student, complete all lessons (100% - eligible for certificate)
                if ($student === $students->first()) {
                    foreach ($lessons as $lesson) {
                        LessonProgress::firstOrCreate([
                            'user_id' => $student->id,
                            'lesson_id' => $lesson->id,
                        ], [
                            'status' => 'completed',
                            'progress_percentage' => 100,
                            'started_at' => Carbon::now()->subDays(rand(5, 20)),
                            'completed_at' => Carbon::now()->subDays(rand(1, 5)),
                            'last_accessed_at' => Carbon::now()->subDays(rand(0, 3)),
                            'video_progress_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'video_duration_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'time_spent_seconds' => rand(300, 1800), // 5-30 minutes
                            'visit_count' => rand(1, 5),
                        ]);
                    }
                    $this->command->info("✅ {$student->name} completed course: {$course->title} (Certificate eligible)");
                }
                // For the second student, complete 80% of lessons (not eligible for certificate)
                elseif ($student === $students->skip(1)->first()) {
                    $lessonsToComplete = $lessons->take(ceil($lessons->count() * 0.8));
                    foreach ($lessonsToComplete as $lesson) {
                        LessonProgress::firstOrCreate([
                            'user_id' => $student->id,
                            'lesson_id' => $lesson->id,
                        ], [
                            'status' => 'completed',
                            'progress_percentage' => 100,
                            'started_at' => Carbon::now()->subDays(rand(5, 20)),
                            'completed_at' => Carbon::now()->subDays(rand(1, 5)),
                            'last_accessed_at' => Carbon::now()->subDays(rand(0, 3)),
                            'video_progress_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'video_duration_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'time_spent_seconds' => rand(300, 1800),
                            'visit_count' => rand(1, 5),
                        ]);
                    }
                    $this->command->info("⏳ {$student->name} completed 80% of course: {$course->title} (In progress)");
                }
                // For the third student, complete 50% of lessons (in progress)
                else {
                    $lessonsToComplete = $lessons->take(ceil($lessons->count() * 0.5));
                    foreach ($lessonsToComplete as $lesson) {
                        LessonProgress::firstOrCreate([
                            'user_id' => $student->id,
                            'lesson_id' => $lesson->id,
                        ], [
                            'status' => 'completed',
                            'progress_percentage' => 100,
                            'started_at' => Carbon::now()->subDays(rand(5, 20)),
                            'completed_at' => Carbon::now()->subDays(rand(1, 5)),
                            'last_accessed_at' => Carbon::now()->subDays(rand(0, 3)),
                            'video_progress_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'video_duration_seconds' => $lesson->duration_minutes ? $lesson->duration_minutes * 60 : 300,
                            'time_spent_seconds' => rand(300, 1800),
                            'visit_count' => rand(1, 5),
                        ]);
                    }
                    $this->command->info("📚 {$student->name} completed 50% of course: {$course->title} (In progress)");
                }
            }
        }

        $this->command->info('✨ Certificate test data created successfully!');
        $this->command->info('');
        $this->command->info('Test scenarios created:');
        $this->command->info('1. First student: 100% completion (can download certificates)');
        $this->command->info('2. Second student: 80% completion (cannot download certificates)');
        $this->command->info('3. Third student: 50% completion (cannot download certificates)');
        $this->command->info('');
        $this->command->info('Login with any student account to test:');
        foreach ($students as $student) {
            $this->command->info("- {$student->email} (password: password123)");
        }
    }
}
