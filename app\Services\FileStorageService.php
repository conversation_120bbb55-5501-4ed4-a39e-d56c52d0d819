<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileStorageService
{
    /**
     * Store a public file for a user (profile pictures, etc.)
     */
    public static function storePublicUserFile(UploadedFile $file, string $userId, string $folder = 'profile'): string
    {
        $directory = "user/{$userId}/{$folder}";
        $filename = self::generateUniqueFilename($file);
        
        // Ensure directory exists
        Storage::disk('public')->makeDirectory($directory);
        
        return $file->storeAs($directory, $filename, 'public');
    }

    /**
     * Store a private file for a tutor (KTP, NPWP, portfolio, etc.)
     */
    public static function storePrivateTutorFile(UploadedFile $file, string $tutorId, string $folder): string
    {
        $directory = "tutor/{$tutorId}/{$folder}";
        $filename = self::generateUniqueFilename($file);
        
        // Ensure directory exists
        Storage::disk('local')->makeDirectory($directory);
        
        return $file->storeAs($directory, $filename, 'local');
    }

    /**
     * Delete a public file
     */
    public static function deletePublicFile(?string $path): bool
    {
        if (!$path) {
            return true;
        }

        return Storage::disk('public')->delete($path);
    }

    /**
     * Delete a private file
     */
    public static function deletePrivateFile(?string $path): bool
    {
        if (!$path) {
            return true;
        }

        return Storage::disk('local')->delete($path);
    }

    /**
     * Get public file URL
     */
    public static function getPublicFileUrl(?string $path): ?string
    {
        if (!$path) {
            return null;
        }

        return Storage::disk('public')->url($path);
    }

    /**
     * Check if private file exists
     */
    public static function privateFileExists(?string $path): bool
    {
        if (!$path) {
            return false;
        }

        return Storage::disk('local')->exists($path);
    }

    /**
     * Generate unique filename with timestamp and random string
     */
    private static function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return "{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get file size in human readable format
     */
    public static function getFileSize(?string $path, string $disk = 'public'): ?string
    {
        if (!$path || !Storage::disk($disk)->exists($path)) {
            return null;
        }

        $bytes = Storage::disk($disk)->size($path);
        
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Create storage directories if they don't exist
     */
    public static function ensureDirectoriesExist(): void
    {
        // Create base directories for public files
        Storage::disk('public')->makeDirectory('user');

        // Create base directories for private files
        Storage::disk('local')->makeDirectory('tutor');
        Storage::disk('local')->makeDirectory('private');

        // Create common subdirectories
        $commonDirs = ['ktp', 'portfolio', 'npwp', 'documents'];
        foreach ($commonDirs as $dir) {
            Storage::disk('local')->makeDirectory("tutor/{$dir}");
        }
    }

    /**
     * Get private file content for authorized access
     */
    public static function getPrivateFileContent(string $path): ?string
    {
        if (!$path || !Storage::disk('local')->exists($path)) {
            return null;
        }

        return Storage::disk('local')->get($path);
    }

    /**
     * Get private file stream for download
     */
    public static function getPrivateFileStream(string $path)
    {
        if (!$path || !Storage::disk('local')->exists($path)) {
            return null;
        }

        return Storage::disk('local')->readStream($path);
    }

    /**
     * Get file MIME type
     */
    public static function getFileMimeType(string $path, string $disk = 'local'): ?string
    {
        if (!$path || !Storage::disk($disk)->exists($path)) {
            return null;
        }

        $extension = pathinfo($path, PATHINFO_EXTENSION);

        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        return $mimeTypes[strtolower($extension)] ?? 'application/octet-stream';
    }

    /**
     * Clean up old files (for maintenance)
     */
    public static function cleanupOldFiles(int $daysOld = 30): array
    {
        $deleted = [];
        $cutoffDate = now()->subDays($daysOld);

        // This is a placeholder for cleanup logic
        // In a real implementation, you'd track file creation dates
        // and remove files that are no longer referenced in the database
        
        return $deleted;
    }
}
