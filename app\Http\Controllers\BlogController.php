<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\SavedArticle;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display a listing of published blog posts.
     */
    public function index(Request $request)
    {
        $query = BlogPost::with(['author', 'category'])
            ->published()
            ->orderBy('published_at', 'desc');

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $blogPosts = $query->paginate(12);
        $categories = Category::whereHas('blogPosts', function ($q) {
            $q->published();
        })->get();

        $featuredPost = BlogPost::with(['author', 'category'])
            ->published()
            ->featured()
            ->latest('published_at')
            ->first();

        return view('blog.index', compact('blogPosts', 'categories', 'featuredPost'));
    }

    /**
     * Display the specified blog post.
     */
    public function show(BlogPost $blogPost)
    {
        // Check if the post is published
        if (!$blogPost->isPublished()) {
            abort(404, 'Blog post tidak ditemukan atau belum dipublikasikan.');
        }

        // Increment views count
        $blogPost->incrementViews();

        // Load relationships
        $blogPost->load(['author', 'category']);

        // Get related posts
        $relatedPosts = BlogPost::with(['author', 'category'])
            ->published()
            ->where('id', '!=', $blogPost->id)
            ->where(function ($query) use ($blogPost) {
                if ($blogPost->category_id) {
                    $query->where('category_id', $blogPost->category_id);
                }
            })
            ->latest('published_at')
            ->limit(3)
            ->get();

        // Check if article is saved by current user
        $isSaved = false;
        if (auth()->check()) {
            $isSaved = SavedArticle::where('user_id', auth()->id())
                ->where('blog_post_id', $blogPost->id)
                ->exists();
        }

        return view('blog.show', compact('blogPost', 'relatedPosts', 'isSaved'));
    }

    /**
     * Display a listing of blog posts by category.
     */
    public function category(Category $category, Request $request)
    {
        $query = BlogPost::with(['author', 'category'])
            ->published()
            ->where('category_id', $category->id)
            ->orderBy('published_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $blogPosts = $query->paginate(12);
        $categories = Category::whereHas('blogPosts', function ($q) {
            $q->published();
        })->get();

        return view('blog.category', compact('blogPosts', 'categories', 'category'));
    }
}
