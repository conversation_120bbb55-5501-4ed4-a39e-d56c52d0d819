<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AssignmentSubmission extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'assignment_id',
        'submission_text',
        'submitted_files',
        'submitted_at',
        'is_late',
        'days_late',
        'score',
        'max_score',
        'score_percentage',
        'feedback',
        'graded_by',
        'graded_at',
        'status',
        'is_passed',
        'original_submission_id',
        'allow_resubmission',
        'resubmission_deadline',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'submitted_files' => 'array',
        'submitted_at' => 'datetime',
        'graded_at' => 'datetime',
        'resubmission_deadline' => 'datetime',
        'is_late' => 'boolean',
        'days_late' => 'integer',
        'score' => 'integer',
        'max_score' => 'integer',
        'score_percentage' => 'decimal:2',
        'is_passed' => 'boolean',
        'allow_resubmission' => 'boolean',
    ];

    /**
     * Get the user that owns the submission.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the assignment that this submission belongs to.
     */
    public function assignment(): BelongsTo
    {
        return $this->belongsTo(LessonAssignment::class, 'assignment_id');
    }

    /**
     * Get the user who graded this submission.
     */
    public function gradedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    /**
     * Get the original submission (if this is a resubmission).
     */
    public function originalSubmission(): BelongsTo
    {
        return $this->belongsTo(AssignmentSubmission::class, 'original_submission_id');
    }

    /**
     * Check if this submission is late.
     */
    public function checkIfLate(): void
    {
        $assignment = $this->assignment;
        
        if ($assignment->due_date && $this->submitted_at > $assignment->due_date) {
            $daysLate = $this->submitted_at->diffInDays($assignment->due_date);
            
            $this->update([
                'is_late' => true,
                'days_late' => $daysLate,
            ]);
        }
    }

    /**
     * Calculate the final score with late penalty.
     */
    public function calculateFinalScore(): int
    {
        if (!$this->score) {
            return 0;
        }

        $finalScore = $this->score;

        // Apply late penalty if applicable
        if ($this->is_late && $this->assignment->late_penalty_percent > 0) {
            $penalty = ($this->assignment->late_penalty_percent / 100) * $this->days_late;
            $finalScore = $this->score * (1 - min($penalty, 1)); // Max penalty is 100%
        }

        return max(0, round($finalScore));
    }

    /**
     * Check if the submission is graded.
     */
    public function isGraded(): bool
    {
        return $this->status === 'graded';
    }

    /**
     * Check if the submission is pending grading.
     */
    public function isPendingGrading(): bool
    {
        return $this->status === 'submitted';
    }

    /**
     * Check if resubmission is allowed.
     */
    public function canResubmit(): bool
    {
        return $this->allow_resubmission && 
               (!$this->resubmission_deadline || Carbon::now() <= $this->resubmission_deadline);
    }

    /**
     * Get the status in Indonesian.
     */
    public function getStatusIndonesianAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'Dikumpulkan',
            'graded' => 'Dinilai',
            'returned' => 'Dikembalikan',
            'resubmitted' => 'Dikumpulkan Ulang',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get the submitted files with full URLs.
     */
    public function getSubmittedFilesUrlsAttribute(): array
    {
        if (!$this->submitted_files) {
            return [];
        }

        return array_map(function ($file) {
            return asset('storage/' . $file);
        }, $this->submitted_files);
    }

    /**
     * Grade the submission.
     */
    public function grade(int $score, string $feedback = null, User $gradedBy = null): void
    {
        $scorePercentage = $this->max_score > 0 ? ($score / $this->max_score) * 100 : 0;
        
        $this->update([
            'score' => $score,
            'score_percentage' => $scorePercentage,
            'feedback' => $feedback,
            'graded_by' => $gradedBy?->id,
            'graded_at' => Carbon::now(),
            'status' => 'graded',
            'is_passed' => $scorePercentage >= 60, // Default passing score
        ]);
    }
}
