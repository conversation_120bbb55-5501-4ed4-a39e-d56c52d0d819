<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TutorController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'is.tutor']);
    }

    /**
     * Show the tutor dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Get real data from database
        $totalCourses = \App\Models\Course::where('tutor_id', $user->id)->count();
        $publishedCourses = \App\Models\Course::where('tutor_id', $user->id)
            ->where('status', 'published')
            ->count();

        // Get active students (students enrolled in tutor's courses)
        $activeStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->distinct('user_id')->count('user_id');

        // Get course ratings average
        $courseRatings = \App\Models\Course::where('tutor_id', $user->id)
            ->where('status', 'published')
            ->avg('average_rating') ?: 0;

        // Calculate monthly earnings (mock for now - will be implemented with payment system)
        $monthlyEarnings = 0; // TODO: Implement with real payment data

        // Calculate total revenue (mock for now)
        $totalRevenue = 0; // TODO: Implement with real payment data

        $stats = [
            'total_courses' => $totalCourses,
            'published_courses' => $publishedCourses,
            'active_students' => $activeStudents,
            'total_revenue' => $totalRevenue,
            'course_ratings' => round($courseRatings, 1),
            'pending_reviews' => 0, // TODO: Implement review system
            'monthly_earnings' => $monthlyEarnings
        ];

        // Get recent activities (recent course enrollments)
        $recentActivities = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get course performance data
        $coursePerformance = \App\Models\Course::with(['enrollments'])
            ->where('tutor_id', $user->id)
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('tutor.dashboard', compact('user', 'stats', 'recentActivities', 'coursePerformance'));
    }

    /**
     * Show the tutor courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get real courses from database
        $publishedCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->get();

        $draftCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'draft')
            ->orderBy('updated_at', 'desc')
            ->get();

        return view('tutor.courses', compact('user', 'publishedCourses', 'draftCourses'));
    }

    /**
     * Show the create course page.
     */
    public function createCourse()
    {
        $user = Auth::user();
        $categories = \App\Models\Category::active()->get();
        return view('tutor.create-course', compact('user', 'categories'));
    }

    /**
     * Store a new course.
     */
    public function storeCourse(Request $request)
    {
        $user = Auth::user();

        // Debug: Log incoming request data
        Log::info('Course creation attempt', [
            'user_id' => $user->id,
            'request_data' => $request->all(),
            'has_file' => $request->hasFile('thumbnail')
        ]);

        // Validate the request with custom error messages
        $validated = $request->validate([
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_duration' => 'required|integer|min:1|max:500',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|numeric|min:30000',
            'learning_outcomes' => 'nullable|array',
            'learning_outcomes.*' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'nullable|string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ], [
            'course_title.required' => 'Judul kursus harus diisi.',
            'course_title.min' => 'Judul kursus minimal 10 karakter.',
            'course_title.max' => 'Judul kursus maksimal 255 karakter.',
            'course_category.required' => 'Kategori kursus harus dipilih.',
            'course_category.exists' => 'Kategori yang dipilih tidak valid.',
            'course_description.required' => 'Deskripsi kursus harus diisi.',
            'course_description.min' => 'Deskripsi kursus minimal 50 karakter.',
            'course_description.max' => 'Deskripsi kursus maksimal 1000 karakter.',
            'course_level.required' => 'Level kursus harus dipilih.',
            'course_level.in' => 'Level kursus tidak valid.',
            'course_duration.required' => 'Estimasi durasi harus diisi.',
            'course_duration.integer' => 'Estimasi durasi harus berupa angka.',
            'course_duration.min' => 'Estimasi durasi minimal 1 jam.',
            'course_duration.max' => 'Estimasi durasi maksimal 500 jam.',
            'course_type.required' => 'Tipe kursus harus dipilih.',
            'course_type.in' => 'Tipe kursus tidak valid.',
            'course_price.required_if' => 'Harga kursus harus diisi untuk kursus berbayar.',
            'course_price.numeric' => 'Harga kursus harus berupa angka.',
            'course_price.min' => 'Harga minimum untuk kursus berbayar adalah IDR 30.000.',
            'learning_outcomes.*.max' => 'Tujuan pembelajaran maksimal 255 karakter.',
            'requirements.*.max' => 'Prasyarat maksimal 255 karakter.',
            'target_audience.*.max' => 'Target audience maksimal 255 karakter.',
            'thumbnail.image' => 'File thumbnail harus berupa gambar.',
            'thumbnail.mimes' => 'Format thumbnail harus JPG, PNG, atau GIF.',
            'thumbnail.max' => 'Ukuran thumbnail maksimal 10MB.',
        ]);

        // Additional validation for course price step (kelipatan 1000)
        if ($validated['course_type'] === 'paid' && isset($validated['course_price'])) {
            $price = (float) $validated['course_price'];
            if ($price % 1000 !== 0) {
                return back()->withInput()->withErrors(['course_price' => 'Harga kursus harus kelipatan 1000.']);
            }
        }

        // Filter out empty values from arrays
        $learningOutcomes = array_filter($request->input('learning_outcomes', []), function($value) {
            return !empty(trim($value));
        });

        $requirements = array_filter($request->input('requirements', []), function($value) {
            return !empty(trim($value));
        });

        $targetAudience = array_filter($request->input('target_audience', []), function($value) {
            return !empty(trim($value));
        });

        try {
            Log::info('Creating course with data', [
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience
            ]);

            // Determine if course is free and set price
            $isFree = $validated['course_type'] === 'free';
            $price = $isFree ? 0 : ($validated['course_price'] ?? 0);

            // Create the course first to get the course ID
            $course = \App\Models\Course::create([
                'tutor_id' => $user->id,
                'category_id' => $validated['course_category'],
                'title' => $validated['course_title'],
                'description' => $validated['course_description'],
                'level' => $validated['course_level'],
                'duration' => $validated['course_duration'],
                'price' => $price,
                'is_free' => $isFree,
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience,
                'status' => 'draft',
            ]);

            Log::info('Course created successfully', ['course_id' => $course->id]);

            // Handle thumbnail upload with custom path structure including thumbnail folder
            if ($request->hasFile('thumbnail')) {
                Log::info('Processing thumbnail upload');
                $thumbnailPath = $request->file('thumbnail')->store(
                    "user/{$user->id}/course/{$course->id}/thumbnail",
                    'public'
                );

                Log::info('Thumbnail uploaded', ['path' => $thumbnailPath]);

                // Update course with thumbnail path
                $course->update(['thumbnail' => $thumbnailPath]);

                Log::info('Course updated with thumbnail path');
            }

            Log::info('Redirecting to courses page');
            return redirect()->route('tutor.courses', ['tab' => 'drafts'])->with('success', 'Kursus berhasil dibuat! Anda dapat melanjutkan dengan menambahkan kurikulum.');

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Database error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'sql' => $e->getSql() ?? 'N/A'
            ]);

            // Check for specific database errors
            if (str_contains($e->getMessage(), 'Duplicate entry')) {
                return back()->withInput()->with('error', 'Kursus dengan judul yang sama sudah ada. Silakan gunakan judul yang berbeda.');
            }

            return back()->withInput()->with('error', 'Terjadi kesalahan database saat membuat kursus. Silakan coba lagi.');

        } catch (\Illuminate\Contracts\Filesystem\FileNotFoundException $e) {
            Log::error('File upload error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withInput()->with('error', 'Terjadi kesalahan saat mengupload thumbnail. Silakan coba lagi.');

        } catch (\Exception $e) {
            Log::error('General error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
                'request_data' => $request->except(['thumbnail']) // Exclude file data from logs
            ]);

            // Provide more specific error messages based on error type
            if (str_contains($e->getMessage(), 'storage')) {
                return back()->withInput()->with('error', 'Terjadi kesalahan saat menyimpan file. Pastikan file tidak terlalu besar dan coba lagi.');
            }

            if (str_contains($e->getMessage(), 'validation')) {
                return back()->withInput()->with('error', 'Data yang dimasukkan tidak valid. Silakan periksa kembali form Anda.');
            }

            return back()->withInput()->with('error', 'Terjadi kesalahan tidak terduga saat membuat kursus. Tim teknis telah diberitahu. Silakan coba lagi dalam beberapa menit.');
        }
    }

    /**
     * Show the edit course page.
     */
    public function editCourse(\App\Models\Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        $categories = \App\Models\Category::active()->get();
        return view('tutor.edit-course', compact('user', 'course', 'categories'));
    }

    /**
     * Update the course.
     */
    public function updateCourse(Request $request, \App\Models\Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Validate the request with custom error messages
        $validated = $request->validate([
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_duration' => 'required|integer|min:1|max:500',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|numeric|min:30000',
            'learning_outcomes' => 'nullable|array',
            'learning_outcomes.*' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'nullable|string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ], [
            'course_title.required' => 'Judul kursus harus diisi.',
            'course_title.min' => 'Judul kursus minimal 10 karakter.',
            'course_title.max' => 'Judul kursus maksimal 255 karakter.',
            'course_category.required' => 'Kategori kursus harus dipilih.',
            'course_category.exists' => 'Kategori yang dipilih tidak valid.',
            'course_description.required' => 'Deskripsi kursus harus diisi.',
            'course_description.min' => 'Deskripsi kursus minimal 50 karakter.',
            'course_description.max' => 'Deskripsi kursus maksimal 1000 karakter.',
            'course_level.required' => 'Level kursus harus dipilih.',
            'course_level.in' => 'Level kursus tidak valid.',
            'course_duration.required' => 'Estimasi durasi harus diisi.',
            'course_duration.integer' => 'Estimasi durasi harus berupa angka.',
            'course_duration.min' => 'Estimasi durasi minimal 1 jam.',
            'course_duration.max' => 'Estimasi durasi maksimal 500 jam.',
            'course_type.required' => 'Tipe kursus harus dipilih.',
            'course_type.in' => 'Tipe kursus tidak valid.',
            'course_price.required_if' => 'Harga kursus harus diisi untuk kursus berbayar.',
            'course_price.numeric' => 'Harga kursus harus berupa angka.',
            'course_price.min' => 'Harga minimum untuk kursus berbayar adalah IDR 30.000.',
            'learning_outcomes.*.max' => 'Tujuan pembelajaran maksimal 255 karakter.',
            'requirements.*.max' => 'Prasyarat maksimal 255 karakter.',
            'target_audience.*.max' => 'Target audience maksimal 255 karakter.',
            'thumbnail.image' => 'File thumbnail harus berupa gambar.',
            'thumbnail.mimes' => 'Format thumbnail harus JPG, PNG, atau GIF.',
            'thumbnail.max' => 'Ukuran thumbnail maksimal 10MB.',
        ]);

        // Additional validation for course price step (kelipatan 1000)
        if ($validated['course_type'] === 'paid' && isset($validated['course_price'])) {
            $price = (float) $validated['course_price'];
            if ($price % 1000 !== 0) {
                return back()->withInput()->withErrors(['course_price' => 'Harga kursus harus kelipatan 1000.']);
            }
        }

        // Filter out empty values from arrays
        $learningOutcomes = array_filter($request->input('learning_outcomes', []), function($value) {
            return !empty(trim($value));
        });

        $requirements = array_filter($request->input('requirements', []), function($value) {
            return !empty(trim($value));
        });

        $targetAudience = array_filter($request->input('target_audience', []), function($value) {
            return !empty(trim($value));
        });

        try {
            // Determine if course is free and set price
            $isFree = $validated['course_type'] === 'free';
            $price = $isFree ? 0 : ($validated['course_price'] ?? 0);

            // Update the course
            $course->update([
                'category_id' => $validated['course_category'],
                'title' => $validated['course_title'],
                'description' => $validated['course_description'],
                'level' => $validated['course_level'],
                'duration' => $validated['course_duration'],
                'price' => $price,
                'is_free' => $isFree,
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience,
            ]);

            // Handle thumbnail upload
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if exists
                if ($course->thumbnail) {
                    \Storage::disk('public')->delete($course->thumbnail);
                }

                $thumbnailPath = $request->file('thumbnail')->store(
                    "user/{$user->id}/course/{$course->id}/thumbnail",
                    'public'
                );

                $course->update(['thumbnail' => $thumbnailPath]);
            }

            return redirect()->route('tutor.courses', ['tab' => 'drafts'])->with('success', 'Kursus berhasil diperbarui!');

        } catch (\Exception $e) {
            return back()->withInput()->with('error', 'Terjadi kesalahan saat memperbarui kursus. Silakan coba lagi.');
        }
    }

    /**
     * Show the tutor students page.
     */
    public function students()
    {
        $user = Auth::user();

        // Get real enrolled students data from database
        $enrolledStudents = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) {
                return [
                    'id' => $enrollment->user->id,
                    'name' => $enrollment->user->name,
                    'email' => $enrollment->user->email,
                    'course_title' => $enrollment->course->title,
                    'course_id' => $enrollment->course->id,
                    'enrollment_date' => $enrollment->enrolled_at->format('d M Y'),
                    'last_active' => $enrollment->updated_at->diffForHumans(),
                    'status' => $enrollment->status,
                    'progress' => 0, // TODO: Implement progress tracking
                ];
            });

        // Get tutor's courses for the filter dropdown
        $tutorCourses = \App\Models\Course::where('tutor_id', $user->id)
            ->select('id', 'title')
            ->orderBy('title')
            ->get();

        return view('tutor.students', compact('user', 'enrolledStudents', 'tutorCourses'));
    }

    /**
     * Show the tutor analytics page.
     */
    public function analytics()
    {
        $user = Auth::user();

        // Get real analytics data from database
        $tutorCourses = \App\Models\Course::where('tutor_id', $user->id)->get();
        $courseIds = $tutorCourses->pluck('id');

        // Course performance metrics
        $coursePerformance = $tutorCourses->map(function($course) {
            return [
                'course_id' => $course->id,
                'title' => $course->title,
                'total_students' => $course->total_students,
                'average_rating' => $course->average_rating ?: 0,
                'total_reviews' => $course->total_reviews,
                'revenue' => $course->price * $course->total_students,
                'status' => $course->status
            ];
        });

        // Monthly enrollment trends (last 6 months)
        $enrollmentTrends = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $enrollments = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->count();

            $enrollmentTrends[] = [
                'month' => $month->format('M Y'),
                'enrollments' => $enrollments
            ];
        }

        // Revenue analytics (last 6 months)
        $revenueAnalytics = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->sum('amount_paid');

            $revenueAnalytics[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue ?: 0
            ];
        }

        // Student engagement metrics
        $totalStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->distinct('user_id')->count('user_id');

        $activeStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->where('status', 'active')
        ->distinct('user_id')
        ->count('user_id');

        $completedStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->where('status', 'completed')
        ->distinct('user_id')
        ->count('user_id');

        $analyticsData = [
            'course_performance' => $coursePerformance,
            'enrollment_trends' => $enrollmentTrends,
            'revenue_analytics' => $revenueAnalytics,
            'student_engagement' => [
                'total_students' => $totalStudents,
                'active_students' => $activeStudents,
                'completed_students' => $completedStudents,
                'completion_rate' => $totalStudents > 0 ? round(($completedStudents / $totalStudents) * 100, 1) : 0
            ],
            'summary_stats' => [
                'total_courses' => $tutorCourses->count(),
                'published_courses' => $tutorCourses->where('status', 'published')->count(),
                'total_enrollments' => \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->count(),
                'total_revenue' => \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->sum('amount_paid'),
                'average_rating' => $tutorCourses->where('status', 'published')->avg('average_rating') ?: 0
            ]
        ];

        return view('tutor.analytics', compact('user', 'analyticsData'));
    }

    /**
     * Show the tutor earnings page.
     */
    public function earnings()
    {
        $user = Auth::user();

        // Get real earnings data from database
        $totalEarnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->sum('amount_paid');

        // Calculate monthly earnings (current month)
        $monthlyEarnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->whereYear('enrolled_at', now()->year)
        ->whereMonth('enrolled_at', now()->month)
        ->sum('amount_paid');

        // Get transaction history (recent enrollments)
        $transactionHistory = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('enrolled_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function($enrollment) {
                return [
                    'id' => $enrollment->id,
                    'student_name' => $enrollment->user->name,
                    'course_title' => $enrollment->course->title,
                    'amount' => $enrollment->amount_paid,
                    'date' => $enrollment->enrolled_at->format('d M Y'),
                    'status' => $enrollment->status,
                    'payment_method' => $enrollment->payment_method ?: 'N/A'
                ];
            });

        // Calculate earnings by course
        $earningsByCourse = \App\Models\Course::where('tutor_id', $user->id)
            ->with(['enrollments'])
            ->get()
            ->map(function($course) {
                $totalRevenue = $course->enrollments->sum('amount_paid');
                $totalEnrollments = $course->enrollments->count();

                return [
                    'course_id' => $course->id,
                    'title' => $course->title,
                    'price' => $course->price,
                    'total_enrollments' => $totalEnrollments,
                    'total_revenue' => $totalRevenue,
                    'status' => $course->status
                ];
            })
            ->sortByDesc('total_revenue');

        // Monthly earnings trend (last 6 months)
        $monthlyTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $earnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->sum('amount_paid');

            $monthlyTrend[] = [
                'month' => $month->format('M Y'),
                'earnings' => $earnings ?: 0
            ];
        }

        $earningsData = [
            'total_earnings' => $totalEarnings,
            'monthly_earnings' => $monthlyEarnings,
            'pending_payouts' => $totalEarnings * 0.8, // Assuming 80% payout rate (20% platform fee)
            'transaction_history' => $transactionHistory,
            'earnings_by_course' => $earningsByCourse,
            'monthly_trend' => $monthlyTrend,
            'summary_stats' => [
                'total_students' => \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->distinct('user_id')->count('user_id'),
                'average_course_price' => \App\Models\Course::where('tutor_id', $user->id)->avg('price'),
                'conversion_rate' => 0, // TODO: Implement with course views tracking
                'platform_fee' => $totalEarnings * 0.2 // 20% platform fee
            ]
        ];

        return view('tutor.earnings', compact('user', 'earningsData'));
    }

    /**
     * Show the tutor profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('tutor.profile', compact('user'));
    }

    /**
     * Show the tutor settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('tutor.settings', compact('user'));
    }

    /**
     * Update tutor profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        // Handle delete profile picture request
        if ($request->has('delete_profile_picture')) {
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
                $user->update(['profile_picture' => null]);
            }
            return back()->with('profile_success', 'Foto profil berhasil dihapus!');
        }

        // Handle profile picture upload only
        if ($request->hasFile('profile_picture')) {
            $request->validate([
                'profile_picture' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            ], [
                'profile_picture.image' => 'File harus berupa gambar.',
                'profile_picture.max' => 'Ukuran file maksimal 2MB.',
                'profile_picture.mimes' => 'Format file harus JPG, PNG, atau JPEG.',
            ]);

            // Delete old profile picture if exists
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
            }

            $profilePicturePath = \App\Services\FileStorageService::storePublicUserFile(
                $request->file('profile_picture'),
                $user->id,
                'profile'
            );

            $user->update(['profile_picture' => $profilePicturePath]);
            return back()->with('profile_success', 'Foto profil berhasil diperbarui!');
        }

        // Handle other profile data
        $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'long_description' => 'nullable|string|max:10000',
        ]);

        $user->update([
            'name' => $request->display_name,
        ]);

        // Update tutor profile if exists
        if ($user->tutorProfile) {
            $user->tutorProfile->update([
                'description' => $request->description,
                'long_description' => $request->long_description,
            ]);
        }

        return back()->with('success', 'Profil tutor berhasil diperbarui!');
    }

    /**
     * Publish a course.
     */
    public function publishCourse($courseId)
    {
        Log::info('PublishCourse method called', ['course_id' => $courseId]);
        $user = Auth::user();

        // Find the course by ID
        $course = \App\Models\Course::findOrFail($courseId);

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Validate that course has content before publishing
        $hasChapters = $course->chapters()->count() > 0;
        $hasLessons = $course->lessons()->count() > 0;
        $hasPublishedContent = $course->lessons()->where('is_published', true)->count() > 0;

        if (!$hasChapters || !$hasLessons || !$hasPublishedContent) {
            return back()->with('error', 'Tidak dapat menerbitkan kursus. Pastikan kursus memiliki minimal 1 bab dan 1 materi yang dipublikasikan.');
        }

        // Update course status to published
        $course->update([
            'status' => 'published',
            'published_at' => now(),
        ]);

        return back()->with('success', 'Kursus berhasil diterbitkan! Sekarang siswa dapat mendaftar di kursus Anda.');
    }

    /**
     * Save course as draft.
     */
    public function saveDraft($courseId)
    {
        $user = Auth::user();

        // Find the course by ID
        $course = \App\Models\Course::findOrFail($courseId);

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Update course status to draft
        $course->update([
            'status' => 'draft',
            'published_at' => null,
        ]);

        return back()->with('success', 'Kursus berhasil disimpan sebagai draft.');
    }
}
