<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_tutor',
        'is_admin',
        'is_superadmin',
        'tutor_status',
        'profile_picture',
        'bio',
        'location',
        'website',
        'linkedin_url',
        'github_url',
        'twitter_url',
        'instagram_url',
        'youtube_url',
        'facebook_url',
        'job_title',
        'company',
        'skills',
        // Education & Learning
        'pendidikan',
        'minat_belajar',
        'institusi_pendidikan',
        'jurusan',
        'tahun_lulus',
        // AI Career Assistant Data
        'career_goals',
        'work_experience',
        'certifications',
        'learning_preferences',
        'personality_traits',
        'experience_years',
        'industry_interests',
        'salary_expectations',
        'work_preferences',
        'ai_recommendations',
        'last_ai_analysis',
        // NALA Membership fields
        'current_membership_id',
        'membership_expires_at',
        'nala_prompts_remaining',
        'has_unlimited_nala',
        'referral_code',
        'referred_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_tutor' => 'boolean',
        'is_admin' => 'boolean',
        'is_superadmin' => 'boolean',
        'skills' => 'array',
        // Education & Learning
        'minat_belajar' => 'array',
        'tahun_lulus' => 'integer',
        // AI Career Assistant Data
        'career_goals' => 'array',
        'work_experience' => 'array',
        'certifications' => 'array',
        'learning_preferences' => 'array',
        'personality_traits' => 'array',
        'experience_years' => 'integer',
        'industry_interests' => 'array',
        'salary_expectations' => 'array',
        'work_preferences' => 'array',
        'ai_recommendations' => 'array',
        'last_ai_analysis' => 'datetime',
        // NALA Membership fields
        'membership_expires_at' => 'datetime',
        'nala_prompts_remaining' => 'integer',
        'has_unlimited_nala' => 'boolean',
    ];

    /**
     * Check if user is a tutor
     */
    public function isTutor(): bool
    {
        return (bool) $this->is_tutor;
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin(): bool
    {
        return (bool) $this->is_admin;
    }

    /**
     * Check if user is a super admin
     */
    public function isSuperAdmin(): bool
    {
        return (bool) $this->is_superadmin;
    }

    /**
     * Check if user has any admin privileges
     */
    public function hasAdminAccess(): bool
    {
        return $this->is_admin || $this->is_superadmin;
    }

    /**
     * Get user role as string
     */
    public function getRole(): string
    {
        if ($this->is_superadmin) {
            return 'Super Admin';
        }
        if ($this->is_admin) {
            return 'Admin';
        }
        if ($this->is_tutor) {
            return 'Tutor';
        }
        return 'Student';
    }

    /**
     * Get user role badge color
     */
    public function getRoleBadgeColor(): string
    {
        if ($this->is_superadmin) {
            return 'red';
        }
        if ($this->is_admin) {
            return 'purple';
        }
        if ($this->is_tutor) {
            return 'blue';
        }
        return 'green';
    }

    /**
     * Get the tutor profile associated with the user.
     */
    public function tutorProfile(): HasOne
    {
        return $this->hasOne(TutorProfile::class);
    }

    /**
     * Get the course enrollments for the user.
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get the courses the user is enrolled in.
     */
    public function enrolledCourses(): BelongsToMany
    {
        return $this->belongsToMany(Course::class, 'course_enrollments')
                    ->withPivot(['status', 'amount_paid', 'enrolled_at', 'completed_at'])
                    ->withTimestamps();
    }

    /**
     * Get the courses created by this tutor.
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class, 'tutor_id');
    }

    /**
     * Get the quiz attempts for the user.
     */
    public function quizAttempts(): HasMany
    {
        return $this->hasMany(QuizAttempt::class);
    }

    /**
     * Get the quiz answers for the user.
     */
    public function quizAnswers(): HasMany
    {
        return $this->hasMany(QuizAnswer::class, 'graded_by');
    }

    /**
     * Get the assignment submissions for the user.
     */
    public function assignmentSubmissions(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    /**
     * Get the exam enrollments for the user.
     */
    public function examEnrollments(): HasMany
    {
        return $this->hasMany(ExamEnrollment::class);
    }

    /**
     * Get the exams the user is enrolled in.
     */
    public function enrolledExams(): BelongsToMany
    {
        return $this->belongsToMany(Exam::class, 'exam_enrollments')
                    ->withPivot(['payment_status', 'enrolled_at', 'is_active', 'amount_paid'])
                    ->withTimestamps();
    }

    /**
     * Get the exam attempts for the user.
     */
    public function examAttempts(): HasMany
    {
        return $this->hasMany(ExamAttempt::class);
    }

    /**
     * Get the assignment submissions graded by the user.
     */
    public function gradedSubmissions(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class, 'graded_by');
    }

    /**
     * Get the lesson progress for the user.
     */
    public function lessonProgress(): HasMany
    {
        return $this->hasMany(LessonProgress::class);
    }

    /**
     * Check if user has a tutor profile.
     */
    public function hasTutorProfile(): bool
    {
        return $this->tutorProfile()->exists();
    }

    /**
     * Get tutor application status.
     */
    public function getTutorStatus(): ?string
    {
        return $this->tutor_status;
    }

    /**
     * Check if tutor application is pending.
     */
    public function isTutorPending(): bool
    {
        return $this->tutor_status === 'pending';
    }

    /**
     * Check if tutor application is approved.
     */
    public function isTutorApproved(): bool
    {
        return $this->tutor_status === 'approved';
    }

    /**
     * Check if tutor application is rejected.
     */
    public function isTutorRejected(): bool
    {
        return $this->tutor_status === 'rejected';
    }

    /**
     * Get profile picture URL or default avatar.
     */
    public function getProfilePictureUrl(): string
    {
        if ($this->profile_picture && file_exists(storage_path('app/public/' . $this->profile_picture))) {
            return asset('storage/' . $this->profile_picture);
        }

        // Return default avatar based on user initials
        return $this->getDefaultAvatarUrl();
    }

    /**
     * Get default avatar URL with user initials.
     */
    public function getDefaultAvatarUrl(): string
    {
        $initials = $this->getInitials();
        return "https://ui-avatars.com/api/?name=" . urlencode($initials) . "&color=7F9CF5&background=EBF4FF&size=200";
    }

    /**
     * Get user initials for avatar.
     */
    public function getInitials(): string
    {
        $words = explode(' ', trim($this->name));
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        }
        return strtoupper(substr($this->name, 0, 2));
    }

    /**
     * Get social media links as array.
     */
    public function getSocialMediaLinks(): array
    {
        return array_filter([
            'linkedin' => $this->linkedin_url,
            'github' => $this->github_url,
            'twitter' => $this->twitter_url,
            'instagram' => $this->instagram_url,
            'youtube' => $this->youtube_url,
            'facebook' => $this->facebook_url,
        ]);
    }

    /**
     * Check if user has any social media links.
     */
    public function hasSocialMediaLinks(): bool
    {
        return !empty($this->getSocialMediaLinks());
    }

    /**
     * Get formatted skills array.
     */
    public function getSkillsArray(): array
    {
        return $this->skills ?? [];
    }

    /**
     * Get skills as comma-separated string.
     */
    public function getSkillsString(): string
    {
        return implode(', ', $this->getSkillsArray());
    }

    /**
     * Get the current membership for the user.
     */
    public function currentMembership(): HasOne
    {
        return $this->hasOne(UserMembership::class, 'id', 'current_membership_id');
    }

    /**
     * Get all memberships for the user.
     */
    public function memberships(): HasMany
    {
        return $this->hasMany(UserMembership::class);
    }

    /**
     * Get the active membership for the user.
     */
    public function activeMembership(): HasOne
    {
        return $this->hasOne(UserMembership::class)->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Get all payments made by the user.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get course purchases for the user.
     */
    public function coursePurchases(): HasMany
    {
        return $this->hasMany(CoursePurchase::class);
    }

    /**
     * Get certification purchases for the user.
     */
    public function certificationPurchases(): HasMany
    {
        return $this->hasMany(CertificationPurchase::class);
    }

    /**
     * Get the user who referred this user.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    /**
     * Get users referred by this user.
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    /**
     * Check if user has an active membership.
     */
    public function hasActiveMembership(): bool
    {
        return $this->activeMembership()->exists();
    }

    /**
     * Get the user's saved articles.
     */
    public function savedArticles(): HasMany
    {
        return $this->hasMany(SavedArticle::class);
    }

    /**
     * Get the blog posts saved by the user.
     */
    public function savedBlogPosts(): BelongsToMany
    {
        return $this->belongsToMany(BlogPost::class, 'saved_articles')
                    ->withPivot(['saved_at', 'notes'])
                    ->withTimestamps()
                    ->orderBy('saved_articles.saved_at', 'desc');
    }

    /**
     * Check if user can use NALA prompts.
     */
    public function canUseNalaPrompts(int $count = 1): bool
    {
        if ($this->has_unlimited_nala) {
            return true;
        }

        $activeMembership = $this->activeMembership;
        if ($activeMembership) {
            return $activeMembership->canUseNalaPrompts($count);
        }

        return false; // Free users have limited access
    }

    /**
     * Use NALA prompts.
     */
    public function useNalaPrompts(int $count = 1): bool
    {
        if ($this->has_unlimited_nala) {
            return true;
        }

        $activeMembership = $this->activeMembership;
        if ($activeMembership && $activeMembership->useNalaPrompts($count)) {
            // Update user's remaining prompts cache
            $this->update([
                'nala_prompts_remaining' => $activeMembership->nala_prompts_remaining
            ]);
            return true;
        }

        return false;
    }

    /**
     * Generate a unique referral code for the user.
     */
    public function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr($this->name, 0, 3) . rand(1000, 9999));
        } while (User::where('referral_code', $code)->exists());

        $this->update(['referral_code' => $code]);
        return $code;
    }

    /**
     * Get the referral code or generate one if it doesn't exist.
     */
    public function getReferralCode(): string
    {
        if (!$this->referral_code) {
            return $this->generateReferralCode();
        }

        return $this->referral_code;
    }

    /**
     * Check if user has completed a specific course.
     */
    public function hasCompletedCourse(string $courseId): bool
    {
        return $this->enrollments()
            ->where('course_id', $courseId)
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Check if user is enrolled in a specific course.
     */
    public function isEnrolledInCourse(string $courseId): bool
    {
        return $this->enrollments()
            ->where('course_id', $courseId)
            ->exists();
    }

    /**
     * Get enrollment for a specific course.
     */
    public function getCourseEnrollment(string $courseId)
    {
        return $this->enrollments()
            ->where('course_id', $courseId)
            ->first();
    }
}
