<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LessonProgress extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'lesson_id',
        'status',
        'progress_percentage',
        'started_at',
        'completed_at',
        'last_accessed_at',
        'video_progress_seconds',
        'video_duration_seconds',
        'quiz_attempts',
        'best_quiz_score',
        'quiz_passed',
        'assignment_submitted',
        'assignment_score',
        'assignment_passed',
        'time_spent_seconds',
        'visit_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'progress_percentage' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'video_progress_seconds' => 'integer',
        'video_duration_seconds' => 'integer',
        'quiz_attempts' => 'integer',
        'best_quiz_score' => 'integer',
        'quiz_passed' => 'boolean',
        'assignment_submitted' => 'boolean',
        'assignment_score' => 'integer',
        'assignment_passed' => 'boolean',
        'time_spent_seconds' => 'integer',
        'visit_count' => 'integer',
    ];

    /**
     * Get the user that owns the progress.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lesson that this progress belongs to.
     */
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(CourseLesson::class, 'lesson_id');
    }

    /**
     * Check if the lesson is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the lesson is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if the lesson is not started.
     */
    public function isNotStarted(): bool
    {
        return $this->status === 'not_started';
    }

    /**
     * Mark the lesson as started.
     */
    public function markAsStarted(): void
    {
        if ($this->isNotStarted()) {
            $this->update([
                'status' => 'in_progress',
                'started_at' => now(),
                'last_accessed_at' => now(),
                'visit_count' => 1,
            ]);
        } else {
            $this->update([
                'last_accessed_at' => now(),
                'visit_count' => $this->visit_count + 1,
            ]);
        }
    }

    /**
     * Mark the lesson as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'progress_percentage' => 100,
            'completed_at' => now(),
            'last_accessed_at' => now(),
        ]);
    }

    /**
     * Update video progress.
     */
    public function updateVideoProgress(int $progressSeconds, int $durationSeconds = null): void
    {
        $data = [
            'video_progress_seconds' => $progressSeconds,
            'last_accessed_at' => now(),
        ];

        if ($durationSeconds) {
            $data['video_duration_seconds'] = $durationSeconds;
        }

        // Calculate progress percentage
        if ($this->video_duration_seconds > 0) {
            $progressPercentage = min(100, ($progressSeconds / $this->video_duration_seconds) * 100);
            $data['progress_percentage'] = round($progressPercentage);

            // Mark as completed if video is 90% watched
            if ($progressPercentage >= 90 && !$this->isCompleted()) {
                $data['status'] = 'completed';
                $data['completed_at'] = now();
                $data['progress_percentage'] = 100;
            }
        }

        $this->update($data);
    }

    /**
     * Update quiz progress.
     */
    public function updateQuizProgress(int $score, bool $passed): void
    {
        $this->update([
            'quiz_attempts' => $this->quiz_attempts + 1,
            'best_quiz_score' => max($this->best_quiz_score ?? 0, $score),
            'quiz_passed' => $this->quiz_passed || $passed,
            'last_accessed_at' => now(),
        ]);

        // Mark lesson as completed if quiz is passed
        if ($passed && !$this->isCompleted()) {
            $this->markAsCompleted();
        }
    }

    /**
     * Update assignment progress.
     */
    public function updateAssignmentProgress(bool $submitted, int $score = null, bool $passed = null): void
    {
        $data = [
            'assignment_submitted' => $submitted,
            'last_accessed_at' => now(),
        ];

        if ($score !== null) {
            $data['assignment_score'] = $score;
        }

        if ($passed !== null) {
            $data['assignment_passed'] = $passed;
        }

        $this->update($data);

        // Mark lesson as completed if assignment is submitted and passed
        if ($submitted && $passed && !$this->isCompleted()) {
            $this->markAsCompleted();
        }
    }

    /**
     * Add time spent on the lesson.
     */
    public function addTimeSpent(int $seconds): void
    {
        $this->update([
            'time_spent_seconds' => $this->time_spent_seconds + $seconds,
            'last_accessed_at' => now(),
        ]);
    }

    /**
     * Get the status in Indonesian.
     */
    public function getStatusIndonesianAttribute(): string
    {
        return match($this->status) {
            'not_started' => 'Belum Dimulai',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get formatted time spent.
     */
    public function getFormattedTimeSpentAttribute(): string
    {
        $hours = floor($this->time_spent_seconds / 3600);
        $minutes = floor(($this->time_spent_seconds % 3600) / 60);
        $seconds = $this->time_spent_seconds % 60;

        if ($hours > 0) {
            return sprintf('%d jam %d menit', $hours, $minutes);
        } elseif ($minutes > 0) {
            return sprintf('%d menit %d detik', $minutes, $seconds);
        } else {
            return sprintf('%d detik', $seconds);
        }
    }
}
