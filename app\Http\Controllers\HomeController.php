<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Category;
use App\Models\User;
use App\Models\CourseEnrollment;
use App\Models\ExamAttempt;

class HomeController extends Controller
{
    public function index()
    {
        // Get real courses from database
        $allCourses = Course::with(['tutor', 'category'])
            ->published()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get();

        // Transform courses for homepage display
        $courses = $allCourses->map(function ($course) {
            return [
                'id' => $course->id,
                'title' => $course->title,
                'description' => $course->description,
                'category' => $course->category->name ?? 'Uncategorized',
                'level' => ucfirst($course->level),
                'price' => $course->is_free ? 'Free' : 'Rp ' . number_format($course->price, 0, ',', '.'),
                'original_price' => null, // Can be implemented later for discounts
                'rating' => $course->average_rating ?: 4.5,
                'students' => $course->total_students ?: 0,
                'duration' => $course->duration ?: '2 jam',
                'image' => $course->thumbnail ? 'storage/' . $course->thumbnail : 'images/courses/placeholder.svg',
                'type' => $this->getCourseType($course),
                'instructor' => $course->tutor->name ?? 'Unknown',
                'slug' => $course->slug,
            ];
        })->toArray();

        // Get real exams from database for exam section
        $featuredExams = Exam::with(['tutor', 'category', 'questions'])
            ->published()
            ->limit(3)
            ->get()
            ->map(function ($exam) {
                return [
                    'id' => $exam->id,
                    'title' => $exam->title,
                    'description' => $exam->description,
                    'category' => $exam->category->name ?? 'Uncategorized',
                    'difficulty_level' => ucfirst($exam->difficulty_level),
                    'price' => $exam->price > 0 ? 'Rp ' . number_format($exam->price, 0, ',', '.') : 'GRATIS',
                    'time_limit' => $exam->time_limit,
                    'total_questions' => $exam->questions->count(),
                    'total_enrollments' => $exam->enrollments->count(),
                    'tutor' => $exam->tutor->name ?? 'Unknown',
                ];
            })
            ->toArray();

        // Get dynamic statistics from database
        $totalStudents = CourseEnrollment::count();
        $totalCourses = Course::published()->count();
        $totalExams = Exam::published()->count();
        $totalExamAttempts = ExamAttempt::count();

        $testimonials = [
            [
                'id' => 1,
                'name' => 'Andi Pratama',
                'role' => 'Frontend Developer di Gojek',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Ngambiskuy membantu saya menjadi developer Gojek dalam 4 bulan! Jalur pembelajaran bertenaga AI sangat sesuai dengan tujuan saya.',
                'course' => 'Bootcamp Pengembangan Web Lengkap',
            ],
            [
                'id' => 2,
                'name' => 'Sari Dewi',
                'role' => 'Data Scientist di Tokopedia',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Track Data Science melebihi ekspektasi saya. Asisten pengajar AI tersedia 24/7 untuk membantu konsep yang kompleks.',
                'course' => 'Data Science dengan Python',
            ],
            [
                'id' => 3,
                'name' => 'Rizki Hakim',
                'role' => 'Mobile Developer di Bukalapak',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Dari nol menjadi mobile developer dalam 6 bulan. Fitur gamifikasi membuat saya tetap termotivasi.',
                'course' => 'Pengembangan Aplikasi Mobile',
            ],
        ];

        // Dynamic blog posts from database
        $blogPosts = \App\Models\BlogPost::with(['author', 'category'])
            ->published()
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // Dynamic exam statistics
        $examStats = [
            'total_exams_taken' => $totalExamAttempts ?: 50000,
            'available_exams' => $totalExams ?: 1200,
            'certificates_issued' => 15000, // Can be implemented later
            'satisfaction_rate' => 98,
        ];

        // Hero section statistics
        $heroStats = [
            'total_students' => $totalStudents ?: 10000,
            'total_courses' => $totalCourses ?: 500,
            'average_rating' => 4.9,
        ];

        return view('home', compact('courses', 'featuredExams', 'testimonials', 'blogPosts', 'examStats', 'heroStats'));
    }

    /**
     * Determine course type based on course properties
     */
    private function getCourseType($course)
    {
        if ($course->is_free) {
            return 'free';
        }

        // You can add more logic here to determine course types
        // For now, we'll categorize based on price ranges or other criteria
        if ($course->price > 200000) {
            return 'premium';
        } elseif ($course->price > 50000) {
            return 'tutorial';
        } else {
            return 'webinar';
        }
    }
}