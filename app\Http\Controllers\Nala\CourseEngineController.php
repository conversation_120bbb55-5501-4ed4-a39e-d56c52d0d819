<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Category;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CourseEngineController extends Controller
{
    private $geminiApiKey;
    private $geminiModel;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key');
        $this->geminiModel = config('services.gemini.model', 'gemini-2.0-flash-exp');
    }

    /**
     * Handle course-related questions - Send data to Gemini AI
     */
    public function handleCourseQuestion($message, $context, $userProfile, $membership, $courseContext = [])
    {
        try {
            // Build comprehensive course data for Gemini
            $courseData = $this->buildCourseDataForGemini($courseContext, $context, $userProfile);

            // Create system prompt with course data
            $systemPrompt = $this->buildCourseSystemPrompt($userProfile, $courseData);

            // Create user prompt
            $userPrompt = $this->buildCourseUserPrompt($message, $context, $courseData);

            // Call Gemini AI with course data
            return $this->callGeminiAPI($systemPrompt, $userPrompt);

        } catch (\Exception $e) {
            Log::error('Course Engine Error: ' . $e->getMessage());

            // Fallback to simple course response
            return $this->getFallbackCourseResponse($message, $courseContext, $userProfile);
        }
    }

    /**
     * Build comprehensive course data for Gemini AI
     */
    private function buildCourseDataForGemini($courseContext, $context, $userProfile)
    {
        $courseData = [
            'context_type' => $context,
            'user_profile' => $userProfile,
            'available_courses' => [],
            'current_course' => null,
            'categories' => []
        ];

        // Add current course context if available
        if (!empty($courseContext) && isset($courseContext['course'])) {
            $courseData['current_course'] = $courseContext['course'];
        }

        // Get tech categories
        $techCategories = $this->getTechCategories();
        $courseData['categories'] = $techCategories;

        // Get relevant courses based on context
        if ($context === 'course_detail' && !empty($courseData['current_course'])) {
            // Get related courses for course detail page
            $courseData['related_courses'] = $this->getRelatedCourses($courseData['current_course']);
        } else {
            // Get popular/recommended courses
            $courseData['available_courses'] = $this->getPopularCourses(10);
        }

        return $courseData;
    }

    /**
     * Build system prompt for course questions
     */
    private function buildCourseSystemPrompt($userProfile, $courseData)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';

        $prompt = "Anda adalah Nala, asisten belajar yang ramah di platform Ngambiskuy.

KARAKTERISTIK:
- Ramah, natural, dan supportif seperti teman
- Berikan jawaban praktis dan mudah dipahami
- Maksimal 150 kata untuk respons
- Gunakan emoji secukupnya untuk membuat friendly

PLATFORM NGAMBISKUY:
Platform edukasi teknologi dengan fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

DATA YANG TERSEDIA:";

        // Add current course data if available
        if (!empty($courseData['current_course'])) {
            $course = $courseData['current_course'];
            $prompt .= "\n\nKURSUS SAAT INI:
- Judul: " . ($course['title'] ?? 'Tidak diketahui') . "
- Level: " . ($course['level'] ?? 'Tidak diketahui') . "
- Harga: " . ($course['price'] ?? 'Tidak diketahui') . "
- Deskripsi: " . ($course['description'] ?? 'Tidak diketahui');

            if (isset($course['tutor'])) {
                $prompt .= "\n- Tutor: " . $course['tutor'];
            }
            if (isset($course['students_count'])) {
                $prompt .= "\n- Jumlah Siswa: " . $course['students_count'];
            }
            if (isset($course['rating'])) {
                $prompt .= "\n- Rating: " . $course['rating'];
            }
        }

        // Add available courses
        if (!empty($courseData['available_courses'])) {
            $prompt .= "\n\nKURSUS TERSEDIA:";
            foreach (array_slice($courseData['available_courses'], 0, 5) as $course) {
                $price = $course['is_free'] ? 'GRATIS' : 'Rp ' . number_format($course['price'], 0, ',', '.');
                $prompt .= "\n- " . $course['title'] . " (" . $course['level'] . ") - " . $price;
            }
        }

        $prompt .= "\n\nINSTRUKSI:
- Gunakan data di atas untuk memberikan respons yang akurat
- Jika ditanya tentang kursus spesifik, gunakan data yang tersedia
- Berikan rekomendasi berdasarkan profil user dan data kursus
- Jangan buat-buat informasi yang tidak ada dalam data
- Fokus pada manfaat dan nilai kursus untuk karir user

Pengguna: {$name}";

        return $prompt;
    }

    /**
     * Build user prompt for course questions
     */
    private function buildCourseUserPrompt($message, $context, $courseData)
    {
        $prompt = "Konteks: ";

        if ($context === 'course_detail' && !empty($courseData['current_course'])) {
            $prompt .= "User sedang melihat halaman detail kursus '" . $courseData['current_course']['title'] . "'";
        } elseif ($context === 'course_listing') {
            $prompt .= "User sedang melihat daftar kursus";
        } else {
            $prompt .= "User bertanya tentang kursus";
        }

        $prompt .= "\n\nPertanyaan user: " . $message;

        $prompt .= "\n\nJawab dengan natural dan helpful berdasarkan data yang tersedia. Jika user bertanya tentang kursus tertentu, gunakan informasi yang akurat dari data.";

        return $prompt;
    }

    /**
     * Get tech categories
     */
    private function getTechCategories()
    {
        $techCategorySlugs = [
            'programming', 'web-development', 'mobile-development',
            'data-science', 'ui-ux-design', 'digital-marketing',
            'business', 'cybersecurity'
        ];

        return Category::whereIn('slug', $techCategorySlugs)
            ->where('is_active', true)
            ->get(['id', 'name', 'slug', 'description'])
            ->toArray();
    }

    /**
     * Get related courses
     */
    private function getRelatedCourses($currentCourse)
    {
        // Try to find course by title to get related courses
        $course = Course::where('title', $currentCourse['title'])->first();

        if (!$course) {
            return $this->getPopularCourses(3);
        }

        return Course::where('status', 'published')
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->orderBy('total_students', 'desc')
            ->limit(3)
            ->get(['id', 'title', 'description', 'level', 'price', 'is_free', 'total_students'])
            ->toArray();
    }

    /**
     * Get popular courses
     */
    private function getPopularCourses($limit = 5)
    {
        $techCategoryIds = $this->getTechCategoryIds();

        return Course::where('status', 'published')
            ->whereIn('category_id', $techCategoryIds)
            ->orderBy('total_students', 'desc')
            ->orderBy('is_featured', 'desc')
            ->limit($limit)
            ->get(['id', 'title', 'description', 'level', 'price', 'is_free', 'total_students'])
            ->toArray();
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 512,
            ]
        ];

        $response = Http::timeout(15)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Fallback response when Gemini fails
     */
    private function getFallbackCourseResponse($message, $courseContext, $userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';

        if (!empty($courseContext) && isset($courseContext['course'])) {
            $courseTitle = $courseContext['course']['title'] ?? 'kursus ini';
            return "Halo {$name}! Saya siap membantu dengan pertanyaan tentang \"{$courseTitle}\". Ada yang ingin ditanyakan tentang materi, manfaat, atau kesesuaiannya dengan tujuan Anda?";
        }

        return "Halo {$name}! Saya siap membantu dengan pertanyaan tentang kursus. Ada topik atau skill tertentu yang ingin Anda pelajari?";
    }

    /**
     * Get tech category IDs
     */
    private function getTechCategoryIds()
    {
        $techCategorySlugs = [
            'programming', 'web-development', 'mobile-development',
            'data-science', 'ui-ux-design', 'digital-marketing',
            'business', 'cybersecurity'
        ];

        return Category::whereIn('slug', $techCategorySlugs)
            ->where('is_active', true)
            ->pluck('id')
            ->toArray();
    }
}
