<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SavedArticle;
use App\Models\User;
use App\Models\BlogPost;
use Carbon\Carbon;

class SavedArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users (including admins and tutors for testing)
        $users = User::all();

        // Get all published blog posts
        $blogPosts = BlogPost::where('status', 'published')->get();

        if ($users->isEmpty() || $blogPosts->isEmpty()) {
            $this->command->info('No users or blog posts found. Skipping SavedArticle seeding.');
            return;
        }

        // Create some saved articles for testing
        $savedArticlesData = [];

        foreach ($users->take(5) as $user) {
            // Each user saves 2-4 random articles
            $articlesToSave = $blogPosts->random(rand(2, min(4, $blogPosts->count())));

            foreach ($articlesToSave as $article) {
                // Check if this combination already exists
                $exists = SavedArticle::where('user_id', $user->id)
                                    ->where('blog_post_id', $article->id)
                                    ->exists();

                if (!$exists) {
                    $savedArticlesData[] = [
                        'id' => \Illuminate\Support\Str::uuid(),
                        'user_id' => $user->id,
                        'blog_post_id' => $article->id,
                        'saved_at' => Carbon::now()->subDays(rand(1, 30)),
                        'notes' => rand(0, 1) ? 'Artikel menarik untuk dibaca nanti' : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        // Insert all saved articles
        if (!empty($savedArticlesData)) {
            SavedArticle::insert($savedArticlesData);
            $this->command->info('Created ' . count($savedArticlesData) . ' saved articles.');
        }

        // Create some specific test cases for the first user
        $firstUser = $users->first();
        if ($firstUser && $blogPosts->count() > 0) {
            // Save the first featured article
            $featuredArticle = $blogPosts->where('is_featured', true)->first();
            if ($featuredArticle) {
                SavedArticle::firstOrCreate([
                    'user_id' => $firstUser->id,
                    'blog_post_id' => $featuredArticle->id,
                ], [
                    'saved_at' => Carbon::now()->subDays(1),
                    'notes' => 'Artikel unggulan yang sangat informatif',
                ]);
            }
        }
    }
}
