<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CoursePurchase extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'course_id',
        'payment_id',
        'status',
        'amount_paid',
        'purchased_at',
        'cancelled_at',
        'referrer_id',
        'has_referral_bonus',
        'last_accessed_at',
        'total_access_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount_paid' => 'decimal:2',
        'purchased_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'has_referral_bonus' => 'boolean',
        'last_accessed_at' => 'datetime',
        'total_access_count' => 'integer',
    ];

    /**
     * Get the user that purchased the course.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that was purchased.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the referrer user.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the payment for this purchase.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Scope a query to only include active purchases.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include cancelled purchases.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Check if the purchase is currently active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the purchase is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }



    /**
     * Record course access.
     */
    public function recordAccess(): void
    {
        $this->update([
            'last_accessed_at' => now(),
            'total_access_count' => $this->total_access_count + 1,
        ]);
    }



    /**
     * Get the formatted amount paid.
     */
    public function getFormattedAmountPaidAttribute(): string
    {
        return 'IDR ' . number_format($this->amount_paid, 0, ',', '.');
    }

    /**
     * Cancel the purchase.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }


}
