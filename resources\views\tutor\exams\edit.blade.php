@extends('layouts.tutor')

@section('title', '<PERSON> - ' . $exam->title . ' - Ngambis<PERSON><PERSON>')

@section('content')
<div class="p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <a href="{{ route('tutor.exams.show', $exam) }}" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">Edit <PERSON></h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Mode
                    </span>
                </div>
                <p class="text-gray-600 mt-1">Edit informasi dan soal ujian: {{ $exam->title }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('tutor.exams.download-template') }}" class="btn bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download Template CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('tutor.exams.update', $exam) }}" method="POST" id="examForm">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Dasar</h2>

                    <div class="space-y-4">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Judul Ujian *</label>
                            <input type="text" name="title" id="title" value="{{ old('title', $exam->title) }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Masukkan judul ujian">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi *</label>
                            <textarea name="description" id="description" rows="4" required
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                      placeholder="Jelaskan tentang ujian ini">{{ old('description', $exam->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Kategori *</label>
                                <select name="category_id" id="category_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    <option value="">Pilih Kategori</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $exam->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">Tingkat Kesulitan *</label>
                                <select name="difficulty_level" id="difficulty_level" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    <option value="">Pilih Tingkat</option>
                                    <option value="beginner" {{ old('difficulty_level', $exam->difficulty_level) == 'beginner' ? 'selected' : '' }}>Pemula</option>
                                    <option value="intermediate" {{ old('difficulty_level', $exam->difficulty_level) == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                                    <option value="advanced" {{ old('difficulty_level', $exam->difficulty_level) == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                                </select>
                                @error('difficulty_level')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Ujian *</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="exam_type" value="free"
                                           {{ old('exam_type', $exam->price == 0 ? 'free' : 'paid') == 'free' ? 'checked' : '' }}
                                           onchange="toggleExamPriceField()"
                                           class="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300 focus:ring-emerald-500">
                                    <span class="ml-2 text-sm text-gray-700">Gratis</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="exam_type" value="paid"
                                           {{ old('exam_type', $exam->price == 0 ? 'free' : 'paid') == 'paid' ? 'checked' : '' }}
                                           onchange="toggleExamPriceField()"
                                           class="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300 focus:ring-emerald-500">
                                    <span class="ml-2 text-sm text-gray-700">Berbayar</span>
                                </label>
                            </div>
                            @error('exam_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div id="exam-price-field" class="{{ old('exam_type', $exam->price == 0 ? 'free' : 'paid') == 'free' ? 'hidden' : '' }}">
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Harga (Rp) *</label>
                            <input type="number" name="price" id="price" value="{{ old('price', $exam->price) }}" min="15000" step="1000" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="15000">
                            <p class="mt-1 text-xs text-gray-500">
                                Harga minimum: IDR 15.000. Tutor mendapat 80%, platform 20% (fixed rate, no referral).
                            </p>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="instructions" class="block text-sm font-medium text-gray-700 mb-2">Instruksi Ujian</label>
                            <textarea name="instructions" id="instructions" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                      placeholder="Instruksi khusus untuk peserta ujian (opsional)">{{ old('instructions', $exam->instructions) }}</textarea>
                            @error('instructions')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Quiz Settings -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Ujian</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="time_limit" class="block text-sm font-medium text-gray-700 mb-2">Batas Waktu (menit) *</label>
                            <input type="number" name="time_limit" id="time_limit" value="{{ old('time_limit', $exam->time_limit) }}" min="1" max="300" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('time_limit')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-2">Maksimal Percobaan *</label>
                            <input type="number" name="max_attempts" id="max_attempts" value="{{ old('max_attempts', $exam->max_attempts) }}" min="1" max="10" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('max_attempts')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="passing_score" class="block text-sm font-medium text-gray-700 mb-2">Nilai Lulus (%) *</label>
                            <input type="number" name="passing_score" id="passing_score" value="{{ old('passing_score', $exam->passing_score) }}" min="0" max="100" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('passing_score')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center space-y-2 flex-col">
                            <div class="flex items-center w-full">
                                <input type="checkbox" name="shuffle_questions" id="shuffle_questions" value="1" {{ old('shuffle_questions', $exam->shuffle_questions) ? 'checked' : '' }}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                                <label for="shuffle_questions" class="ml-2 text-sm text-gray-700">Acak urutan soal</label>
                            </div>

                            <div class="flex items-center w-full">
                                <input type="checkbox" name="show_results_immediately" id="show_results_immediately" value="1" {{ old('show_results_immediately', $exam->show_results_immediately) ? 'checked' : '' }}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                                <label for="show_results_immediately" class="ml-2 text-sm text-gray-700">Tampilkan hasil langsung</label>
                            </div>

                            <div class="flex items-center w-full">
                                <input type="checkbox" name="certificate_enabled" id="certificate_enabled" value="1" {{ old('certificate_enabled', $exam->certificate_enabled) ? 'checked' : '' }}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                                <label for="certificate_enabled" class="ml-2 text-sm text-gray-700">Berikan sertifikat</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Questions Section -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Soal Ujian ({{ $exam->questions->count() }})</h2>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="showImportModal()"
                                    class="btn btn-sm btn-outline">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                </svg>
                                Import CSV
                            </button>
                            <button type="button" onclick="addQuestion()"
                                    class="btn btn-sm bg-emerald-600 hover:bg-emerald-700 text-white">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Tambah Soal
                            </button>
                        </div>
                    </div>

                    <div id="questions_container" class="space-y-4">
                        <!-- Existing questions will be loaded here -->
                        @foreach($exam->questions as $index => $question)
                            <div class="border border-gray-200 rounded-lg p-4" id="question_{{ $index + 1 }}">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Soal {{ $index + 1 }}</h4>
                                    <button type="button" onclick="removeQuestion({{ $index + 1 }})" class="text-red-600 hover:text-red-800">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan *</label>
                                        <textarea name="questions[{{ $index + 1 }}][question]" rows="3" required
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                  placeholder="Masukkan pertanyaan">{{ $question->question }}</textarea>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                                            <select name="questions[{{ $index + 1 }}][type]" onchange="toggleQuestionOptions({{ $index + 1 }})"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                                <option value="multiple_choice" {{ $question->type == 'multiple_choice' ? 'selected' : '' }}>Pilihan Ganda</option>
                                                <option value="true_false" {{ $question->type == 'true_false' ? 'selected' : '' }}>Benar/Salah</option>
                                                <option value="short_answer" {{ $question->type == 'short_answer' ? 'selected' : '' }}>Jawaban Singkat</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                                            <input type="number" name="questions[{{ $index + 1 }}][points]" min="1" max="100" value="{{ $question->points }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                        </div>
                                    </div>

                                    @if($question->type === 'multiple_choice' || $question->type === 'true_false')
                                        <div id="options_{{ $index + 1 }}">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                                            <div class="space-y-2">
                                                @foreach($question->options as $optionIndex => $option)
                                                    <div class="flex items-center space-x-2" {{ $question->type === 'true_false' && $optionIndex > 1 ? 'style=display:none;' : '' }}>
                                                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">{{ chr(65 + $optionIndex) }}</span>
                                                        <input type="text" name="questions[{{ $index + 1 }}][options][]" value="{{ $option->option_text }}"
                                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                               placeholder="Pilihan {{ chr(65 + $optionIndex) }}">
                                                    </div>
                                                @endforeach
                                                @for($i = $question->options->count(); $i < 4; $i++)
                                                    <div class="flex items-center space-x-2" {{ $question->type === 'true_false' && $i > 1 ? 'style=display:none;' : '' }}>
                                                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">{{ chr(65 + $i) }}</span>
                                                        <input type="text" name="questions[{{ $index + 1 }}][options][]"
                                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                               placeholder="Pilihan {{ chr(65 + $i) }}">
                                                    </div>
                                                @endfor
                                            </div>
                                        </div>

                                        <div id="correct_answer_{{ $index + 1 }}">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Jawaban Benar</label>
                                            <select name="questions[{{ $index + 1 }}][correct_answer]"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                                @if($question->type === 'multiple_choice')
                                                    @foreach($question->options as $optionIndex => $option)
                                                        <option value="{{ chr(65 + $optionIndex) }}" {{ $option->is_correct ? 'selected' : '' }}>{{ chr(65 + $optionIndex) }}</option>
                                                    @endforeach
                                                @else
                                                    @php
                                                        $correctOption = $question->options->where('is_correct', true)->first();
                                                        $correctAnswer = $correctOption ? ($question->options->search($correctOption) == 0 ? 'A' : 'B') : 'A';
                                                    @endphp
                                                    <option value="A" {{ $correctAnswer === 'A' ? 'selected' : '' }}>Benar</option>
                                                    <option value="B" {{ $correctAnswer === 'B' ? 'selected' : '' }}>Salah</option>
                                                @endif
                                            </select>
                                        </div>
                                    @else
                                        <div id="options_{{ $index + 1 }}" style="display: none;"></div>
                                        <div id="correct_answer_{{ $index + 1 }}" style="display: none;"></div>
                                    @endif

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                                        <textarea name="questions[{{ $index + 1 }}][explanation]" rows="2"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                  placeholder="Penjelasan jawaban yang benar">{{ $question->explanation }}</textarea>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi</h3>
                    <div class="space-y-3">
                        <button type="submit" class="w-full btn bg-emerald-600 hover:bg-emerald-700 text-white">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Perubahan
                        </button>
                        <a href="{{ route('tutor.exams.show', $exam) }}" class="w-full btn btn-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                    </div>
                </div>

                <!-- Current Stats -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistik Saat Ini</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Total Soal</span>
                            <span class="text-sm font-medium text-gray-900">{{ $exam->questions->count() }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Total Poin</span>
                            <span class="text-sm font-medium text-gray-900">{{ $exam->questions->sum('points') }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Status</span>
                            <span class="text-sm font-medium {{ $exam->is_published ? 'text-green-600' : 'text-yellow-600' }}">
                                {{ $exam->is_published ? 'Terpublikasi' : 'Draft' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Help -->
                <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg shadow-sm p-6 border border-emerald-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Tips Edit Ujian</h3>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Simpan perubahan secara berkala
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Gunakan CSV untuk menambah soal banyak
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Periksa kembali jawaban yang benar
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Berikan penjelasan untuk setiap soal
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Import CSV Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Import Soal dari CSV</h3>
                    <button type="button" onclick="hideImportModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Download Template -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">1. Download Template CSV</label>
                    <a href="{{ route('tutor.exams.download-template') }}"
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Template
                    </a>
                </div>

                <!-- Upload CSV -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">2. Upload File CSV</label>
                    <input type="file" id="csvFile" accept=".csv" onchange="handleCSVUpload(event)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                </div>

                <!-- Preview -->
                <div id="csvPreview" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-3">3. Preview Soal</label>
                    <div id="csvPreviewContent" class="max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4 mb-4">
                        <!-- Preview content will be inserted here -->
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideImportModal()" class="btn btn-outline">Batal</button>
                        <button type="button" onclick="importQuestions()" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">Import Soal</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let questionCounter = {{ $exam->questions->count() }};
let csvData = [];

function addQuestion() {
    questionCounter++;
    const container = document.getElementById('questions_container');
    const questionDiv = document.createElement('div');
    questionDiv.className = 'border border-gray-200 rounded-lg p-4';
    questionDiv.id = `question_${questionCounter}`;

    questionDiv.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium text-gray-900">Soal ${questionCounter}</h4>
            <button type="button" onclick="removeQuestion(${questionCounter})" class="text-red-600 hover:text-red-800">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan *</label>
                <textarea name="questions[${questionCounter}][question]" rows="3" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          placeholder="Masukkan pertanyaan"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                    <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        <option value="multiple_choice">Pilihan Ganda</option>
                        <option value="true_false">Benar/Salah</option>
                        <option value="short_answer">Jawaban Singkat</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                    <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                </div>
            </div>

            <div id="options_${questionCounter}">
                <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">A</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                               placeholder="Pilihan A">
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">B</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                               placeholder="Pilihan B">
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">C</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                               placeholder="Pilihan C">
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">D</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                               placeholder="Pilihan D">
                    </div>
                </div>
            </div>

            <div id="correct_answer_${questionCounter}">
                <label class="block text-sm font-medium text-gray-700 mb-2">Jawaban Benar</label>
                <select name="questions[${questionCounter}][correct_answer]"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                <textarea name="questions[${questionCounter}][explanation]" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                          placeholder="Penjelasan jawaban yang benar"></textarea>
            </div>
        </div>
    `;

    container.appendChild(questionDiv);
}

function removeQuestion(questionNumber) {
    const questionDiv = document.getElementById(`question_${questionNumber}`);
    if (questionDiv) {
        questionDiv.remove();
    }
}

function toggleQuestionOptions(questionNumber) {
    const typeSelect = document.querySelector(`select[name="questions[${questionNumber}][type]"]`);
    const optionsDiv = document.getElementById(`options_${questionNumber}`);
    const correctAnswerDiv = document.getElementById(`correct_answer_${questionNumber}`);

    if (typeSelect.value === 'short_answer') {
        optionsDiv.style.display = 'none';
        correctAnswerDiv.style.display = 'none';
    } else {
        optionsDiv.style.display = 'block';
        correctAnswerDiv.style.display = 'block';

        if (typeSelect.value === 'true_false') {
            // Hide options C and D for true/false questions
            const optionInputs = optionsDiv.querySelectorAll('.flex.items-center.space-x-2');
            optionInputs.forEach((input, index) => {
                if (index > 1) {
                    input.style.display = 'none';
                } else {
                    input.style.display = 'flex';
                }
            });

            // Update correct answer options
            const correctSelect = correctAnswerDiv.querySelector('select');
            correctSelect.innerHTML = `
                <option value="A">Benar</option>
                <option value="B">Salah</option>
            `;

            // Set default values for true/false
            const optionA = optionInputs[0]?.querySelector('input');
            const optionB = optionInputs[1]?.querySelector('input');
            if (optionA && !optionA.value) optionA.value = 'Benar';
            if (optionB && !optionB.value) optionB.value = 'Salah';
        } else {
            // Show all options for multiple choice
            const optionInputs = optionsDiv.querySelectorAll('.flex.items-center.space-x-2');
            optionInputs.forEach(input => {
                input.style.display = 'flex';
            });

            // Update correct answer options
            const correctSelect = correctAnswerDiv.querySelector('select');
            correctSelect.innerHTML = `
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="C">C</option>
                <option value="D">D</option>
            `;
        }
    }
}

function showImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

function hideImportModal() {
    document.getElementById('importModal').classList.add('hidden');
    document.getElementById('csvPreview').classList.add('hidden');
    document.getElementById('csvFile').value = '';
    csvData = [];
}

function handleCSVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());

        // Validate headers
        const requiredHeaders = ['question', 'type', 'option_a', 'option_b', 'correct_answer', 'points'];
        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

        if (missingHeaders.length > 0) {
            alert(`Header yang hilang: ${missingHeaders.join(', ')}`);
            return;
        }

        csvData = [];
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim() === '') continue;

            const values = lines[i].split(',').map(v => v.trim());
            const question = {};

            headers.forEach((header, index) => {
                question[header] = values[index] || '';
            });

            if (question.question) {
                csvData.push(question);
            }
        }

        displayCSVPreview();
    };

    reader.readAsText(file);
}

function displayCSVPreview() {
    const previewDiv = document.getElementById('csvPreviewContent');
    let html = '';

    csvData.forEach((question, index) => {
        html += `
            <div class="border-b border-gray-200 pb-3 mb-3">
                <h4 class="font-medium text-gray-900 mb-2">Soal ${index + 1}</h4>
                <p class="text-sm text-gray-700 mb-2">${question.question}</p>
                <p class="text-xs text-gray-500">Tipe: ${question.type} | Poin: ${question.points} | Jawaban: ${question.correct_answer}</p>
            </div>
        `;
    });

    previewDiv.innerHTML = html;
    document.getElementById('csvPreview').classList.remove('hidden');
}

function importQuestions() {
    csvData.forEach(question => {
        questionCounter++;
        const container = document.getElementById('questions_container');
        const questionDiv = document.createElement('div');
        questionDiv.className = 'border border-gray-200 rounded-lg p-4';
        questionDiv.id = `question_${questionCounter}`;

        const isMultipleChoice = question.type === 'multiple_choice';
        const isTrueFalse = question.type === 'true_false';

        let optionsHtml = '';
        let correctAnswerHtml = '';

        if (isMultipleChoice || isTrueFalse) {
            const options = ['A', 'B', 'C', 'D'];
            const optionKeys = ['option_a', 'option_b', 'option_c', 'option_d'];

            optionsHtml = `
                <div id="options_${questionCounter}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                    <div class="space-y-2">
            `;

            options.forEach((option, index) => {
                const value = question[optionKeys[index]] || '';
                const display = isTrueFalse && index > 1 ? 'style="display:none;"' : '';
                optionsHtml += `
                    <div class="flex items-center space-x-2" ${display}>
                        <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">${option}</span>
                        <input type="text" name="questions[${questionCounter}][options][]" value="${value}"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                               placeholder="Pilihan ${option}">
                    </div>
                `;
            });

            optionsHtml += '</div></div>';

            correctAnswerHtml = `
                <div id="correct_answer_${questionCounter}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Jawaban Benar</label>
                    <select name="questions[${questionCounter}][correct_answer]"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
            `;

            if (isTrueFalse) {
                correctAnswerHtml += `
                    <option value="A" ${question.correct_answer === 'A' ? 'selected' : ''}>Benar</option>
                    <option value="B" ${question.correct_answer === 'B' ? 'selected' : ''}>Salah</option>
                `;
            } else {
                options.forEach(option => {
                    correctAnswerHtml += `<option value="${option}" ${question.correct_answer === option ? 'selected' : ''}>${option}</option>`;
                });
            }

            correctAnswerHtml += '</select></div>';
        } else {
            optionsHtml = '<div id="options_' + questionCounter + '" style="display: none;"></div>';
            correctAnswerHtml = '<div id="correct_answer_' + questionCounter + '" style="display: none;"></div>';
        }

        questionDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">Soal ${questionCounter}</h4>
                <button type="button" onclick="removeQuestion(${questionCounter})" class="text-red-600 hover:text-red-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan *</label>
                    <textarea name="questions[${questionCounter}][question]" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                              placeholder="Masukkan pertanyaan">${question.question}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                        <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <option value="multiple_choice" ${question.type === 'multiple_choice' ? 'selected' : ''}>Pilihan Ganda</option>
                            <option value="true_false" ${question.type === 'true_false' ? 'selected' : ''}>Benar/Salah</option>
                            <option value="short_answer" ${question.type === 'short_answer' ? 'selected' : ''}>Jawaban Singkat</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                        <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="${question.points || 10}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    </div>
                </div>

                ${optionsHtml}
                ${correctAnswerHtml}

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                    <textarea name="questions[${questionCounter}][explanation]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                              placeholder="Penjelasan jawaban yang benar">${question.explanation || ''}</textarea>
                </div>
            </div>
        `;

        container.appendChild(questionDiv);
    });

    hideImportModal();
    alert(`${csvData.length} soal berhasil diimport!`);
}

function toggleExamPriceField() {
    const examType = document.querySelector('input[name="exam_type"]:checked').value;
    const priceField = document.getElementById('exam-price-field');
    const priceInput = document.getElementById('price');

    if (examType === 'free') {
        priceField.classList.add('hidden');
        priceInput.value = 0;
    } else {
        priceField.classList.remove('hidden');
        if (priceInput.value == 0) {
            priceInput.value = '';
        }
    }
}
</script>
@endpush

@endsection