<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Exam extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tutor_id',
        'title',
        'description',
        'price',
        'time_limit',
        'max_attempts',
        'passing_score',
        'shuffle_questions',
        'show_results_immediately',
        'is_published',
        'category_id',
        'difficulty_level',
        'instructions',
        'certificate_enabled',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'time_limit' => 'integer',
        'max_attempts' => 'integer',
        'passing_score' => 'integer',
        'shuffle_questions' => 'boolean',
        'show_results_immediately' => 'boolean',
        'is_published' => 'boolean',
        'certificate_enabled' => 'boolean',
    ];

    /**
     * Get the tutor that owns the exam.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    /**
     * Get the category that the exam belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the questions for the exam.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(ExamQuestion::class)->orderBy('sort_order');
    }

    /**
     * Get the attempts for the exam.
     */
    public function attempts(): HasMany
    {
        return $this->hasMany(ExamAttempt::class);
    }

    /**
     * Get the enrollments for the exam.
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(ExamEnrollment::class);
    }

    /**
     * Scope a query to only include published exams.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Get the total number of questions.
     */
    public function getTotalQuestionsAttribute()
    {
        return $this->questions()->count();
    }

    /**
     * Get the total points for the exam.
     */
    public function getTotalPointsAttribute()
    {
        return $this->questions()->sum('points');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        if ($this->price == 0) {
            return 'Gratis';
        }
        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get the difficulty level label.
     */
    public function getDifficultyLabelAttribute()
    {
        return match($this->difficulty_level) {
            'beginner' => 'Pemula',
            'intermediate' => 'Menengah',
            'advanced' => 'Lanjutan',
            default => 'Tidak Ditentukan'
        };
    }

    /**
     * Check if user has enrolled in this exam.
     */
    public function isEnrolledBy($user)
    {
        if (!$user) return false;
        return $this->enrollments()->where('user_id', $user->id)->exists();
    }

    /**
     * Get user's enrollment for this exam.
     */
    public function getEnrollmentFor($user)
    {
        if (!$user) return null;
        return $this->enrollments()->where('user_id', $user->id)->first();
    }

    /**
     * Check if user can take this exam.
     */
    public function canBeTakenBy($user)
    {
        if (!$user) return false;
        
        $enrollment = $this->getEnrollmentFor($user);
        if (!$enrollment) return false;

        $attempts = $this->attempts()->where('user_id', $user->id)->count();
        return $attempts < $this->max_attempts;
    }
}
