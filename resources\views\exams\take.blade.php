@extends('layouts.exam')

@section('title', '<PERSON><PERSON><PERSON><PERSON>: ' . $exam->title . ' - Ngambiskuy')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Minimal Header with Timer -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <!-- Ngambis<PERSON>y Logo -->
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">N</span>
                        </div>
                        <span class="ml-2 text-lg font-semibold text-gray-900">{{ $exam->title }}</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        <PERSON><PERSON><PERSON><PERSON> {{ $attempt->attempt_number }} dari {{ $exam->max_attempts }}
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Timer -->
                    <div class="bg-red-50 border border-red-200 rounded-lg px-4 py-2">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-mono text-lg font-semibold text-red-600" id="timer">
                                {{ gmdate('H:i:s', $timeRemaining) }}
                            </span>
                        </div>
                    </div>
                    <!-- Submit Button -->
                    <button type="button" onclick="submitExam()" class="btn bg-green-600 hover:bg-green-700 text-white">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Selesai Ujian
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 py-8">
        <form id="examForm" action="{{ route('exams.submit', $exam) }}" method="POST">
            @csrf
            <input type="hidden" name="attempt_id" value="{{ $attempt->id }}">

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Current Question -->
                <div class="lg:col-span-3">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                        <!-- Question Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <span class="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                                    {{ $currentQuestionIndex }}
                                </span>
                                <div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ ucfirst(str_replace('_', ' ', $currentQuestion->type)) }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                        {{ $currentQuestion->points }} poin
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Question Text -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">
                                {{ $currentQuestion->question }}
                            </h3>
                        </div>

                        <!-- Answer Options -->
                        @if($currentQuestion->type === 'multiple_choice' || $currentQuestion->type === 'true_false')
                            <div class="space-y-3">
                                @foreach($currentQuestion->options as $optionIndex => $option)
                                    <label class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                        <input type="radio"
                                               name="selected_option"
                                               value="{{ $option->id }}"
                                               class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                               {{ isset($existingAnswers[$currentQuestion->id]) && (string)$existingAnswers[$currentQuestion->id] === (string)$option->id ? 'checked' : '' }}
                                               onchange="saveCurrentAnswer()">
                                        <div class="ml-3 flex-1">
                                            <div class="flex items-center">
                                                <span class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                                                    {{ chr(65 + $optionIndex) }}
                                                </span>
                                                <span class="text-gray-900">{{ $option->option_text }}</span>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                        @elseif($currentQuestion->type === 'short_answer')
                            <div>
                                <textarea name="answer_text"
                                          rows="4"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                          placeholder="Masukkan jawaban Anda..."
                                          onchange="saveCurrentAnswer()">{{ $existingAnswers[$currentQuestion->id] ?? '' }}</textarea>
                            </div>
                        @endif

                        <!-- Question Navigation -->
                        <div class="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                            <div class="flex items-center space-x-2">
                                @if($currentQuestionIndex > 1)
                                    <button type="button" onclick="navigateToQuestion({{ $currentQuestionIndex - 1 }})"
                                            class="btn btn-sm btn-outline">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                        Sebelumnya
                                    </button>
                                @endif
                                @if($currentQuestionIndex < $questions->count())
                                    <button type="button" onclick="navigateToQuestion({{ $currentQuestionIndex + 1 }})"
                                            class="btn btn-sm bg-blue-600 hover:bg-blue-700 text-white">
                                        Selanjutnya
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">
                                Soal {{ $currentQuestionIndex }} dari {{ $questions->count() }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar - Question Navigator -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 sticky top-24">
                        <h3 class="font-semibold text-gray-900 mb-4">Navigasi Soal</h3>

                        <!-- Progress -->
                        <div class="mb-4">
                            @php
                                $answeredCount = collect($existingAnswers)->filter(function($answer) {
                                    return $answer !== null && $answer !== '';
                                })->count();
                                $progressPercentage = $questions->count() > 0 ? ($answeredCount / $questions->count()) * 100 : 0;
                            @endphp
                            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                                <span>Progress</span>
                                <span id="progress-text">{{ $answeredCount }} / {{ $questions->count() }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: {{ $progressPercentage }}%"></div>
                            </div>

                            {{-- Debug info (remove in production) --}}
                            @if(config('app.debug'))
                                <div class="text-xs text-gray-500 mt-2">
                                    Debug: {{ count($existingAnswers) }} answers found
                                </div>
                            @endif
                        </div>

                        <!-- Question Grid -->
                        <div class="grid grid-cols-5 gap-2 mb-6">
                            @foreach($questions as $index => $question)
                                @php
                                    $isAnswered = isset($existingAnswers[$question->id]) &&
                                                 ($existingAnswers[$question->id] !== null &&
                                                  $existingAnswers[$question->id] !== '');
                                    $isCurrent = ($index + 1) == $currentQuestionIndex;
                                @endphp
                                <button type="button" onclick="navigateToQuestion({{ $index + 1 }})"
                                        class="w-8 h-8 rounded text-xs font-medium border transition-colors flex items-center justify-center
                                               @if($isCurrent)
                                                   bg-blue-100 border-blue-500 text-blue-700
                                               @elseif($isAnswered)
                                                   bg-green-100 border-green-300 text-green-700
                                               @else
                                                   bg-gray-200 border-gray-300 hover:border-blue-500
                                               @endif">
                                    {{ $index + 1 }}
                                </button>
                            @endforeach
                        </div>

                        <!-- Legend -->
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-gray-200 border border-gray-300 rounded mr-2"></div>
                                <span class="text-gray-600">Belum dijawab</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
                                <span class="text-gray-600">Sudah dijawab</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-blue-100 border border-blue-500 rounded mr-2"></div>
                                <span class="text-gray-600">Sedang dilihat</span>
                            </div>
                        </div>

                        <!-- Submit Section -->
                        <div class="mt-6 pt-4 border-t border-gray-200">
                            <button type="button" onclick="submitExam()" class="w-full btn bg-green-600 hover:bg-green-700 text-white">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai Ujian
                            </button>
                            <p class="text-xs text-gray-500 mt-2 text-center">
                                Pastikan semua soal sudah dijawab
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
let timeRemaining = {{ $timeRemaining }};
let totalQuestions = {{ $questions->count() }};
let currentQuestionId = {{ $currentQuestion->id }};

@php
    $answeredCount = collect($existingAnswers)->filter(function($answer) {
        return $answer !== null && $answer !== '';
    })->count();
@endphp
let answeredCount = {{ $answeredCount }};

// Timer functionality
function updateTimer() {
    try {
        if (timeRemaining <= 0) {
            // Time's up - auto submit
            alert('Waktu ujian telah habis! Ujian akan otomatis diselesaikan.');
            document.getElementById('examForm').submit();
            return;
        }

        const hours = Math.floor(timeRemaining / 3600);
        const minutes = Math.floor((timeRemaining % 3600) / 60);
        const seconds = timeRemaining % 60;

        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.textContent = timeString;
        }

        // Change color when time is running low
        const timerContainer = timerElement ? timerElement.closest('.bg-red-50') : null;

        if (timerContainer && timeRemaining <= 300) { // 5 minutes
            timerContainer.className = 'bg-red-100 border border-red-300 rounded-lg px-4 py-2';
            timerElement.className = 'font-mono text-lg font-semibold text-red-700';
        } else if (timerContainer && timeRemaining <= 600) { // 10 minutes
            timerContainer.className = 'bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-2';
            timerElement.className = 'font-mono text-lg font-semibold text-yellow-700';
        }

        timeRemaining--;
    } catch (error) {
        console.error('Timer error:', error);
    }
}

// Start timer
setInterval(updateTimer, 1000);

// Save current answer via AJAX
function saveCurrentAnswer() {
    return new Promise((resolve, reject) => {
        const selectedOption = document.querySelector('input[name="selected_option"]:checked');
        const answerText = document.querySelector('textarea[name="answer_text"]');

        let data = {
            question_id: currentQuestionId,
            _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        };

        if (selectedOption) {
            data.selected_option_id = selectedOption.value;
        }

        if (answerText && answerText.value.trim()) {
            data.answer_text = answerText.value.trim();
        }

        // Always save, even if empty (to track that user visited this question)
        fetch(`{{ route('exams.save-answer', $exam) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': data._token
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Answer saved successfully');
                resolve(data);
            } else {
                reject('Failed to save answer');
            }
        })
        .catch(error => {
            console.error('Error saving answer:', error);
            reject(error);
        });
    });
}

// Navigate to specific question with auto-save
function navigateToQuestion(questionNumber) {
    try {
        console.log('Navigating to question:', questionNumber);
        // Save current answer first
        saveCurrentAnswer().then(() => {
            // Navigate to the question
            window.location.href = `{{ route('exams.take', $exam) }}?question=${questionNumber}`;
        }).catch(error => {
            console.error('Error saving before navigation:', error);
            // Navigate anyway, but warn user
            if (confirm('Terjadi kesalahan saat menyimpan jawaban. Lanjutkan navigasi?')) {
                window.location.href = `{{ route('exams.take', $exam) }}?question=${questionNumber}`;
            }
        });
    } catch (error) {
        console.error('Navigation error:', error);
        // Fallback navigation
        window.location.href = `{{ route('exams.take', $exam) }}?question=${questionNumber}`;
    }
}

// Submit exam
function submitExam() {
    try {
        console.log('Submitting exam...');
        // Save current answer before submitting
        saveCurrentAnswer();

        // Get answered questions count from server-side data
        const unanswered = totalQuestions - answeredCount;

        if (unanswered > 0) {
            const confirmMessage = `Anda masih memiliki ${unanswered} soal yang belum dijawab. Apakah Anda yakin ingin menyelesaikan ujian?`;
            if (!confirm(confirmMessage)) {
                return;
            }
        } else {
            if (!confirm('Apakah Anda yakin ingin menyelesaikan ujian? Anda tidak dapat mengubah jawaban setelah ini.')) {
                return;
            }
        }

        // Disable submit button to prevent double submission
        const submitButtons = document.querySelectorAll('button[onclick="submitExam()"]');
        submitButtons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Menyimpan...';
        });

        // Wait a moment for the AJAX save to complete, then submit
        setTimeout(() => {
            const form = document.getElementById('examForm');
            if (form) {
                form.submit();
            } else {
                console.error('Exam form not found');
            }
        }, 500);
    } catch (error) {
        console.error('Submit exam error:', error);
        alert('Terjadi kesalahan saat mengirim ujian. Silakan coba lagi.');
    }
}

// Auto-save when user navigates away from page
window.addEventListener('beforeunload', function(e) {
    saveCurrentAnswer();
    e.preventDefault();
    e.returnValue = 'Anda yakin ingin meninggalkan halaman? Progress ujian mungkin akan hilang.';
});

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Exam page loaded successfully');
    console.log('Timer remaining:', timeRemaining);
    console.log('Total questions:', totalQuestions);
    console.log('Current question ID:', currentQuestionId);
    console.log('Answered count:', answeredCount);

    // Test if timer element exists
    const timerElement = document.getElementById('timer');
    if (!timerElement) {
        console.error('Timer element not found!');
    }

    // Test if form exists
    const examForm = document.getElementById('examForm');
    if (!examForm) {
        console.error('Exam form not found!');
    }
});
</script>

