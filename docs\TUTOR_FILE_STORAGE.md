# Tutor File Storage System

## Overview
This document explains the secure file storage system for tutor registration documents and how to manage them properly.

## Storage Structure

### Private Files (Not Publicly Accessible)
**Location:** `storage/app/tutor/{tutor_id}/{type}/`

```
storage/app/
├── tutor/
│   └── {tutor_id}/
│       ├── ktp/           # Identity documents (KTP/SIM/Passport)
│       ├── portfolio/     # Portfolio/Resume PDFs
│       └── npwp/          # NPWP photos
└── private/               # Other private files
```

### Public Files (Accessible via URL)
**Location:** `storage/app/public/user/{user_id}/`

```
storage/app/public/
└── user/
    └── {user_id}/
        └── profile/       # Profile pictures
```

## Security Features

### 1. Private File Access Control
- **Authentication Required:** Only logged-in users can access files
- **Authorization:** Only the file owner or admins can view files
- **Secure Routes:** Files served through protected controllers, not direct URLs

### 2. File Validation
- **Size Limits:** 2MB maximum for all files
- **Format Validation:** 
  - Images: JPG, PNG only
  - Documents: PDF only
- **Real-time Validation:** Client-side and server-side checks

### 3. Secure File Serving
```php
// Secure file access routes
Route::get('/secure/tutor/{tutorId}/{type}/{filename}', [SecureFileController::class, 'serveTutorFile'])
    ->name('secure.tutor.file')
    ->middleware('auth');
```

## File Types

### Identity Documents (KTP)
- **Purpose:** Government ID verification
- **Formats:** JPG, PNG
- **Max Size:** 2MB
- **Storage:** `storage/app/tutor/{tutor_id}/ktp/`
- **Access:** Tutor owner + Admins only

### Portfolio/Resume
- **Purpose:** Professional background verification
- **Formats:** PDF only
- **Max Size:** 2MB
- **Storage:** `storage/app/tutor/{tutor_id}/portfolio/`
- **Access:** Tutor owner + Admins only

### NPWP Documents
- **Purpose:** Tax ID verification (optional)
- **Formats:** JPG, PNG
- **Max Size:** 2MB
- **Storage:** `storage/app/tutor/{tutor_id}/npwp/`
- **Access:** Tutor owner + Admins only

## Usage Examples

### Uploading Files
```php
// In TutorRegistrationController
$data['identity_photo_path'] = FileStorageService::storePrivateTutorFile(
    $request->file('identity_photo'),
    $profile->id,
    'ktp'
);
```

### Accessing Files Securely
```php
// Generate secure URL
$url = route('secure.tutor.file', [
    'tutorId' => $profile->id,
    'type' => 'ktp',
    'filename' => basename($profile->identity_photo_path)
]);
```

### Checking File Existence
```php
// Check if file exists
$exists = FileStorageService::privateFileExists($profile->identity_photo_path);
```

## Maintenance Commands

### Setup Storage Directories
```bash
php artisan storage:setup
```

### Check File Integrity
```bash
# Check for missing files
php artisan tutor:check-files

# Fix missing file references
php artisan tutor:check-files --fix
```

### Create Storage Link
```bash
php artisan storage:link
```

## File Naming Convention

Files are automatically renamed using this pattern:
```
{timestamp}_{random_string}.{extension}
```

Example: `2025-05-31_08-48-36_2hrOkTxs.png`

This ensures:
- **Uniqueness:** No file conflicts
- **Security:** Original filenames are hidden
- **Organization:** Chronological sorting

## Error Handling

### Missing Files
If files are referenced in database but missing from storage:
1. Run `php artisan tutor:check-files` to identify issues
2. Run `php artisan tutor:check-files --fix` to clean up references
3. Users will need to re-upload missing files

### Permission Issues
Ensure proper directory permissions:
- **Linux/Mac:** `chmod 755 storage/`
- **Windows:** Handled automatically

## Best Practices

### 1. Regular Backups
- Backup `storage/app/tutor/` directory regularly
- Include database backup for file references

### 2. Monitoring
- Monitor storage usage
- Check for orphaned files periodically
- Validate file integrity regularly

### 3. Security
- Never serve private files directly via web server
- Always use the secure file controller
- Validate user permissions before serving files

### 4. Performance
- Use streaming responses for large files
- Implement proper caching headers
- Consider CDN for public files

## Troubleshooting

### Files Not Uploading
1. Check directory permissions
2. Verify storage disk configuration
3. Check file size limits in PHP configuration
4. Run `php artisan storage:setup`

### Files Not Accessible
1. Verify authentication
2. Check user permissions
3. Ensure file exists in storage
4. Check route configuration

### Database Inconsistencies
1. Run `php artisan tutor:check-files`
2. Fix missing references with `--fix` flag
3. Re-upload missing files through the interface

## Configuration

### Storage Disks
```php
// config/filesystems.php
'local' => [
    'driver' => 'local',
    'root' => storage_path('app'),
],

'public' => [
    'driver' => 'local',
    'root' => storage_path('app/public'),
    'url' => env('APP_URL').'/storage',
    'visibility' => 'public',
],
```

### File Upload Limits
```php
// php.ini
upload_max_filesize = 2M
post_max_size = 2M
max_execution_time = 30
```
