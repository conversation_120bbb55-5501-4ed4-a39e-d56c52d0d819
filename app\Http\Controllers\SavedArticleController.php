<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SavedArticle;
use App\Models\BlogPost;
use Illuminate\Support\Facades\Auth;

class SavedArticleController extends Controller
{
    /**
     * Save an article for the authenticated user.
     */
    public function store(Request $request, BlogPost $blogPost)
    {
        $user = Auth::user();

        // Check if already saved
        $existingSave = SavedArticle::where('user_id', $user->id)
            ->where('blog_post_id', $blogPost->id)
            ->first();

        if ($existingSave) {
            return response()->json([
                'success' => false,
                'message' => 'Artikel sudah disimpan sebelumnya.'
            ], 409);
        }

        // Save the article
        SavedArticle::create([
            'user_id' => $user->id,
            'blog_post_id' => $blogPost->id,
            'saved_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Artikel berhasil disimpan!'
        ]);
    }

    /**
     * Remove a saved article for the authenticated user.
     */
    public function destroy(BlogPost $blogPost)
    {
        $user = Auth::user();

        $savedArticle = SavedArticle::where('user_id', $user->id)
            ->where('blog_post_id', $blogPost->id)
            ->first();

        if (!$savedArticle) {
            return response()->json([
                'success' => false,
                'message' => 'Artikel tidak ditemukan dalam daftar simpan.'
            ], 404);
        }

        $savedArticle->delete();

        return response()->json([
            'success' => true,
            'message' => 'Artikel berhasil dihapus dari daftar simpan!'
        ]);
    }

    /**
     * Check if an article is saved by the authenticated user.
     */
    public function check(BlogPost $blogPost)
    {
        $user = Auth::user();

        $isSaved = SavedArticle::where('user_id', $user->id)
            ->where('blog_post_id', $blogPost->id)
            ->exists();

        return response()->json([
            'is_saved' => $isSaved
        ]);
    }
}
