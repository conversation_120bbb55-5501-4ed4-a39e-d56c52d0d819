<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_question_options', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('question_id'); // Foreign key to exam_questions table

            // Option Information
            $table->text('option_text'); // Option text
            $table->boolean('is_correct')->default(false); // Whether this is the correct answer
            $table->integer('sort_order')->default(0); // Order within question

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('question_id')->references('id')->on('exam_questions')->onDelete('cascade');

            // Indexes
            $table->index('question_id');
            $table->index('sort_order');
            $table->index('is_correct');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_question_options');
    }
};
