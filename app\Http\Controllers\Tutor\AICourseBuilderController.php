<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Category;

class AICourseBuilderController extends Controller
{
    private $geminiApiKey;
    private $geminiModel = 'gemini-1.5-flash';

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key');
    }

    /**
     * Generate course suggestions using AI
     */
    public function generateCourseSuggestion(Request $request)
    {
        try {
            // Get available categories
            $categories = Category::where('is_active', true)
                ->orderBy('sort_order')
                ->get(['id', 'name', 'slug']);

            // Generate course suggestion using AI
            $courseSuggestion = $this->generateAICourseSuggestion($categories);

            return response()->json([
                'success' => true,
                'course_suggestion' => $courseSuggestion
            ]);

        } catch (\Exception $e) {
            Log::error('AI Course Builder Error: ' . $e->getMessage());
            Log::error('AI Course Builder Stack Trace: ' . $e->getTraceAsString());

            // Fallback to predefined suggestions if AI fails
            $fallbackSuggestion = $this->getFallbackCourseSuggestion();

            return response()->json([
                'success' => true,
                'course_suggestion' => $fallbackSuggestion,
                'is_fallback' => true,
                'error_message' => $e->getMessage() // For debugging
            ]);
        }
    }

    /**
     * Generate AI course suggestion
     */
    private function generateAICourseSuggestion($categories)
    {
        $categoryList = $categories->pluck('name')->implode(', ');
        
        $systemPrompt = "Anda adalah AI assistant yang membantu tutor membuat kursus teknologi berkualitas tinggi.

KONTEKS PLATFORM:
Ngambiskuy adalah platform edukasi teknologi Indonesia yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

TUGAS:
Buatkan saran kursus yang menarik, praktis, dan sesuai dengan tren industri tech 2024.

KATEGORI TERSEDIA: {$categoryList}

FORMAT RESPONSE (JSON):
{
    \"title\": \"Judul kursus yang menarik dan spesifik\",
    \"category\": \"Nama kategori yang sesuai dari daftar di atas\",
    \"description\": \"Deskripsi 100-150 kata yang menjelaskan manfaat konkret\",
    \"level\": \"beginner/intermediate/advanced\",
    \"duration\": \"Durasi dalam jam (20-100)\",
    \"price\": \"Harga dalam rupiah (************, kelipatan 1000)\",
    \"is_free\": false,
    \"learning_outcomes\": [\"Outcome 1\", \"Outcome 2\", \"Outcome 3\"],
    \"target_audience\": [\"Target 1\", \"Target 2\", \"Target 3\"],
    \"requirements\": [\"Requirement 1\", \"Requirement 2\"]
}

PENTING:
- Judul harus spesifik dan menarik (contoh: 'Membangun REST API dengan Laravel untuk E-commerce')
- Deskripsi harus fokus pada manfaat praktis dan hasil yang bisa dicapai
- Learning outcomes harus konkret dan terukur
- Harga sesuai dengan kompleksitas dan durasi
- Semua konten harus dalam Bahasa Indonesia
- Fokus pada skill yang dibutuhkan industri tech Indonesia";

        $userPrompt = "Buatkan saran kursus teknologi yang menarik dan sesuai dengan tren industri 2024. Pastikan kursus ini praktis dan memberikan value tinggi untuk siswa.";

        $response = $this->callGeminiAPI($systemPrompt, $userPrompt);

        // Clean and parse JSON response (remove markdown code blocks if present)
        $cleanResponse = $this->cleanJsonResponse($response);
        $courseData = json_decode($cleanResponse, true);

        if (!$courseData) {
            throw new \Exception('Invalid JSON response from AI: ' . $cleanResponse);
        }

        // Validate and clean the response
        return $this->validateAndCleanCourseData($courseData, $categories);
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 1024,
            ]
        ];

        $response = Http::timeout(20)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Clean JSON response by removing markdown code blocks
     */
    private function cleanJsonResponse($response)
    {
        // Remove markdown code blocks (```json ... ```)
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);
        $response = preg_replace('/```/', '', $response);

        // Remove any leading/trailing whitespace
        $response = trim($response);

        return $response;
    }

    /**
     * Validate and clean course data
     */
    private function validateAndCleanCourseData($courseData, $categories)
    {
        // Find matching category
        $categoryName = $courseData['category'] ?? '';
        $matchingCategory = $categories->firstWhere('name', $categoryName);
        
        if (!$matchingCategory) {
            // Try to find by partial match
            $matchingCategory = $categories->first(function ($cat) use ($categoryName) {
                return stripos($cat->name, $categoryName) !== false || 
                       stripos($categoryName, $cat->name) !== false;
            });
        }

        // Use first category as fallback
        if (!$matchingCategory) {
            $matchingCategory = $categories->first();
        }

        // Clean and validate data
        return [
            'title' => substr($courseData['title'] ?? 'Kursus Teknologi Terbaru', 0, 255),
            'category_id' => $matchingCategory->id,
            'category_name' => $matchingCategory->name,
            'description' => substr($courseData['description'] ?? 'Deskripsi kursus akan diisi di sini.', 0, 1000),
            'level' => in_array($courseData['level'] ?? 'beginner', ['beginner', 'intermediate', 'advanced']) 
                      ? $courseData['level'] : 'beginner',
            'duration' => max(1, min(500, intval($courseData['duration'] ?? 20))),
            'price' => $this->validatePrice($courseData['price'] ?? 50000),
            'is_free' => $courseData['is_free'] ?? false,
            'learning_outcomes' => array_slice($courseData['learning_outcomes'] ?? [], 0, 5),
            'target_audience' => array_slice($courseData['target_audience'] ?? [], 0, 5),
            'requirements' => array_slice($courseData['requirements'] ?? [], 0, 5)
        ];
    }

    /**
     * Validate price (must be multiple of 1000, min 30000)
     */
    private function validatePrice($price)
    {
        $price = intval($price);
        
        if ($price < 30000) {
            $price = 30000;
        }
        
        // Round to nearest 1000
        $price = round($price / 1000) * 1000;
        
        return min(1000000, $price); // Max 1 million
    }

    /**
     * Get fallback course suggestion when AI fails
     */
    private function getFallbackCourseSuggestion()
    {
        $fallbackSuggestions = [
            [
                'title' => 'Membangun Website Modern dengan React dan Laravel',
                'category_name' => 'Web Development',
                'description' => 'Pelajari cara membangun aplikasi web full-stack modern menggunakan React untuk frontend dan Laravel untuk backend. Kursus ini akan mengajarkan Anda dari dasar hingga deployment aplikasi yang siap production.',
                'level' => 'intermediate',
                'duration' => 40,
                'price' => 150000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Membangun REST API dengan Laravel',
                    'Membuat UI responsif dengan React',
                    'Implementasi autentikasi dan authorization',
                    'Deploy aplikasi ke production'
                ],
                'target_audience' => [
                    'Developer yang ingin upgrade skill',
                    'Fresh graduate IT',
                    'Freelancer web developer'
                ],
                'requirements' => [
                    'Dasar HTML, CSS, JavaScript',
                    'Familiar dengan PHP',
                    'Pengalaman dengan database MySQL'
                ]
            ],
            [
                'title' => 'Data Science untuk Pemula dengan Python',
                'category_name' => 'Data Science',
                'description' => 'Mulai karir Anda di bidang Data Science dengan mempelajari Python, pandas, dan machine learning. Kursus praktis dengan project real-world yang akan membantu Anda memahami analisis data dari nol.',
                'level' => 'beginner',
                'duration' => 35,
                'price' => 120000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Menguasai Python untuk data analysis',
                    'Membuat visualisasi data yang menarik',
                    'Membangun model machine learning sederhana',
                    'Menganalisis dataset real-world'
                ],
                'target_audience' => [
                    'Pemula yang ingin masuk ke Data Science',
                    'Professional yang ingin pivot karir',
                    'Mahasiswa IT dan statistik'
                ],
                'requirements' => [
                    'Dasar matematika dan statistik',
                    'Tidak perlu pengalaman programming',
                    'Laptop dengan spesifikasi minimal'
                ]
            ],
            [
                'title' => 'Mobile App Development dengan Flutter',
                'category_name' => 'Mobile Development',
                'description' => 'Belajar membuat aplikasi mobile cross-platform dengan Flutter. Dari UI design hingga integrasi API, kursus ini akan membekali Anda skill untuk menjadi mobile developer profesional.',
                'level' => 'intermediate',
                'duration' => 45,
                'price' => 180000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Membangun aplikasi Android dan iOS dengan satu codebase',
                    'Implementasi state management yang efisien',
                    'Integrasi dengan REST API dan database',
                    'Publish aplikasi ke Play Store dan App Store'
                ],
                'target_audience' => [
                    'Web developer yang ingin masuk mobile',
                    'Fresh graduate yang ingin spesialisasi mobile',
                    'Entrepreneur yang ingin membuat aplikasi sendiri'
                ],
                'requirements' => [
                    'Dasar programming (any language)',
                    'Familiar dengan konsep OOP',
                    'Pengalaman dengan Git'
                ]
            ]
        ];

        // Get random suggestion
        $randomIndex = array_rand($fallbackSuggestions);
        $suggestion = $fallbackSuggestions[$randomIndex];

        // Find matching category ID
        $category = Category::where('name', $suggestion['category_name'])->first();
        if ($category) {
            $suggestion['category_id'] = $category->id;
        }

        return $suggestion;
    }
}
