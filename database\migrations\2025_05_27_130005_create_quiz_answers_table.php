<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_answers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('attempt_id'); // Foreign key to quiz_attempts table
            $table->uuid('question_id'); // Foreign key to quiz_questions table
            $table->uuid('selected_option_id')->nullable(); // Foreign key to quiz_question_options table (for multiple choice)
            
            // Answer Information
            $table->longText('answer_text')->nullable(); // Text answer for short answer/essay questions
            $table->boolean('is_correct')->default(false); // Whether the answer is correct
            $table->integer('points_earned')->default(0); // Points earned for this answer
            $table->integer('max_points')->default(0); // Maximum points for this question
            
            // Timing
            $table->timestamp('answered_at')->nullable(); // When the question was answered
            $table->integer('time_taken')->nullable(); // Time taken to answer in seconds
            
            // Grading (for essay questions)
            $table->integer('manual_score')->nullable(); // Manual score given by tutor
            $table->text('feedback')->nullable(); // Feedback from tutor
            $table->uuid('graded_by')->nullable(); // Who graded this answer
            $table->timestamp('graded_at')->nullable(); // When it was graded
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('attempt_id')->references('id')->on('quiz_attempts')->onDelete('cascade');
            $table->foreign('question_id')->references('id')->on('quiz_questions')->onDelete('cascade');
            $table->foreign('selected_option_id')->references('id')->on('quiz_question_options')->onDelete('set null');
            $table->foreign('graded_by')->references('id')->on('users')->onDelete('set null');
            
            // Indexes
            $table->index('attempt_id');
            $table->index('question_id');
            $table->index('selected_option_id');
            $table->index('is_correct');
            $table->index('graded_by');
            
            // Unique constraint to prevent duplicate answers for same question in same attempt
            $table->unique(['attempt_id', 'question_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_answers');
    }
};
