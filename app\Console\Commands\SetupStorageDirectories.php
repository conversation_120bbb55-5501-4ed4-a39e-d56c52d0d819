<?php

namespace App\Console\Commands;

use App\Services\FileStorageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SetupStorageDirectories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:setup {--force : Force recreation of directories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up storage directories for file uploads and ensure proper structure';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up storage directories...');

        // Ensure base directories exist
        $this->createDirectoryStructure();

        // Create symbolic link for public storage if it doesn't exist
        $this->createStorageLink();

        // Set proper permissions
        $this->setPermissions();

        $this->info('✅ Storage directories setup completed successfully!');

        return Command::SUCCESS;
    }

    /**
     * Create the directory structure
     */
    private function createDirectoryStructure(): void
    {
        $this->info('Creating directory structure...');

        // Use FileStorageService to ensure directories exist
        FileStorageService::ensureDirectoriesExist();

        // Additional directories for organized storage
        $directories = [
            // Public directories
            'public' => [
                'user',
                'courses',
                'blog',
                'temp'
            ],
            // Private directories
            'local' => [
                'tutor',
                'private',
                'backups',
                'exports'
            ]
        ];

        foreach ($directories as $disk => $dirs) {
            foreach ($dirs as $dir) {
                if (!Storage::disk($disk)->exists($dir)) {
                    Storage::disk($disk)->makeDirectory($dir);
                    $this->line("  ✓ Created {$disk}/{$dir}");
                } else {
                    $this->line("  - {$disk}/{$dir} already exists");
                }
            }
        }
    }

    /**
     * Create storage link if it doesn't exist
     */
    private function createStorageLink(): void
    {
        $this->info('Checking storage link...');

        $publicPath = public_path('storage');
        $storagePath = storage_path('app/public');

        if (!file_exists($publicPath)) {
            if (PHP_OS_FAMILY === 'Windows') {
                // For Windows, create a junction
                $command = "mklink /J \"{$publicPath}\" \"{$storagePath}\"";
                exec($command, $output, $returnCode);

                if ($returnCode === 0) {
                    $this->line('  ✓ Storage link created successfully');
                } else {
                    $this->warn('  ⚠ Could not create storage link automatically');
                    $this->warn('  Please run: php artisan storage:link');
                }
            } else {
                // For Unix-like systems
                symlink($storagePath, $publicPath);
                $this->line('  ✓ Storage link created successfully');
            }
        } else {
            $this->line('  - Storage link already exists');
        }
    }

    /**
     * Set proper permissions for storage directories
     */
    private function setPermissions(): void
    {
        if (PHP_OS_FAMILY !== 'Windows') {
            $this->info('Setting permissions...');

            $storagePath = storage_path();

            // Set permissions for storage directory
            chmod($storagePath, 0755);
            chmod($storagePath . '/app', 0755);
            chmod($storagePath . '/app/public', 0755);
            chmod($storagePath . '/logs', 0755);

            $this->line('  ✓ Permissions set successfully');
        } else {
            $this->line('  - Skipping permissions (Windows)');
        }
    }
}
