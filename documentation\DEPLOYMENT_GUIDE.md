# Ngambiskuy Deployment Guide

## Overview

This guide covers deploying the Ngambiskuy AI-powered learning platform with the latest build assets.

## Pre-Deployment Checklist

### ✅ Build Assets Ready
- [x] CSS optimized: `app-B3aRpyPq.css` (88.76 kB → 13.46 kB gzipped)
- [x] Main JS optimized: `app-BrzNTaqk.js` (4.28 kB → 1.65 kB gzipped)  
- [x] AI Chat JS optimized: `ai-chat-GFKfmSl1.js` (10.34 kB → 3.57 kB gzipped)
- [x] Build manifest generated: `public/build/manifest.json`

### 🔧 Environment Configuration
- [ ] Set `APP_ENV=production` in `.env`
- [ ] Configure database connection
- [ ] Set up file storage (for course materials)
- [ ] Configure AI service endpoints (for future Gemini integration)

## Deployment Steps

### 1. Build Assets
```bash
# Generate optimized production assets
npm run build

# Or with build info
npm run build:info
```

### 2. <PERSON><PERSON> Setup
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Generate application key
php artisan key:generate

# Run database migrations
php artisan migrate --force

# Seed initial data (optional)
php artisan db:seed

# Clear and cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 3. File Permissions
```bash
# Set proper permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### 4. Web Server Configuration

#### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/ngambiskuy/public;
    
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Asset Management

### Production Asset Loading
The application automatically detects the environment and loads appropriate assets:

- **Development**: Vite dev server with hot reload
- **Production**: Pre-built optimized assets from `public/build/`

### Asset Versioning
Assets include content hashes for cache busting:
- `app-B3aRpyPq.css` (hash: B3aRpyPq)
- `app-BrzNTaqk.js` (hash: BrzNTaqk)
- `ai-chat-GFKfmSl1.js` (hash: GFKfmSl1)

## Performance Optimizations

### Implemented
- ✅ CSS minification and compression (84.8% reduction)
- ✅ JavaScript minification and compression (61-65% reduction)
- ✅ Asset bundling and optimization
- ✅ Gzip compression ready

### Recommended
- [ ] Enable Gzip/Brotli compression on web server
- [ ] Set up CDN for static assets
- [ ] Configure browser caching headers
- [ ] Implement lazy loading for images

## Monitoring

### Key Metrics to Monitor
- Page load times
- Asset load times
- JavaScript errors (especially AI Chat component)
- Database query performance
- File upload success rates

### Health Checks
```bash
# Check if application is running
curl -f http://your-domain.com/health || exit 1

# Check asset availability
curl -f http://your-domain.com/build/assets/app-B3aRpyPq.css || exit 1
```

## Troubleshooting

### Common Issues

#### Assets Not Loading
1. Check if `public/build/` directory exists
2. Verify file permissions
3. Check web server configuration
4. Ensure `APP_ENV=production` is set

#### AI Chat Not Working
1. Check browser console for JavaScript errors
2. Verify `ai-chat-GFKfmSl1.js` is loading
3. Check if AI Chat component is included in layout

#### Performance Issues
1. Enable web server compression
2. Check database query optimization
3. Monitor memory usage
4. Review asset loading strategy

## Security Considerations

- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] File upload restrictions in place
- [ ] Database credentials secured
- [ ] API endpoints protected

## Backup Strategy

### What to Backup
- Database (user data, courses, progress)
- Uploaded files (`storage/app/`)
- Environment configuration (`.env`)
- Custom configurations

### Backup Commands
```bash
# Database backup
php artisan backup:run

# File backup
tar -czf storage-backup.tar.gz storage/
```

## Rollback Plan

1. Keep previous build assets
2. Database migration rollback capability
3. File storage backup restoration
4. Configuration rollback

## Post-Deployment Verification

- [ ] Homepage loads correctly
- [ ] User registration/login works
- [ ] Course browsing functional
- [ ] AI Chat component loads
- [ ] File uploads work
- [ ] Database connections stable
- [ ] All assets load properly

## Support

For deployment issues:
1. Check Laravel logs: `storage/logs/laravel.log`
2. Check web server error logs
3. Verify environment configuration
4. Test individual components

---

**Last Updated**: After latest build completion  
**Build Version**: app-B3aRpyPq, app-BrzNTaqk, ai-chat-GFKfmSl1
