<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_questions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('exam_id'); // Foreign key to exams table

            // Question Information
            $table->longText('question'); // Question text (supports rich text)
            $table->enum('type', ['multiple_choice', 'true_false', 'short_answer', 'essay'])->default('multiple_choice');
            $table->integer('points')->default(1); // Points for this question
            $table->integer('sort_order')->default(0); // Order within exam
            $table->text('explanation')->nullable(); // Explanation shown after answering
            $table->boolean('is_required')->default(true); // Whether this question must be answered

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('exam_id')->references('id')->on('exams')->onDelete('cascade');

            // Indexes
            $table->index('exam_id');
            $table->index('sort_order');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_questions');
    }
};
