@extends('layouts.app')

@section('title', 'Checkout NALA Membership - Ngambiskuy')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-8 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Checkout NALA Membership</h1>
                <p class="text-gray-600 mt-2">Selesaikan pembayaran untuk mengaktifkan membership Anda</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">
                <!-- Order Summary -->
                <div class="order-2 lg:order-1">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4"><PERSON><PERSON><PERSON></h2>

                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">{{ $membershipPlan->name }} Plan</h3>
                            <span class="text-2xl font-bold text-emerald-600">
                                {{ $membershipPlan->formatted_price }}
                            </span>
                        </div>

                        <p class="text-gray-600 mb-4">{{ $membershipPlan->description }}</p>

                        <div class="space-y-2">
                            @foreach($membershipPlan->all_features as $feature)
                            <div class="flex items-start">
                                <svg class="w-4 h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700 text-sm">{{ $feature }}</span>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    @if($membershipPlan->is_team_plan)
                    <div class="mb-6">
                        <label for="team_size" class="block text-sm font-medium text-gray-700 mb-2">
                            Ukuran Tim (minimal {{ $membershipPlan->minimum_users }} orang)
                        </label>
                        <input type="number"
                               id="team_size"
                               name="team_size"
                               min="{{ $membershipPlan->minimum_users }}"
                               max="{{ $membershipPlan->maximum_users ?? 100 }}"
                               value="{{ $teamSize }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
                               onchange="updateTeamPrice()">
                        <p class="text-sm text-gray-600 mt-1">
                            Harga per user: {{ $membershipPlan->formatted_price_per_user }}
                        </p>
                    </div>
                    @endif

                    <!-- Price Breakdown -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-semibold" id="subtotal">{{ $membershipPlan->formatted_price }}</span>
                        </div>
                        @if($membershipPlan->is_team_plan)
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Jumlah User</span>
                            <span class="font-semibold" id="user-count">{{ $teamSize }}</span>
                        </div>
                        @endif
                        <div class="flex justify-between items-center text-lg font-bold border-t border-gray-200 pt-2">
                            <span>Total</span>
                            <span class="text-emerald-600" id="total">{{ $membershipPlan->formatted_price }}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="order-1 lg:order-2">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Pembayaran</h2>

                    <form action="{{ route('payment.membership.process', $membershipPlan) }}" method="POST" id="payment-form">
                        @csrf

                        @if($membershipPlan->is_team_plan)
                        <input type="hidden" name="team_size" id="hidden_team_size" value="{{ $teamSize }}">
                        @endif

                        <!-- Payment Method -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Metode Pembayaran</label>
                            <div class="space-y-3">
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="bank_transfer" class="mr-3" checked>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Transfer Bank</div>
                                            <div class="text-sm text-gray-600">BCA, Mandiri, BNI, BRI</div>
                                        </div>
                                    </div>
                                </label>

                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="e_wallet" class="mr-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">E-Wallet</div>
                                            <div class="text-sm text-gray-600">GoPay, OVO, DANA, LinkAja</div>
                                        </div>
                                    </div>
                                </label>

                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="credit_card" class="mr-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Kartu Kredit</div>
                                            <div class="text-sm text-gray-600">Visa, Mastercard, JCB</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>



                        <!-- Terms and Conditions -->
                        <div class="mb-6">
                            <label class="flex items-start">
                                <input type="checkbox" name="terms_agreed" required class="mt-1 mr-3">
                                <span class="text-sm text-gray-700">
                                    Saya setuju dengan
                                    <a href="#" class="text-emerald-600 hover:text-emerald-700">Syarat dan Ketentuan</a>
                                    serta
                                    <a href="#" class="text-emerald-600 hover:text-emerald-700">Kebijakan Privasi</a>
                                    Ngambiskuy
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="w-full bg-emerald-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200">
                            Bayar Sekarang
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@if($membershipPlan->is_team_plan)
<script>
function updateTeamPrice() {
    const teamSize = document.getElementById('team_size').value;
    const pricePerUser = {{ $membershipPlan->price_per_user ?? $membershipPlan->price }};
    const total = teamSize * pricePerUser;

    document.getElementById('subtotal').textContent = 'IDR ' + total.toLocaleString('id-ID');
    document.getElementById('total').textContent = 'IDR ' + total.toLocaleString('id-ID');
    document.getElementById('user-count').textContent = teamSize;
    document.getElementById('hidden_team_size').value = teamSize;
}
</script>
@endif
@endsection
