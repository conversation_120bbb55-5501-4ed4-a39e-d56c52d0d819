@extends('layouts.tutor')

@section('title', 'Kurikulum - ' . $course->title)

@push('styles')
<style>
/* Modal Animation Styles */
.modal-backdrop {
    transition: opacity 0.3s ease-in-out;
}

.modal-backdrop.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-backdrop:not(.hidden) {
    opacity: 1;
}

.modal-content {
    transform: scale(0.95) translateY(-20px);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0;
}

.modal-backdrop:not(.hidden) .modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Enhanced button hover effects */
.btn-hover-scale {
    transition: transform 0.2s ease-in-out;
}

.btn-hover-scale:hover {
    transform: scale(1.05);
}

/* Loading animation for buttons */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.btn-loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Enhanced focus styles */
.focus-enhanced:focus {
    ring-width: 3px;
    ring-color: rgba(59, 130, 246, 0.5);
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Modern Header Section -->
    <div class="bg-white border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <!-- Breadcrumb -->
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('tutor.courses') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-emerald-600 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                </svg>
                                Kursus Saya
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Kurikulum</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <!-- Main Header -->
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Kurikulum Kursus</h1>
                                <p class="text-gray-600 mt-1">Kelola konten dan struktur pembelajaran kursus Anda</p>
                            </div>
                        </div>

                        <!-- Course Info Card -->
                        <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-4 border border-emerald-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                        <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">{{ $course->title }}</h3>
                                        <div class="flex items-center space-x-4 mt-1">
                                            <span class="inline-flex items-center text-sm text-gray-600">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                                                </svg>
                                                {{ $course->category->name }}
                                            </span>
                                            <span class="inline-flex items-center text-sm text-gray-600">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                                </svg>
                                                {{ $course->level_indonesian }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-3">
                                    @if($course->is_free)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                            GRATIS
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $course->formatted_price }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center space-x-3 ml-6">
                        <button onclick="openAddChapterModal()" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg text-sm font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Tambah Bab
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Course Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-emerald-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-600">Total Bab</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $course->chapters->count() }}</p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-teal-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-600">Total Materi</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $course->lessons->count() }}</p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-amber-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-600">Total Durasi</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $course->lessons->sum('duration_minutes') }} min</p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-green-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-600">Materi Published</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $course->lessons->where('is_published', true)->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Actions & Referral Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Course Publishing Actions -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Status Kursus</h3>
                    <p class="text-sm text-gray-600">Kelola publikasi kursus Anda</p>
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Status Saat Ini:</span>
                    @if($course->status === 'published')
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Dipublikasikan
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Draft
                        </span>
                    @endif
                </div>

                <div class="flex flex-col space-y-3">
                    @if($course->status === 'draft')
                        <form action="{{ route('tutor.courses.publish', $course) }}" method="POST" onsubmit="return confirmPublish()">
                            @csrf
                            <button type="submit" class="w-full btn bg-green-600 hover:bg-green-700 text-white">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Publikasikan Kursus
                            </button>
                        </form>
                    @else
                        <form action="{{ route('tutor.courses.save-draft', $course) }}" method="POST" onsubmit="return confirmUnpublish()">
                            @csrf
                            <button type="submit" class="w-full btn bg-gray-600 hover:bg-gray-700 text-white">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Ubah ke Draft
                            </button>
                        </form>
                    @endif

                    <form action="{{ route('tutor.courses.save-draft', $course) }}" method="POST">
                        @csrf
                        <button type="submit" class="w-full btn border-emerald-300 text-emerald-600 hover:bg-emerald-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                            </svg>
                            Simpan Perubahan
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Referral Program Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-emerald-600 to-teal-600 px-6 py-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-white">Program Referral</h3>
                        <p class="text-sm text-emerald-100">Bagikan kursus dan dapatkan bonus revenue</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 space-y-6">
                <!-- Referral Link -->
                <div class="space-y-3">
                    <label class="text-sm font-medium text-gray-700">Link Referral Kursus</label>
                    <div class="relative">
                        <input type="text" id="courseReferralLink" readonly
                               value="{{ route('course.show', $course) }}?ref={{ auth()->user()->getReferralCode() }}"
                               class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-gray-50 text-sm focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        <button onclick="copyReferralLink()" id="copyLinkBtn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-emerald-600 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Referral Code -->
                <div class="space-y-3">
                    <label class="text-sm font-medium text-gray-700">Kode Referral Anda</label>
                    <div class="relative">
                        <input type="text" id="referralCode" readonly
                               value="{{ auth()->user()->getReferralCode() }}"
                               class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg bg-gray-50 text-lg font-mono text-center tracking-wider focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                        <button onclick="copyReferralCode()" id="copyCodeBtn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-emerald-600 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="space-y-3">
                    <label class="text-sm font-medium text-gray-700">Bagikan Kursus</label>
                    <div class="grid grid-cols-3 gap-3">
                        <button onclick="shareToWhatsApp()" class="flex items-center justify-center p-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.787"/>
                            </svg>
                            <span class="text-sm font-medium">WhatsApp</span>
                        </button>
                        <button onclick="shareToTelegram()" class="flex items-center justify-center p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                            </svg>
                            <span class="text-sm font-medium">Telegram</span>
                        </button>
                        <button onclick="shareToTwitter()" class="flex items-center justify-center p-3 bg-sky-500 hover:bg-sky-600 text-white rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                            <span class="text-sm font-medium">Twitter</span>
                        </button>
                    </div>
                </div>

                <!-- Simple Info -->
                <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-emerald-800">Dapatkan 80% revenue dengan referral (vs 60% tanpa referral)</p>
                            <p class="text-xs text-emerald-600 mt-1">Bagikan link atau kode referral untuk meningkatkan penghasilan</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculum Content -->
    <div class="bg-white rounded-lg shadow-sm">
        @if($course->chapters->count() > 0)
            <div class="divide-y divide-gray-200">
                @foreach($course->chapters as $chapter)
                    <div class="p-6">
                        <!-- Chapter Header -->
                        <div class="flex items-center justify-between mb-4 group">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold shadow-sm transition-all duration-200">
                                        {{ $loop->iteration }}
                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $chapter->title }}</h3>
                                    @if($chapter->description)
                                        <p class="text-sm text-gray-600 mt-1">{{ $chapter->description }}</p>
                                    @endif
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="text-xs text-gray-500">{{ $chapter->lessons->count() }} materi</span>
                                        <span class="text-xs text-gray-500">{{ $chapter->lessons->sum('duration_minutes') }} menit</span>
                                        @if($chapter->is_published)
                                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Published</span>
                                        @else
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">Draft</span>
                                        @endif
                                        @if($chapter->is_free)
                                            <span class="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded">Gratis</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="{{ route('tutor.curriculum.create-material', [$course, $chapter]) }}"
                                        class="btn-hover-scale px-3 py-2 text-sm font-medium text-emerald-600 bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-all duration-200 flex items-center"
                                        title="Tambah materi baru ke bab ini">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Tambah Materi
                                </a>
                                <button onclick="editChapter('{{ $chapter->id }}', '{{ $chapter->title }}', '{{ $chapter->description }}', {{ $chapter->is_published ? 'true' : 'false' }}, {{ $chapter->is_free ? 'true' : 'false' }})"
                                        class="btn-hover-scale p-2 text-gray-400 hover:text-teal-600 hover:bg-teal-50 rounded-lg transition-all duration-200 group"
                                        title="Edit informasi bab">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button onclick="deleteChapter('{{ $chapter->id }}', '{{ $chapter->title }}')"
                                        class="btn-hover-scale p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 group"
                                        title="Hapus bab dan semua materinya">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Chapter Lessons -->
                        @if($chapter->lessons->count() > 0)
                            <div class="ml-11 space-y-3">
                                @foreach($chapter->lessons as $lesson)
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md hover:border-gray-300 transition-all duration-200 group">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 rounded-lg flex items-center justify-center
                                                    @if($lesson->type === 'video') bg-red-100 text-red-600
                                                    @elseif($lesson->type === 'text') bg-blue-100 text-blue-600
                                                    @elseif($lesson->type === 'quiz') bg-yellow-100 text-yellow-600
                                                    @else bg-purple-100 text-purple-600
                                                    @endif">
                                                    @if($lesson->type === 'video')
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                        </svg>
                                                    @elseif($lesson->type === 'text')
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    @elseif($lesson->type === 'quiz')
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                                        </svg>
                                                    @endif
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">{{ $lesson->title }}</h4>
                                                <div class="flex items-center space-x-3 mt-1">
                                                    <span class="text-xs text-gray-500">{{ $lesson->type_indonesian }}</span>
                                                    @if($lesson->duration_minutes > 0)
                                                        <span class="text-xs text-gray-500">{{ $lesson->duration_minutes }} menit</span>
                                                    @endif
                                                    @if($lesson->is_published)
                                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Published</span>
                                                    @else
                                                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">Draft</span>
                                                    @endif
                                                    @if($lesson->is_preview && !$course->is_free)
                                                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">Preview</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                            <a href="{{ route('tutor.curriculum.edit-material', [$course, $chapter, $lesson]) }}"
                                                    class="btn-hover-scale p-2 text-gray-400 hover:text-teal-600 hover:bg-teal-50 rounded-lg transition-all duration-200 group/edit"
                                                    title="Edit materi ini">
                                                <svg class="w-4 h-4 group-hover/edit:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </a>
                                            <button onclick="deleteLesson('{{ $lesson->id }}', '{{ $chapter->id }}', '{{ $lesson->title }}')"
                                                    class="btn-hover-scale p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 group/delete"
                                                    title="Hapus materi ini">
                                                <svg class="w-4 h-4 group-hover/delete:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="ml-11 p-4 bg-gray-50 rounded-lg text-center">
                                <p class="text-gray-500 text-sm">Belum ada materi dalam bab ini</p>
                                <a href="{{ route('tutor.curriculum.create-material', [$course, $chapter]) }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white text-sm mt-2">
                                    Tambah Materi Pertama
                                </a>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="p-12 text-center">
                <div class="w-16 h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Belum Ada Kurikulum</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto text-sm">Mulai buat kurikulum kursus Anda dengan menambahkan bab pertama</p>
                <button onclick="openAddChapterModal()" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Tambah Bab Pertama
                </button>
            </div>
        @endif
    </div>
</div>

<!-- Add Chapter Modal -->
<div id="addChapterModal" class="modal-backdrop fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 flex items-center justify-center">
    <div class="relative mx-auto p-0 border-0 w-full max-w-md">
        <div class="modal-content bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-emerald-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white">Tambah Bab Baru</h3>
                    <button type="button" onclick="closeAddChapterModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <form action="{{ route('tutor.curriculum.store-chapter', $course) }}" method="POST" class="p-6">
                @csrf
                <div class="space-y-5">
                    <div>
                        <label for="chapter_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Bab <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="chapter_title" name="title" required
                               class="focus-enhanced w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: Bab 1: Pengenalan Programming">
                    </div>

                    <div>
                        <label for="chapter_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                        <textarea id="chapter_description" name="description" rows="3"
                                  class="focus-enhanced w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none"
                                  placeholder="Deskripsi singkat tentang bab ini..."></textarea>
                    </div>

                    @if(!$course->is_free)
                        <div class="bg-teal-50 p-4 rounded-lg">
                            <label class="flex items-start">
                                <input type="checkbox" name="is_free" value="1" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Bab Gratis</span>
                                    <p class="text-xs text-gray-500 mt-1">Bab ini dapat diakses tanpa membeli kursus (cocok untuk preview)</p>
                                </div>
                            </label>
                        </div>
                    @endif
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeAddChapterModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200">
                        Batal
                    </button>
                    <button type="submit"
                            class="btn-hover-scale px-6 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Bab
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Chapter Modal -->
<div id="editChapterModal" class="modal-backdrop fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 flex items-center justify-center">
    <div class="relative mx-auto p-0 border-0 w-full max-w-md">
        <div class="modal-content bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-teal-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white">Edit Bab</h3>
                    <button type="button" onclick="closeEditChapterModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <form id="editChapterForm" method="POST" class="p-6" onsubmit="handleEditChapterSubmit(event)">
                @csrf
                @method('PUT')
                <div class="space-y-5">
                    <div>
                        <label for="edit_chapter_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Bab <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="edit_chapter_title" name="title" required
                               class="focus-enhanced w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: Bab 1: Pengenalan Programming">
                    </div>

                    <div>
                        <label for="edit_chapter_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                        <textarea id="edit_chapter_description" name="description" rows="3"
                                  class="focus-enhanced w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-200 resize-none"
                                  placeholder="Deskripsi singkat tentang bab ini..."></textarea>
                    </div>

                    <div class="space-y-3">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <label class="flex items-start">
                                <input type="checkbox" id="edit_chapter_is_published" name="is_published" value="1" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded mt-1">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Publikasikan Bab</span>
                                    <p class="text-xs text-gray-500 mt-1">Bab ini akan terlihat oleh siswa</p>
                                </div>
                            </label>
                        </div>

                        @if(!$course->is_free)
                            <div class="bg-teal-50 p-4 rounded-lg">
                                <label class="flex items-start">
                                    <input type="checkbox" id="edit_chapter_is_free" name="is_free" value="1" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded mt-1">
                                    <div class="ml-3">
                                        <span class="text-sm font-medium text-gray-700">Bab Gratis</span>
                                        <p class="text-xs text-gray-500 mt-1">Bab ini dapat diakses tanpa membeli kursus (cocok untuk preview)</p>
                                    </div>
                                </label>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeEditChapterModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200">
                        Batal
                    </button>
                    <button type="submit"
                            class="btn-hover-scale px-6 py-2 text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 rounded-lg transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="modal-backdrop fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 flex items-center justify-center">
    <div class="relative mx-auto p-0 border-0 w-full max-w-md">
        <div class="modal-content bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-red-500 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-white">Konfirmasi Hapus</h3>
                    </div>
                    <button type="button" onclick="closeDeleteConfirmModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Yakin ingin menghapus?</h4>
                        <p class="text-sm text-gray-600 mb-4" id="deleteConfirmMessage">
                            <!-- Message will be set by JavaScript -->
                        </p>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-red-800">Peringatan!</p>
                                    <p class="text-xs text-red-700 mt-1" id="deleteWarningMessage">
                                        <!-- Warning message will be set by JavaScript -->
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeDeleteConfirmModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200">
                        Batal
                    </button>
                    <button type="button" id="confirmDeleteBtn"
                            class="btn-hover-scale px-6 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Ya, Hapus
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>




<script>
// Global variables for delete confirmation
let pendingDeleteAction = null;

// Add Chapter Modal Functions
function openAddChapterModal() {
    const modal = document.getElementById('addChapterModal');
    modal.classList.remove('hidden');
    // Clear form
    document.getElementById('chapter_title').value = '';
    document.getElementById('chapter_description').value = '';
    // Focus on title input after animation
    setTimeout(() => {
        document.getElementById('chapter_title').focus();
    }, 300);
}

function closeAddChapterModal() {
    const modal = document.getElementById('addChapterModal');
    modal.classList.add('hidden');
}

// Edit Chapter Modal Functions
function editChapter(id, title, description, isPublished, isFree) {
    // Set form action
    const form = document.getElementById('editChapterForm');
    form.action = '{{ route("tutor.curriculum.update-chapter", [$course, "__CHAPTER_ID__"]) }}'.replace('__CHAPTER_ID__', id);

    // Fill form with current data
    document.getElementById('edit_chapter_title').value = title;
    document.getElementById('edit_chapter_description').value = description || '';
    document.getElementById('edit_chapter_is_published').checked = isPublished;

    @if(!$course->is_free)
        document.getElementById('edit_chapter_is_free').checked = isFree;
    @endif

    // Show modal
    const modal = document.getElementById('editChapterModal');
    modal.classList.remove('hidden');

    // Focus on title input after animation
    setTimeout(() => {
        document.getElementById('edit_chapter_title').focus();
    }, 300);
}

function closeEditChapterModal() {
    const modal = document.getElementById('editChapterModal');
    modal.classList.add('hidden');
}

// Handle edit chapter form submission to ensure boolean values are sent correctly
function handleEditChapterSubmit(event) {
    const form = event.target;

    // Remove any existing hidden inputs for boolean fields
    const existingHiddenInputs = form.querySelectorAll('input[type="hidden"][name="is_published"], input[type="hidden"][name="is_free"]');
    existingHiddenInputs.forEach(input => input.remove());

    // Add hidden inputs for unchecked checkboxes
    const isPublishedCheckbox = document.getElementById('edit_chapter_is_published');
    if (!isPublishedCheckbox.checked) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'is_published';
        hiddenInput.value = '0';
        form.appendChild(hiddenInput);
    }

    @if(!$course->is_free)
        const isFreeCheckbox = document.getElementById('edit_chapter_is_free');
        if (!isFreeCheckbox.checked) {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'is_free';
            hiddenInput.value = '0';
            form.appendChild(hiddenInput);
        }
    @endif

    // Allow form to submit normally
    return true;
}

// Delete Confirmation Modal Functions
function deleteChapter(id, title) {
    showDeleteConfirmation(
        `Bab "${title}"`,
        'Semua materi dalam bab ini juga akan dihapus. Tindakan ini tidak dapat dibatalkan.',
        function() {
            // Create and submit delete form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("tutor.curriculum.delete-chapter", [$course, "__CHAPTER_ID__"]) }}'.replace('__CHAPTER_ID__', id);

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';

            const tokenField = document.createElement('input');
            tokenField.type = 'hidden';
            tokenField.name = '_token';
            tokenField.value = '{{ csrf_token() }}';

            form.appendChild(methodField);
            form.appendChild(tokenField);
            document.body.appendChild(form);
            form.submit();
        }
    );
}

function deleteLesson(lessonId, chapterId, title) {
    showDeleteConfirmation(
        `Materi "${title}"`,
        'Materi ini akan dihapus secara permanen. Tindakan ini tidak dapat dibatalkan.',
        function() {
            // Create and submit delete form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("tutor.curriculum.delete-lesson", [$course, "__CHAPTER_ID__", "__LESSON_ID__"]) }}'.replace('__CHAPTER_ID__', chapterId).replace('__LESSON_ID__', lessonId);

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';

            const tokenField = document.createElement('input');
            tokenField.type = 'hidden';
            tokenField.name = '_token';
            tokenField.value = '{{ csrf_token() }}';

            form.appendChild(methodField);
            form.appendChild(tokenField);
            document.body.appendChild(form);
            form.submit();
        }
    );
}

function showDeleteConfirmation(itemName, warningMessage, deleteAction) {
    document.getElementById('deleteConfirmMessage').textContent = `Anda akan menghapus ${itemName}.`;
    document.getElementById('deleteWarningMessage').textContent = warningMessage;

    // Store the delete action
    pendingDeleteAction = deleteAction;

    // Reset button state
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    confirmBtn.innerHTML = `
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        Ya, Hapus
    `;
    confirmBtn.disabled = false;
    confirmBtn.classList.remove('btn-loading');

    // Show modal
    const modal = document.getElementById('deleteConfirmModal');
    modal.classList.remove('hidden');
}

function closeDeleteConfirmModal() {
    const modal = document.getElementById('deleteConfirmModal');
    modal.classList.add('hidden');
    pendingDeleteAction = null;
}

function confirmDelete() {
    if (pendingDeleteAction) {
        // Add loading state
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Menghapus...
        `;
        confirmBtn.disabled = true;
        confirmBtn.classList.add('btn-loading');

        // Execute delete action after a short delay for better UX
        setTimeout(() => {
            pendingDeleteAction();
        }, 500);
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add Chapter Modal
    const addChapterModal = document.getElementById('addChapterModal');
    if (addChapterModal) {
        addChapterModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddChapterModal();
            }
        });
    }

    // Edit Chapter Modal
    const editChapterModal = document.getElementById('editChapterModal');
    if (editChapterModal) {
        editChapterModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditChapterModal();
            }
        });
    }

    // Delete Confirmation Modal
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    if (deleteConfirmModal) {
        deleteConfirmModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteConfirmModal();
            }
        });
    }

    // Confirm Delete Button
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', confirmDelete);
    }

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAddChapterModal();
            closeEditChapterModal();
            closeDeleteConfirmModal();
        }
    });
});

// Success notification system
function showSuccessNotification(message) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.success-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'success-notification fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Error notification system
function showErrorNotification(message) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.error-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'error-notification fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds (longer for errors)
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Check for Laravel session flash messages
@if(session('success'))
    document.addEventListener('DOMContentLoaded', function() {
        showSuccessNotification('{{ session('success') }}');
    });
@endif

@if(session('error'))
    document.addEventListener('DOMContentLoaded', function() {
        showErrorNotification('{{ session('error') }}');
    });
@endif

// Course publishing confirmation functions
function confirmPublish() {
    return confirm('Apakah Anda yakin ingin menerbitkan kursus ini? Setelah dipublikasikan, siswa dapat mendaftar di kursus Anda.');
}

function confirmUnpublish() {
    return confirm('Apakah Anda yakin ingin mengubah kursus ini menjadi draft? Kursus akan disembunyikan dari publik.');
}

// Enhanced referral code copy functions with visual feedback
function copyReferralLink() {
    const linkInput = document.getElementById('courseReferralLink');
    const copyBtn = document.getElementById('copyLinkBtn');

    linkInput.select();
    linkInput.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(linkInput.value).then(function() {
        // Visual feedback
        copyBtn.innerHTML = `
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        `;
        copyBtn.classList.add('text-green-600');

        showSuccessNotification('Link referral berhasil disalin!');

        // Reset button after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            `;
            copyBtn.classList.remove('text-green-600');
        }, 2000);
    }).catch(function() {
        // Fallback for older browsers
        document.execCommand('copy');
        showSuccessNotification('Link referral berhasil disalin!');
    });
}

function copyReferralCode() {
    const codeInput = document.getElementById('referralCode');
    const copyBtn = document.getElementById('copyCodeBtn');

    codeInput.select();
    codeInput.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(codeInput.value).then(function() {
        // Visual feedback
        copyBtn.innerHTML = `
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        `;
        copyBtn.classList.add('text-green-600');

        showSuccessNotification('Kode referral berhasil disalin!');

        // Reset button after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            `;
            copyBtn.classList.remove('text-green-600');
        }, 2000);
    }).catch(function() {
        // Fallback for older browsers
        document.execCommand('copy');
        showSuccessNotification('Kode referral berhasil disalin!');
    });
}

// Social media sharing functions
function shareToWhatsApp() {
    const referralLink = document.getElementById('courseReferralLink').value;
    const courseTitle = '{{ $course->title }}';
    const message = `🎓 Kursus "${courseTitle}" tersedia di Ngambiskuy!\n\n✨ Dapatkan akses pembelajaran berkualitas dengan harga terjangkau.\n\n🔗 ${referralLink}\n\n#Ngambiskuy #OnlineLearning #Kursus`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareToTelegram() {
    const referralLink = document.getElementById('courseReferralLink').value;
    const courseTitle = '{{ $course->title }}';
    const message = `🎓 Kursus "${courseTitle}" tersedia di Ngambiskuy!\n\n✨ Dapatkan akses pembelajaran berkualitas dengan harga terjangkau.\n\n🔗 ${referralLink}`;

    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent(message)}`;
    window.open(telegramUrl, '_blank');
}

function shareToTwitter() {
    const referralLink = document.getElementById('courseReferralLink').value;
    const courseTitle = '{{ $course->title }}';
    const message = `🎓 Belajar "${courseTitle}" di @Ngambiskuy! ✨ Pembelajaran berkualitas dengan harga terjangkau. #OnlineLearning #Kursus`;

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(referralLink)}`;
    window.open(twitterUrl, '_blank');
}
</script>
@endsection
