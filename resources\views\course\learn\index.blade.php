@extends('layouts.app')

@section('title', 'Belajar: ' . $course->title . ' - Ngambiskuy')

@push('styles')
<style>
    /* Course Learning Page Specific Styles - Scoped to avoid conflicts */
    .course-learning-page {
        min-height: 100vh;
        background: #f8fafc;
    }

    /* Ensure header stays at top - Fix header positioning */
    body {
        position: relative !important;
        overflow-x: hidden;
    }

    header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1000 !important;
        width: 100% !important;
    }

    /* Add top padding to main content to account for fixed header */
    .course-learning-page {
        padding-top: 64px; /* Height of header */
    }

    .course-learning-page .curriculum-sidebar {
        transition: transform 0.3s ease-in-out;
        z-index: 40;
    }

    .course-learning-page .curriculum-sidebar.collapsed {
        transform: translateX(-100%);
    }

    .course-learning-page .lesson-item {
        transition: all 0.2s ease;
    }

    .course-learning-page .lesson-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .course-learning-page .progress-ring {
        transform: rotate(-90deg);
    }

    .course-learning-page .progress-ring-circle {
        transition: stroke-dasharray 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }

    .course-learning-page .stats-card {
        transition: all 0.2s ease;
    }

    .course-learning-page .stats-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .course-learning-page .quick-action-btn {
        transition: all 0.2s ease;
    }

    .course-learning-page .quick-action-btn:hover {
        transform: translateY(-1px);
    }

    /* Completed lesson checkmark */
    .course-learning-page .lesson-completed {
        background: #10b981;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .course-learning-page .lesson-in-progress {
        background: #f59e0b;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Mobile responsive fixes */
    @media (max-width: 1024px) {
        .course-learning-page .curriculum-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 320px;
            z-index: 50;
        }

        .course-learning-page .main-content {
            margin-left: 0;
        }
    }
</style>
@endpush

@section('content')
<div class="course-learning-page">
    <!-- Course Header -->
    <div class="bg-primary text-white relative">
        <!-- Simple background pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div class="flex-1">
                    <!-- Enhanced Breadcrumb -->
                    <nav class="mb-6">
                        <ol class="flex items-center space-x-2 text-sm">
                            <li><a href="{{ route('home') }}" class="text-white/80 hover:text-white transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                </svg>
                                Beranda
                            </a></li>
                            <li><span class="text-white/60 mx-2">•</span></li>
                            <li><a href="{{ route('courses.index') }}" class="text-white/80 hover:text-white transition-colors duration-200">Kursus</a></li>
                            <li><span class="text-white/60 mx-2">•</span></li>
                            <li><a href="{{ route('course.show', $course) }}" class="text-white/80 hover:text-white transition-colors duration-200">{{ Str::limit($course->title, 30) }}</a></li>
                            <li><span class="text-white/60 mx-2">•</span></li>
                            <li class="text-white font-medium">Belajar</li>
                        </ol>
                    </nav>

                    <div class="space-y-4">
                        <h1 class="text-3xl lg:text-4xl font-bold leading-tight">{{ $course->title }}</h1>
                        <p class="text-lg text-white/90 leading-relaxed max-w-3xl">{{ $course->description }}</p>

                        <!-- Course Stats -->
                        <div class="flex flex-wrap items-center gap-6 text-sm">
                            <div class="flex items-center text-white/80">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                {{ $totalLessons }} Materi
                            </div>
                            <div class="flex items-center text-white/80">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $completedLessons }} Selesai
                            </div>
                            <div class="flex items-center text-white/80">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                {{ $course->tutor->name }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Card -->
                <div class="lg:flex-shrink-0">
                    <div class="bg-white/20 rounded-xl p-6 border border-white/30">
                        <div class="flex items-center space-x-4">
                            <!-- Circular Progress -->
                            <div class="relative w-16 h-16">
                                <svg class="w-16 h-16 progress-ring" viewBox="0 0 36 36">
                                    <path class="text-white/30" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                    <path class="text-white progress-ring-circle" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $progressPercentage }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-lg font-bold text-white">{{ $progressPercentage }}%</span>
                                </div>
                            </div>
                            <div class="text-white">
                                <div class="text-sm opacity-90">Progress Kursus</div>
                                <div class="text-lg font-semibold">{{ $completedLessons }}/{{ $totalLessons }} Materi</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex gap-8 relative">
            <!-- Curriculum Sidebar Toggle Button -->
            <button id="curriculum-toggle" class="fixed top-1/2 left-4 z-50 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-all duration-200 lg:hidden">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
            </button>

            <!-- Collapsible Curriculum Sidebar -->
            <div id="curriculum-sidebar" class="curriculum-sidebar fixed lg:relative inset-y-0 left-0 z-40 w-80 lg:w-96 bg-white shadow-xl lg:shadow-sm rounded-r-2xl lg:rounded-2xl overflow-hidden lg:block">
                <!-- Sidebar Header -->
                <div class="bg-primary text-white p-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold">Kurikulum Kursus</h2>
                        <button id="sidebar-close" class="lg:hidden text-white/80 hover:text-white">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    <div class="mt-2 text-sm text-white/80">
                        {{ $course->chapters->count() }} Bab • {{ $totalLessons }} Materi
                    </div>
                </div>

                <!-- Curriculum Content -->
                <div class="h-full overflow-y-auto pb-20">
                    <div class="p-6 space-y-4">
                        @foreach($course->chapters as $chapter)
                            <div class="bg-gray-50 rounded-xl overflow-hidden border border-gray-100">
                                <!-- Chapter Header -->
                                <div class="p-4 bg-white border-b border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <h3 class="font-semibold text-gray-900 text-lg">{{ $chapter->title }}</h3>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                {{ $chapter->lessons->count() }} materi
                                            </span>
                                        </div>
                                    </div>
                                    @if($chapter->description)
                                        <p class="text-sm text-gray-600 mt-2 leading-relaxed">{{ $chapter->description }}</p>
                                    @endif
                                </div>

                                <!-- Chapter Lessons -->
                                <div class="p-2">
                                    @foreach($chapter->lessons as $lesson)
                                        @php
                                            $progress = $userProgress->get($lesson->id);
                                            $isCompleted = $progress && $progress->status === 'completed';
                                            $isInProgress = $progress && $progress->status === 'in_progress';
                                        @endphp

                                        <a href="{{ route('course.lesson', [$course, $lesson]) }}"
                                           class="lesson-item block p-4 m-2 rounded-lg border border-gray-200 hover:border-primary/30 hover:shadow-md transition-all duration-200 bg-white group">
                                            <div class="flex items-center space-x-3">
                                                <!-- Lesson Type Icon -->
                                                <div class="flex-shrink-0">
                                                    @if($lesson->type === 'video')
                                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6a2 2 0 012 2v8a2 2 0 01-2 2H9a2 2 0 01-2-2V8a2 2 0 012-2z"/>
                                                            </svg>
                                                        </div>
                                                    @elseif($lesson->type === 'text')
                                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                            </svg>
                                                        </div>
                                                    @elseif($lesson->type === 'quiz')
                                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                            </svg>
                                                        </div>
                                                    @else
                                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                                                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- Lesson Info -->
                                                <div class="flex-1 min-w-0">
                                                    <h4 class="font-medium text-gray-900 group-hover:text-primary transition-colors truncate">
                                                        {{ $lesson->title }}
                                                    </h4>
                                                    <div class="flex items-center space-x-3 mt-1">
                                                        @if($lesson->duration_minutes)
                                                            <span class="text-xs text-gray-500 flex items-center">
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                </svg>
                                                                {{ $lesson->duration_minutes }} menit
                                                            </span>
                                                        @endif
                                                        <span class="text-xs text-gray-500 capitalize">{{ $lesson->type }}</span>
                                                    </div>
                                                </div>

                                                <!-- Progress Status -->
                                                <div class="flex-shrink-0">
                                                    @if($isCompleted)
                                                        <div class="lesson-completed w-6 h-6">
                                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"/>
                                                            </svg>
                                                        </div>
                                                    @elseif($isInProgress)
                                                        <div class="lesson-in-progress w-6 h-6">
                                                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                                <circle cx="12" cy="12" r="12"/>
                                                            </svg>
                                                        </div>
                                                    @else
                                                        <div class="w-6 h-6 border-2 border-gray-300 rounded-full bg-gray-50"></div>
                                                    @endif
                                                </div>
                                            </div>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Main Learning Content -->
            <div class="main-content flex-1 lg:ml-8 space-y-6">
                <!-- Progress Overview Card -->
                <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-8 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h2 class="text-2xl font-bold mb-2">Progress Pembelajaran Anda</h2>
                            <p class="text-blue-100">Terus tingkatkan skill dan raih tujuan karir Anda!</p>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl font-bold mb-1">{{ $progressPercentage }}%</div>
                            <div class="text-blue-200 text-sm">Selesai</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-6">
                        <div class="flex justify-between text-sm text-blue-200 mb-2">
                            <span>{{ $completedLessons }} dari {{ $totalLessons }} materi selesai</span>
                            <span>{{ $totalLessons - $completedLessons }} materi tersisa</span>
                        </div>
                        <div class="w-full bg-blue-500/30 rounded-full h-3">
                            <div class="bg-white h-3 rounded-full transition-all duration-500" style="width: {{ $progressPercentage }}%"></div>
                        </div>
                    </div>

                    <!-- Learning Stats -->
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold">{{ $enrollment->created_at->diffInDays(now()) }}</div>
                            <div class="text-blue-200 text-sm">Hari Belajar</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">{{ floor($progressPercentage/25) }}</div>
                            <div class="text-blue-200 text-sm">Achievement</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">{{ $completedLessons }}</div>
                            <div class="text-blue-200 text-sm">Materi Selesai</div>
                        </div>
                    </div>
                </div>

                <!-- Continue Learning Section -->
                @if($nextLesson)
                    <div class="bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-gray-900 flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </div>
                                Lanjutkan Pembelajaran
                            </h3>
                            <span class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                Materi Selanjutnya
                            </span>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6">
                                <!-- Lesson Icon -->
                                @if($nextLesson->type === 'video')
                                    <div class="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </div>
                                @elseif($nextLesson->type === 'text')
                                    <div class="w-16 h-16 bg-green-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                @elseif($nextLesson->type === 'quiz')
                                    <div class="w-16 h-16 bg-purple-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                @else
                                    <div class="w-16 h-16 bg-orange-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                        </svg>
                                    </div>
                                @endif

                                <div>
                                    <h4 class="text-xl font-bold text-gray-900 mb-1">{{ $nextLesson->title }}</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                            </svg>
                                            {{ $nextLesson->chapter->title }}
                                        </span>
                                        @if($nextLesson->duration_minutes)
                                            <span class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                {{ $nextLesson->duration_minutes }} menit
                                            </span>
                                        @endif
                                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium capitalize">
                                            {{ $nextLesson->type }}
                                        </span>
                                    </div>
                                    <p class="text-gray-600 text-sm">Lanjutkan progress pembelajaran Anda dan tingkatkan skill!</p>
                                </div>
                            </div>

                            <div class="flex flex-col space-y-3">
                                <a href="{{ route('course.lesson', [$course, $nextLesson]) }}"
                                   class="bg-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200 flex items-center space-x-2 text-center">
                                    <span>Lanjutkan Belajar</span>
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </a>
                                <button class="bg-blue-100 text-blue-700 px-8 py-3 rounded-lg font-medium hover:bg-blue-200 transition-colors duration-200 flex items-center space-x-2 text-center">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    <span>Chat dengan NALA</span>
                                </button>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-8 border border-green-200">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">🎉 Selamat! Pembelajaran Selesai</h3>
                            <p class="text-gray-600 mb-6 text-lg">Anda telah menyelesaikan semua materi dalam kursus ini dengan baik!</p>
                            <div class="flex justify-center space-x-4">
                                <a href="{{ route('course.show', $course) }}"
                                   class="bg-white text-gray-700 px-6 py-3 rounded-lg font-semibold border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                                    Lihat Detail Kursus
                                </a>
                                <a href="{{ route('courses.index') }}"
                                   class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200">
                                    Jelajahi Kursus Lain
                                </a>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- NALA AI Assistant CTA -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 mb-2">Tingkatkan Pembelajaran dengan NALA AI</h3>
                                <p class="text-gray-600 mb-3">Dapatkan bantuan personal AI untuk memahami materi lebih dalam, latihan soal, dan panduan karir yang disesuaikan dengan progress Anda.</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Penjelasan materi personal
                                    </span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Latihan soal adaptif
                                    </span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Prediksi karir
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-3">
                            <button class="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                <span>Chat dengan NALA</span>
                            </button>
                            <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium text-center">
                                Upgrade ke NALA Membership →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Learning Progress Insights -->
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Progress Breakdown -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <div class="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            Progress Detail
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                    </div>
                                    <span class="font-medium text-gray-900">Materi Selesai</span>
                                </div>
                                <span class="text-lg font-bold text-green-600">{{ $completedLessons }}</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <span class="font-medium text-gray-900">Sisa Materi</span>
                                </div>
                                <span class="text-lg font-bold text-orange-600">{{ $totalLessons - $completedLessons }}</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                        </svg>
                                    </div>
                                    <span class="font-medium text-gray-900">Achievement</span>
                                </div>
                                <span class="text-lg font-bold text-blue-600">{{ floor($progressPercentage/25) }}/4</span>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Motivation -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <div class="w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                            </div>
                            Motivasi Belajar
                        </h3>
                        <div class="space-y-4">
                            @if($progressPercentage >= 75)
                                <div class="p-4 bg-green-50 rounded-lg border border-green-200">
                                    <h4 class="font-semibold text-green-800 mb-2">🎉 Hampir Selesai!</h4>
                                    <p class="text-green-700 text-sm">Anda sudah sangat dekat dengan menyelesaikan kursus ini. Tetap semangat!</p>
                                </div>
                            @elseif($progressPercentage >= 50)
                                <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                                    <h4 class="font-semibold text-blue-800 mb-2">💪 Setengah Perjalanan!</h4>
                                    <p class="text-blue-700 text-sm">Progress yang luar biasa! Anda sudah melewati setengah dari materi kursus.</p>
                                </div>
                            @elseif($progressPercentage >= 25)
                                <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                    <h4 class="font-semibold text-yellow-800 mb-2">🚀 Momentum Bagus!</h4>
                                    <p class="text-yellow-700 text-sm">Anda sudah membangun momentum yang baik. Lanjutkan pembelajaran!</p>
                                </div>
                            @else
                                <div class="p-4 bg-purple-50 rounded-lg border border-purple-200">
                                    <h4 class="font-semibold text-purple-800 mb-2">🌟 Mulai Perjalanan!</h4>
                                    <p class="text-purple-700 text-sm">Setiap ahli pernah menjadi pemula. Mulai perjalanan pembelajaran Anda!</p>
                                </div>
                            @endif

                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900 mb-1">{{ $enrollment->created_at->diffInDays(now()) }} Hari</div>
                                <div class="text-gray-600 text-sm">Sudah belajar di kursus ini</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                        <div class="w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center mr-2">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        Aksi Cepat
                    </h3>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="quick-action-btn flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 group">
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-600 transition-colors">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Download Materi</span>
                        </button>

                        <button class="quick-action-btn flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 group">
                            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mb-2 group-hover:bg-green-600 transition-colors">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Catatan</span>
                        </button>

                        <button class="quick-action-btn flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 group">
                            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mb-2 group-hover:bg-purple-600 transition-colors">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Forum</span>
                        </button>

                        <button class="quick-action-btn flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 group">
                            <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mb-2 group-hover:bg-red-600 transition-colors">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Bagikan</span>
                        </button>
                    </div>
                </div>

                <!-- Instructor Card -->
                <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Tentang Instruktur</h3>
                    <div class="flex items-center space-x-6">
                        <div class="w-16 h-16 bg-primary rounded-xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900">{{ $course->tutor->name }}</h4>
                            <p class="text-gray-600 mb-2">Instruktur Kursus</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                    </svg>
                                    Instruktur Berpengalaman
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    1,200+ Students
                                </span>
                            </div>
                        </div>
                        <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200 text-sm font-medium">
                            Kirim Pesan
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden"></div>
</div>

@push('scripts')
<script>
// Course Learning Page JavaScript - Scoped to avoid conflicts
(function() {
    'use strict';

    // Only run on course learning page
    if (!document.querySelector('.course-learning-page')) {
        return;
    }

    class CourseLearningPage {
        constructor() {
            this.curriculumToggle = document.getElementById('curriculum-toggle');
            this.curriculumSidebar = document.getElementById('curriculum-sidebar');
            this.sidebarClose = document.getElementById('sidebar-close');
            this.sidebarOverlay = document.getElementById('sidebar-overlay');

            this.init();
        }

        init() {
            this.bindEvents();
            this.setupProgressAnimations();
            this.setupStatsCardAnimations();
        }

        bindEvents() {
            // Toggle sidebar on mobile
            if (this.curriculumToggle) {
                this.curriculumToggle.addEventListener('click', () => this.openSidebar());
            }

            // Close sidebar events
            if (this.sidebarClose) {
                this.sidebarClose.addEventListener('click', () => this.closeSidebar());
            }

            if (this.sidebarOverlay) {
                this.sidebarOverlay.addEventListener('click', () => this.closeSidebar());
            }

            // Close sidebar on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeSidebar();
                }
            });
        }

        openSidebar() {
            if (this.curriculumSidebar) {
                this.curriculumSidebar.classList.remove('collapsed');
            }
            if (this.sidebarOverlay) {
                this.sidebarOverlay.classList.remove('hidden');
            }
        }

        closeSidebar() {
            if (this.curriculumSidebar) {
                this.curriculumSidebar.classList.add('collapsed');
            }
            if (this.sidebarOverlay) {
                this.sidebarOverlay.classList.add('hidden');
            }
        }

        setupProgressAnimations() {
            // Animate progress rings
            const progressRings = document.querySelectorAll('.course-learning-page .progress-ring-circle');
            progressRings.forEach(ring => {
                const strokeDasharray = ring.getAttribute('stroke-dasharray');
                if (strokeDasharray) {
                    ring.style.strokeDasharray = '0, 100';
                    setTimeout(() => {
                        ring.style.strokeDasharray = strokeDasharray;
                    }, 500);
                }
            });
        }

        setupStatsCardAnimations() {
            // Add intersection observer for stats cards
            const statsCards = document.querySelectorAll('.course-learning-page .stats-card');

            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, { threshold: 0.1 });

                statsCards.forEach(card => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    observer.observe(card);
                });
            }
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        new CourseLearningPage();
    });

})();
</script>
@endpush

