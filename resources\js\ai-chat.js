/**
 * Nala AI Assistant for Ngambiskuy
 * Ngambiskuy Advance Learning Assistance (NALA)
 *
 * This module handles the AI chat functionality including:
 * - Route-aware responses
 * - Course recommendations
 * - Career path analysis
 * - Personalized learning paths
 * - Integration with Gemini AI
 */

class NalaAIAssistant {
    constructor() {
        this.isOpen = false;
        this.messageHistory = [];
        this.isTyping = false;
        this.apiEndpoint = '/api/nala-chat'; // Backend endpoint for Gemini AI
        this.userProfile = null;
        this.currentRoute = null;
        this.currentContext = null;
        this.isAuthenticated = false;
        this.currentConversationId = null; // Track current conversation

        this.init();
    }

    init() {
        this.loadConfig();
        this.detectRoute();
        this.bindEvents();
        this.loadUserProfile();
        this.initializeWelcomeMessage();
        this.updateQuickActions();
        this.loadChatHistory(); // Load chat history from database
    }

    loadConfig() {
        // Load configuration from window.nalaConfig set by Blade template
        if (window.nalaConfig) {
            this.isAuthenticated = window.nalaConfig.isAuthenticated;
            this.userProfile = window.nalaConfig.user || null;
            this.currentRoute = window.nalaConfig.currentRoute;
            this.currentUrl = window.nalaConfig.currentUrl;
            this.csrfToken = window.nalaConfig.csrfToken;
        }
    }

    detectRoute() {
        // Route mapping based on actual Laravel routes in web.php
        const routeContextMap = {
            // Public routes
            'home': 'homepage',
            'courses.index': 'course_listing',
            'exams.index': 'exam_listing',
            'exams.show': 'exam_detail',
            'exams.take': 'exam_taking',
            'exams.result': 'exam_result',
            'blog.index': 'blog_listing',
            'blog.show': 'blog_detail',
            'blog.category': 'blog_category',
            'course.show': 'course_detail',
            'course.learn': 'learning_page',
            'tutor.public-profile': 'tutor_profile',
            'payment.pricing': 'pricing_page',

            // User Dashboard routes
            'user.dashboard': 'user_dashboard',
            'user.profile': 'user_profile',
            'user.courses': 'user_courses',
            'user.exams': 'user_exams',
            'user.blog': 'user_blog',
            'user.progress': 'user_progress',
            'user.certificates': 'user_certificates',
            'user.settings': 'user_settings',
            'user.membership': 'user_membership',

            // Tutor Dashboard routes (matching actual web.php routes)
            'tutor.dashboard': 'tutor_dashboard',
            'tutor.courses': 'tutor_courses',
            'tutor.create-course': 'tutor_create_course',
            'tutor.edit-course': 'tutor_edit_course',
            'tutor.curriculum.index': 'tutor_curriculum',
            'tutor.curriculum.create-material': 'tutor_create_material',
            'tutor.curriculum.edit-material': 'tutor_edit_material',
            'tutor.exams': 'tutor_exams',
            'tutor.exams.create': 'tutor_create_exam',
            'tutor.exams.edit': 'tutor_edit_exam',
            'tutor.exams.show': 'tutor_exam_detail',
            'tutor.blogs': 'tutor_blogs',
            'tutor.blogs.create': 'tutor_create_blog',
            'tutor.blogs.edit': 'tutor_edit_blog',
            'tutor.blogs.show': 'tutor_blog_detail',
            'tutor.students': 'tutor_students',
            'tutor.analytics': 'tutor_analytics',
            'tutor.earnings': 'tutor_earnings',
            'tutor.profile': 'tutor_profile_edit',
            'tutor.settings': 'tutor_settings',

            // Tutor Registration routes
            'tutor.register.terms': 'tutor_register_terms',
            'tutor.register.profile': 'tutor_register_profile',
            'tutor.register.review': 'tutor_register_review',
            'tutor.register.status': 'tutor_register_status',

            // Payment routes
            'payment.membership.checkout': 'membership_checkout',
            'payment.course.checkout': 'course_checkout'
        };

        this.currentContext = routeContextMap[this.currentRoute] || 'general';
        console.log('Nala detected route:', this.currentRoute, 'Context:', this.currentContext);

        // Extract route-specific parameters and context
        this.extractRouteParameters();
        this.extractCourseContext();
        this.determineDataAccessLevel();
    }

    /**
     * Extract route-specific parameters (IDs, etc.)
     */
    extractRouteParameters() {
        this.routeParameters = {};

        // Extract course ID from URL for course-related pages
        if (this.currentRoute === 'course.show' || this.currentRoute === 'course.learn') {
            const pathSegments = window.location.pathname.split('/');
            const courseIndex = pathSegments.findIndex(segment => segment === 'course' || segment === 'courses');
            if (courseIndex !== -1 && pathSegments[courseIndex + 1]) {
                this.routeParameters.courseId = pathSegments[courseIndex + 1];
            }
        }

        // Extract exam ID from URL for exam-related pages
        if (this.currentRoute === 'exams.show' || this.currentRoute === 'exams.take') {
            const pathSegments = window.location.pathname.split('/');
            const examIndex = pathSegments.findIndex(segment => segment === 'exam' || segment === 'exams');
            if (examIndex !== -1 && pathSegments[examIndex + 1]) {
                this.routeParameters.examId = pathSegments[examIndex + 1];
            }
        }

        // Extract tutor ID for tutor profile pages
        if (this.currentRoute === 'tutor.public-profile') {
            const pathSegments = window.location.pathname.split('/');
            const tutorIndex = pathSegments.findIndex(segment => segment === 'tutor');
            if (tutorIndex !== -1 && pathSegments[tutorIndex + 1]) {
                this.routeParameters.tutorId = pathSegments[tutorIndex + 1];
            }
        }

        console.log('Extracted route parameters:', this.routeParameters);
    }

    /**
     * Determine data access level based on route and enrollment
     */
    determineDataAccessLevel() {
        this.dataAccessLevel = {
            courses: 'basic', // basic, detailed, enrolled_content
            exams: 'basic',   // basic, detailed, enrolled_content
            career: true,     // always include career data
            realtime: false,  // real-time content access
            page_specific: true // include page-specific database data
        };

        // Route-specific data access rules
        switch (this.currentContext) {
            // Public pages
            case 'homepage':
                this.dataAccessLevel.courses = 'detailed';
                this.dataAccessLevel.exams = 'detailed';
                break;

            case 'course_listing':
                this.dataAccessLevel.courses = 'detailed';
                this.dataAccessLevel.exams = 'basic';
                break;

            case 'exam_listing':
            case 'exam_detail':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'detailed';
                break;

            case 'course_detail':
                this.dataAccessLevel.courses = 'detailed';
                this.dataAccessLevel.exams = 'none';
                break;

            case 'learning_page':
                this.dataAccessLevel.courses = 'enrolled_content';
                this.dataAccessLevel.exams = 'none';
                this.dataAccessLevel.realtime = true;
                break;

            case 'exam_taking':
                // Disable Nala during exam taking
                this.dataAccessLevel.courses = 'none';
                this.dataAccessLevel.exams = 'none';
                this.dataAccessLevel.career = false;
                this.dataAccessLevel.page_specific = false;
                this.disableNalaDuringExam();
                break;

            case 'blog_listing':
            case 'blog_detail':
            case 'blog_category':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            // User Dashboard pages
            case 'user_dashboard':
                this.dataAccessLevel.courses = 'detailed';
                this.dataAccessLevel.exams = 'detailed';
                break;

            case 'user_courses':
                this.dataAccessLevel.courses = 'enrolled_content';
                this.dataAccessLevel.exams = 'basic';
                break;

            case 'user_exams':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'enrolled_content';
                break;

            case 'user_progress':
                this.dataAccessLevel.courses = 'enrolled_content';
                this.dataAccessLevel.exams = 'enrolled_content';
                break;

            case 'user_certificates':
                this.dataAccessLevel.courses = 'enrolled_content';
                this.dataAccessLevel.exams = 'enrolled_content';
                break;

            case 'user_blog':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            case 'user_profile':
            case 'user_settings':
            case 'user_membership':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            // Tutor Dashboard pages
            case 'tutor_dashboard':
                this.dataAccessLevel.courses = 'tutor_content';
                this.dataAccessLevel.exams = 'tutor_content';
                break;

            case 'tutor_courses':
            case 'tutor_create_course':
            case 'tutor_edit_course':
            case 'tutor_curriculum':
            case 'tutor_create_material':
            case 'tutor_edit_material':
                this.dataAccessLevel.courses = 'tutor_content';
                this.dataAccessLevel.exams = 'basic';
                break;

            case 'tutor_exams':
            case 'tutor_create_exam':
            case 'tutor_edit_exam':
            case 'tutor_exam_detail':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'tutor_content';
                break;

            case 'tutor_blogs':
            case 'tutor_create_blog':
            case 'tutor_edit_blog':
            case 'tutor_blog_detail':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            case 'tutor_students':
            case 'tutor_analytics':
            case 'tutor_earnings':
                this.dataAccessLevel.courses = 'tutor_content';
                this.dataAccessLevel.exams = 'tutor_content';
                break;

            case 'tutor_profile_edit':
            case 'tutor_settings':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            // Tutor Registration pages
            case 'tutor_register_terms':
            case 'tutor_register_profile':
            case 'tutor_register_review':
            case 'tutor_register_status':
                this.dataAccessLevel.courses = 'basic';
                this.dataAccessLevel.exams = 'basic';
                break;

            // Payment pages
            case 'pricing_page':
            case 'membership_checkout':
            case 'course_checkout':
                this.dataAccessLevel.courses = 'detailed';
                this.dataAccessLevel.exams = 'detailed';
                break;

            default:
                // Keep basic access for other routes
                break;
        }

        console.log('Data access level determined:', this.dataAccessLevel);
    }

    /**
     * Disable Nala during exam taking
     */
    disableNalaDuringExam() {
        const chatToggle = document.getElementById('nala-chat-toggle');
        const chatContainer = document.getElementById('nala-chat-container');

        if (chatToggle && chatContainer) {
            // Hide Nala completely during exam
            chatContainer.style.display = 'none';

            // Show a subtle message that Nala will be available after exam
            this.showExamModeMessage();
        }
    }

    /**
     * Show exam mode message
     */
    showExamModeMessage() {
        // Create a small notification that Nala is disabled during exam
        const examNotice = document.createElement('div');
        examNotice.id = 'nala-exam-notice';
        examNotice.className = 'fixed bottom-4 right-4 bg-purple-100 text-purple-800 px-4 py-2 rounded-lg text-sm shadow-lg z-40';
        examNotice.innerHTML = `
            <div class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Nala akan tersedia setelah ujian selesai</span>
            </div>
        `;

        document.body.appendChild(examNotice);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (examNotice && examNotice.parentNode) {
                examNotice.parentNode.removeChild(examNotice);
            }
        }, 5000);
    }

    extractCourseContext() {
        this.courseContext = {};

        // Extract course filters from URL if on courses.index
        if (this.currentRoute === 'courses.index') {
            const urlParams = new URLSearchParams(window.location.search);
            this.courseContext.filters = {
                category: urlParams.get('category'),
                level: urlParams.get('level'),
                price_type: urlParams.get('price_type'),
                search: urlParams.get('search'),
                sort: urlParams.get('sort')
            };
        }

        // Extract course details if on course.show or course.learn
        if (this.currentRoute === 'course.show' || this.currentRoute === 'course.learn') {
            // Try to extract course data from page elements
            const courseTitle = document.querySelector('h1')?.textContent?.trim();
            const coursePrice = document.querySelector('[data-course-price]')?.textContent?.trim();
            const courseLevel = document.querySelector('[data-course-level]')?.textContent?.trim();
            const courseDescription = document.querySelector('[data-course-description]')?.textContent?.trim();

            // Extract additional course information
            const tutorName = document.querySelector('h1')?.parentElement?.querySelector('span')?.textContent?.trim();
            const studentsCount = document.querySelector('span')?.textContent?.match(/(\d+(?:,\d+)*)\s+siswa/)?.[1];
            const rating = document.querySelector('span')?.textContent?.match(/(\d+\.?\d*)\s+\(/)?.[1];
            const category = document.querySelector('nav a[href*="category"]')?.textContent?.trim();

            if (courseTitle) {
                this.courseContext.course = {
                    title: courseTitle,
                    price: coursePrice,
                    level: courseLevel,
                    description: courseDescription,
                    tutor: tutorName,
                    students_count: studentsCount,
                    rating: rating,
                    category: category,
                    is_free: coursePrice?.includes('Gratis') || coursePrice?.includes('Free'),
                    url: window.location.href
                };

                console.log('Course context extracted:', this.courseContext.course);
            }

            // For learning page, extract current lesson/chapter info
            if (this.currentRoute === 'course.learn') {
                this.extractLearningContext();
            }
        }

        // Extract exam context for exam pages
        if (this.currentRoute === 'exams.index' || this.currentRoute === 'exams.show' || this.currentRoute === 'exams.take') {
            this.extractExamContext();
        }
    }

    /**
     * Extract learning context for real-time assistance
     */
    extractLearningContext() {
        this.learningContext = {};

        // Extract current chapter/lesson information
        const currentChapter = document.querySelector('[data-current-chapter]')?.textContent?.trim();
        const currentLesson = document.querySelector('[data-current-lesson]')?.textContent?.trim();
        const lessonContent = document.querySelector('[data-lesson-content]')?.textContent?.trim();

        if (currentChapter || currentLesson) {
            this.learningContext = {
                current_chapter: currentChapter,
                current_lesson: currentLesson,
                lesson_content: lessonContent ? lessonContent.substring(0, 500) : null, // Limit content length
                course_id: this.routeParameters.courseId,
                timestamp: new Date().toISOString()
            };
        }

        console.log('Learning context extracted:', this.learningContext);
    }

    /**
     * Extract exam context
     */
    extractExamContext() {
        this.examContext = {};

        if (this.currentRoute === 'exams.index') {
            // Extract exam filters from URL
            const urlParams = new URLSearchParams(window.location.search);
            this.examContext.filters = {
                category: urlParams.get('category'),
                difficulty: urlParams.get('difficulty'),
                price_type: urlParams.get('price_type'),
                search: urlParams.get('search')
            };
        }

        if (this.currentRoute === 'exams.show') {
            // Extract exam details from page
            const examTitle = document.querySelector('h1')?.textContent?.trim();
            const examDifficulty = document.querySelector('[data-exam-difficulty]')?.textContent?.trim();
            const examPrice = document.querySelector('[data-exam-price]')?.textContent?.trim();

            if (examTitle) {
                this.examContext.exam = {
                    title: examTitle,
                    difficulty: examDifficulty,
                    price: examPrice,
                    is_free: examPrice?.includes('Gratis') || examPrice?.includes('Free')
                };
            }
        }

        console.log('Exam context extracted:', this.examContext);
    }
    
    bindEvents() {
        const chatToggle = document.getElementById('nala-chat-toggle');
        const chatInput = document.getElementById('nala-input');
        const sendButton = document.getElementById('nala-send-message');
        const deleteButton = document.getElementById('nala-delete-chat');
        const quickActionBtns = document.querySelectorAll('.nala-quick-action-btn');

        if (chatToggle) {
            chatToggle.addEventListener('click', () => this.toggleChat());
        }

        if (chatInput) {
            chatInput.addEventListener('input', (e) => this.handleInputChange(e));
            chatInput.addEventListener('keypress', (e) => this.handleKeyPress(e));
        }

        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (deleteButton) {
            deleteButton.addEventListener('click', () => this.handleDeleteChat());
        }

        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickAction(e));
        });

        // Close chat when clicking outside
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    }
    
    toggleChat() {
        const chatWindow = document.getElementById('nala-chat-window');
        const nalaAvatar = document.getElementById('nala-avatar');
        const chatIcon = document.getElementById('chat-icon');
        const closeIcon = document.getElementById('close-icon');

        this.isOpen = !this.isOpen;

        if (this.isOpen) {
            chatWindow.classList.remove('hidden');
            setTimeout(() => {
                chatWindow.classList.remove('scale-0', 'opacity-0');
                chatWindow.classList.add('scale-100', 'opacity-100');
            }, 10);
            document.getElementById('nala-input').focus();

            // Switch to close icon
            if (nalaAvatar) nalaAvatar.classList.add('hidden');
            if (chatIcon) chatIcon.classList.add('hidden');
            if (closeIcon) closeIcon.classList.remove('hidden');

            // Hide notification badge when chat is opened
            this.hideNotification();
        } else {
            chatWindow.classList.remove('scale-100', 'opacity-100');
            chatWindow.classList.add('scale-0', 'opacity-0');
            setTimeout(() => {
                chatWindow.classList.add('hidden');
            }, 300);

            // Switch back to Nala avatar
            if (nalaAvatar) nalaAvatar.classList.remove('hidden');
            if (chatIcon) chatIcon.classList.add('hidden');
            if (closeIcon) closeIcon.classList.add('hidden');
        }
    }
    
    handleInputChange(e) {
        const length = e.target.value.length;
        const charCount = document.getElementById('nala-char-count');
        const sendButton = document.getElementById('nala-send-message');

        if (charCount) charCount.textContent = `${length}/500`;
        if (sendButton) sendButton.disabled = length === 0;

        if (charCount && length > 450) {
            charCount.classList.add('text-red-500');
        } else if (charCount) {
            charCount.classList.remove('text-red-500');
        }

        // Auto-resize textarea
        this.autoResizeTextarea(e.target);
    }

    autoResizeTextarea(textarea) {
        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';

        // Calculate new height based on content
        const newHeight = Math.min(textarea.scrollHeight, 120); // Max height 120px
        textarea.style.height = newHeight + 'px';
    }

    handleKeyPress(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    handleQuickAction(e) {
        const action = e.target.getAttribute('data-action') || e.target.textContent.trim();
        const chatInput = document.getElementById('nala-input');
        const sendButton = document.getElementById('nala-send-message');

        const message = this.getQuickActionMessage(action);
        if (chatInput) chatInput.value = message;
        if (chatInput) chatInput.focus();
        if (sendButton) sendButton.disabled = false;

        // Update character count
        const event = new Event('input');
        if (chatInput) chatInput.dispatchEvent(event);
    }

    updateQuickActions() {
        // Update quick actions based on current route/context
        const quickActionsContainer = document.getElementById('nala-quick-actions');
        if (!quickActionsContainer) return;

        const contextActions = this.getContextualQuickActions();

        // Clear existing actions
        quickActionsContainer.innerHTML = '';

        // Add context-specific actions
        contextActions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'nala-quick-action-btn px-3 py-1 bg-purple-50 text-purple-700 rounded-full text-xs hover:bg-purple-100 transition-colors';
            button.setAttribute('data-action', action.key);
            button.textContent = action.label;
            button.addEventListener('click', (e) => this.handleQuickAction(e));
            quickActionsContainer.appendChild(button);
        });
    }

    getContextualQuickActions() {
        const baseActions = [
            { key: 'course-recommendation', label: 'Rekomendasi Kursus' },
            { key: 'career-path', label: 'Jalur Karir' },
            { key: 'help', label: 'Bantuan' }
        ];

        const contextActions = {
            'homepage': [
                { key: 'getting-started', label: 'Mulai Belajar' },
                { key: 'popular-courses', label: 'Kursus Populer' }
            ],
            'user_dashboard': [
                { key: 'continue-learning', label: 'Lanjutkan Belajar' },
                { key: 'progress-analysis', label: 'Analisis Progress' }
            ],
            'tutor_dashboard': [
                { key: 'course-creation-tips', label: 'Tips Membuat Kursus' },
                { key: 'student-engagement', label: 'Engagement Siswa' }
            ],
            'course_detail': [
                { key: 'course-info', label: 'Info Kursus' },
                { key: 'prerequisites', label: 'Prasyarat' },
                { key: 'course-benefits', label: 'Manfaat Kursus' },
                { key: 'career-impact', label: 'Dampak Karir' }
            ],
            'general': [
                { key: 'course-recommendation', label: 'Rekomendasi Kursus' },
                { key: 'course-comparison', label: 'Bandingkan Kursus' },
                { key: 'learning-path', label: 'Jalur Pembelajaran' }
            ],
            'learning_page': [
                { key: 'learning-help', label: 'Bantuan Belajar' },
                { key: 'concept-explanation', label: 'Jelaskan Konsep' }
            ],
            'exam_taking': [
                { key: 'exam-tips', label: 'Tips Ujian' },
                { key: 'time-management', label: 'Manajemen Waktu' }
            ]
        };

        const currentContextActions = contextActions[this.currentContext] || [];
        return [...currentContextActions, ...baseActions];
    }
    
    handleOutsideClick(e) {
        const chatToggle = document.getElementById('nala-chat-toggle');
        const chatWindow = document.getElementById('nala-chat-window');

        if (this.isOpen &&
            !chatToggle.contains(e.target) &&
            !chatWindow.contains(e.target)) {
            this.toggleChat();
        }
    }

    getQuickActionMessage(action) {
        const contextMessages = {
            // Base actions
            'course-recommendation': 'Bisakah Anda merekomendasikan kursus yang tepat untuk saya?',
            'career-path': 'Saya ingin tahu jalur karir yang cocok dengan skill saya',
            'help': 'Saya butuh bantuan menggunakan platform Ngambiskuy',

            // Context-specific actions
            'getting-started': 'Bagaimana cara memulai belajar di Ngambiskuy?',
            'popular-courses': 'Apa saja kursus yang paling populer saat ini?',
            'continue-learning': 'Bagaimana cara melanjutkan pembelajaran saya?',
            'progress-analysis': 'Bisakah Anda menganalisis progress belajar saya?',
            'course-creation-tips': 'Berikan tips untuk membuat kursus yang menarik',
            'student-engagement': 'Bagaimana cara meningkatkan engagement siswa?',
            'course-info': 'Jelaskan lebih detail tentang kursus ini',
            'prerequisites': 'Apa saja prasyarat untuk mengikuti kursus ini?',
            'course-benefits': 'Apa manfaat yang akan saya dapatkan dari kursus ini?',
            'career-impact': 'Bagaimana kursus ini akan membantu karir saya?',
            'course-comparison': 'Bandingkan kursus ini dengan alternatif lainnya',
            'learning-path': 'Buatkan jalur pembelajaran yang tepat untuk saya',
            'learning-help': 'Saya butuh bantuan memahami materi ini',
            'concept-explanation': 'Bisakah Anda menjelaskan konsep ini dengan lebih sederhana?',
            'exam-tips': 'Berikan tips untuk mengerjakan ujian ini',
            'time-management': 'Bagaimana cara mengatur waktu saat ujian?'
        };

        return contextMessages[action] || action;
    }

    /**
     * Handle delete chat button click
     */
    handleDeleteChat() {
        if (!this.isAuthenticated) {
            return;
        }

        // Show confirmation dialog
        if (confirm('Apakah Anda yakin ingin menghapus semua riwayat chat dengan Nala? Tindakan ini tidak dapat dibatalkan.')) {
            this.clearChatHistory();
        }
    }
    
    async sendMessage() {
        const chatInput = document.getElementById('nala-input');
        const message = chatInput.value.trim();

        if (!message || this.isTyping) return;

        // Check if user is authenticated for full functionality
        if (!this.isAuthenticated) {
            this.showLoginPrompt();
            return;
        }

        // Add user message
        this.addMessage(message, 'user');

        // Clear input and reset textarea height
        chatInput.value = '';
        chatInput.style.height = 'auto';
        const charCount = document.getElementById('nala-char-count');
        const sendButton = document.getElementById('nala-send-message');
        if (charCount) charCount.textContent = '0/500';
        if (sendButton) sendButton.disabled = true;

        // Show typing indicator
        this.showTypingIndicator();
        this.isTyping = true;

        try {
            // Call AI service (Gemini AI integration point)
            const response = await this.callAIService(message);
            this.hideTypingIndicator();
            this.addMessage(response, 'ai');
        } catch (error) {
            console.error('Nala AI Error:', error);
            this.hideTypingIndicator();
            this.addMessage('Maaf, terjadi kesalahan. Silakan coba lagi nanti.', 'ai');
        } finally {
            this.isTyping = false;
        }
    }

    showLoginPrompt() {
        const loginPrompt = document.getElementById('nala-login-prompt');
        if (loginPrompt) {
            loginPrompt.classList.remove('hidden');
            const messagesContainer = document.getElementById('nala-messages');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }
    }
    
    async callAIService(message) {
        // Real Gemini AI integration
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                },
                body: JSON.stringify({
                    message: message,
                    context: this.getConversationContext(),
                    current_route: this.currentRoute,
                    current_context: this.currentContext,
                    current_url: this.currentUrl,
                    course_context: this.courseContext,
                    exam_context: this.examContext,
                    learning_context: this.learningContext,
                    route_parameters: this.routeParameters,
                    data_access_level: this.dataAccessLevel,
                    conversation_id: this.currentConversationId
                })
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'AI service error');
            }

            // Handle membership-specific responses
            if (data.limit_reached || data.api_limit_reached) {
                this.handleMembershipLimit(data);
            }

            // Update conversation ID if returned
            if (data.conversation_id) {
                this.currentConversationId = data.conversation_id;
            }

            return data.response;
        } catch (error) {
            console.error('Nala AI Service Error:', error);
            // Fallback to context-aware response if API fails
            return this.generateContextAwareResponse(message);
        }
    }

    generateContextAwareResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // Course-specific responses with psychological sales techniques
        if (this.currentRoute === 'courses.index') {
            if (lowerMessage.includes('rekomendasi') || lowerMessage.includes('pilih') || lowerMessage.includes('bagus')) {
                return 'Saya melihat Anda sedang mencari kursus yang tepat! 🎯 Untuk memberikan rekomendasi terbaik, boleh saya tahu: 1) Apa tujuan karir Anda? 2) Berapa waktu yang bisa Anda dedikasikan per hari? 3) Apakah Anda pemula atau sudah ada pengalaman? Dengan info ini, saya bisa carikan kursus yang akan membawa Anda ke level berikutnya!';
            }
            if (lowerMessage.includes('harga') || lowerMessage.includes('mahal') || lowerMessage.includes('murah')) {
                return 'Saya paham budget adalah pertimbangan penting! 💰 Tapi ingat, investasi di skill adalah investasi terbaik untuk masa depan. Kursus premium kami rata-rata memberikan ROI 300-500% dalam 6 bulan pertama. Plus, banyak alumni yang langsung dapat kenaikan gaji atau job baru. Mau saya carikan yang sesuai budget Anda?';
            }
        }

        if (this.currentRoute === 'course.show') {
            if (lowerMessage.includes('worth') || lowerMessage.includes('layak') || lowerMessage.includes('bagus')) {
                return 'Kursus ini sudah terbukti mengubah karir 1000+ alumni! 🚀 Yang membuat spesial: 1) Kurikulum industry-standard, 2) Mentor berpengalaman, 3) Project portfolio, 4) Job placement assistance. Bayangkan 3 bulan dari sekarang Anda sudah mahir dan siap apply ke perusahaan impian. Kapan lagi ada kesempatan seperti ini?';
            }
            if (lowerMessage.includes('waktu') || lowerMessage.includes('sibuk') || lowerMessage.includes('schedule')) {
                return 'Saya tahu Anda sibuk, tapi justru itu alasan kenapa skill ini penting! ⏰ Kursus ini dirancang fleksibel - cukup 1-2 jam per hari. Banyak peserta yang berhasil sambil kerja full-time. Daripada scrolling sosmed, lebih baik invest waktu untuk masa depan yang lebih cerah. Ready to start?';
            }
        }

        // Route-specific responses
        if (this.currentContext === 'learning_page' && (lowerMessage.includes('bantuan') || lowerMessage.includes('help'))) {
            return 'Saya melihat Anda sedang belajar! Apakah ada konsep tertentu yang sulit dipahami? Saya bisa menjelaskan dengan cara yang lebih sederhana atau memberikan contoh praktis.';
        }

        if (this.currentContext === 'exam_taking' && (lowerMessage.includes('tips') || lowerMessage.includes('ujian'))) {
            return 'Tips untuk ujian: 1) Baca soal dengan teliti, 2) Kerjakan yang mudah dulu, 3) Kelola waktu dengan baik, 4) Jangan panik jika ada soal sulit. Anda bisa melakukannya!';
        }

        if (this.currentContext === 'tutor_dashboard' && lowerMessage.includes('kursus')) {
            return 'Sebagai tutor, pastikan kursus Anda memiliki: 1) Tujuan pembelajaran yang jelas, 2) Materi terstruktur, 3) Video berkualitas, 4) Quiz dan latihan, 5) Sertifikat. Butuh bantuan spesifik?';
        }

        if (this.currentContext === 'user_dashboard' && lowerMessage.includes('progress')) {
            return 'Berdasarkan dashboard Anda, saya bisa membantu menganalisis progress belajar dan memberikan rekomendasi untuk meningkatkan hasil pembelajaran. Mau saya analisis lebih detail?';
        }

        // Fall back to general responses
        return this.generatePlaceholderResponse(userMessage);
    }
    
    generatePlaceholderResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();
        
        // Intelligent Course Engine responses
        if (lowerMessage.includes('rekomendasi') || lowerMessage.includes('kursus')) {
            return this.getCourseRecommendation();
        }
        
        if (lowerMessage.includes('karir') || lowerMessage.includes('pekerjaan')) {
            return this.getCareerPathAdvice();
        }
        
        if (lowerMessage.includes('belajar') || lowerMessage.includes('pembelajaran')) {
            return this.getLearningPathSuggestion();
        }
        
        if (lowerMessage.includes('bantuan') || lowerMessage.includes('help')) {
            return this.getHelpResponse();
        }
        
        // Default response
        return 'Terima kasih atas pertanyaan Anda! Saya adalah AI Assistant Ngambiskuy yang dapat membantu dengan rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran. Apa yang ingin Anda ketahui?';
    }
    
    getCourseRecommendation() {
        const recommendations = [
            'Berdasarkan tren industri tech, saya merekomendasikan kursus Frontend Development dengan React. Teknologi ini sangat diminati dan cocok untuk pemula maupun yang ingin upgrade skill.',
            'Untuk pemula, saya sarankan mulai dengan "Fundamental Programming" kemudian lanjut ke "Web Development". Jalur ini dirancang khusus untuk mencapai job-ready dalam 3-6 bulan.',
            'Data Science sedang sangat diminati! Kursus "Python for Data Analysis" sangat cocok untuk yang ingin berkarir di bidang teknologi dan analisis data.',
            'UI/UX Design adalah skill yang sangat dibutuhkan. Kursus design thinking dan prototyping bisa jadi pilihan bagus untuk memulai karir di tech.',
            'Mobile Development dengan Flutter atau React Native sedang trending. Cocok untuk yang ingin membuat aplikasi mobile modern.'
        ];

        return recommendations[Math.floor(Math.random() * recommendations.length)];
    }
    
    getCareerPathAdvice() {
        const careerAdvice = [
            'Berdasarkan tren industri tech Indonesia, posisi yang paling dibutuhkan adalah: Frontend Developer, Data Analyst, dan UI/UX Designer. Mana yang menarik bagi Anda?',
            'Untuk menjadi Full Stack Developer, Anda perlu menguasai: Frontend (React/Vue), Backend (Node.js/Laravel), Database (MySQL/MongoDB), dan DevOps basics. Estimasi waktu: 6-9 bulan dengan pembelajaran intensif.',
            'Karir di Data Science sangat menjanjikan! Skill yang perlu dikuasai: Python, Machine Learning, dan SQL. Mau saya bantu buatkan roadmap belajarnya?',
            'Mobile Developer sangat dibutuhkan sekarang. Pilih antara native (Android/iOS) atau cross-platform (Flutter/React Native) sesuai minat Anda.',
            'Cybersecurity specialist semakin dicari. Mulai dari network security, ethical hacking, atau information security sesuai background Anda.'
        ];

        return careerAdvice[Math.floor(Math.random() * careerAdvice.length)];
    }
    
    getLearningPathSuggestion() {
        const pathSuggestions = [
            'Rekomendasi jalur pembelajaran untuk Frontend Developer: 1) JavaScript Fundamentals (2 minggu), 2) React Basics (3 minggu), 3) Project-based Learning (4 minggu). Total estimasi: 9 minggu untuk menjadi job-ready.',
            'Jalur Data Science yang saya sarankan: 1) Python Basics (3 minggu), 2) Data Analysis dengan Pandas (2 minggu), 3) Machine Learning Fundamentals (4 minggu). Cocok untuk pemula!',
            'Path UI/UX Designer: 1) Design Thinking (2 minggu), 2) Figma/Adobe XD (3 minggu), 3) User Research & Testing (3 minggu). Siap jadi designer profesional!',
            'Mobile Development roadmap: 1) Programming Fundamentals (3 minggu), 2) Flutter/React Native (4 minggu), 3) App Publishing (2 minggu). Total 9 minggu jadi mobile developer!'
        ];

        return pathSuggestions[Math.floor(Math.random() * pathSuggestions.length)];
    }
    
    getHelpResponse() {
        return 'Saya siap membantu! Berikut yang bisa saya lakukan:\n\n• 🎯 Rekomendasi kursus tech yang sesuai minat Anda\n• 📈 Saran jalur karir di bidang teknologi\n• 🗺️ Roadmap pembelajaran yang terstruktur\n• 💼 Info tentang skill yang dibutuhkan industri tech\n\nApa yang ingin Anda ketahui lebih lanjut?';
    }
    
    addMessage(content, sender) {
        const chatMessages = document.getElementById('nala-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-4 message-animation';

        const timestamp = new Date().toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit'
        });

        if (sender === 'user') {
            messageDiv.innerHTML = this.getUserMessageHTML(content, timestamp);

            // Extract profile data from user message
            if (this.isAuthenticated) {
                this.parseProfileDataFromResponse('', content);
            }
        } else {
            messageDiv.innerHTML = this.getNalaMessageHTML(content, timestamp);
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Store in history
        this.messageHistory.push({ content, sender, timestamp });

        // Save to localStorage for persistence
        this.saveMessageHistory();
    }
    
    getUserMessageHTML(content, timestamp) {
        return `
            <div class="flex items-start space-x-3 justify-end">
                <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-2xl rounded-tr-md p-3 shadow-sm max-w-xs">
                    <p class="text-sm">${this.escapeHtml(content)}</p>
                </div>
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
            <span class="text-xs text-gray-500 flex justify-end mr-11">${timestamp}</span>
        `;
    }

    getNalaMessageHTML(content, timestamp) {
        // Convert newlines to <br> and format text
        let formattedContent = content.replace(/\n/g, '<br>');

        // Convert **text** to <strong>text</strong> for bold formatting
        formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Convert *text* to <em>text</em> for italic formatting (but not if it's part of **)
        // Use negative lookbehind and lookahead to avoid matching * that are part of **
        formattedContent = formattedContent.replace(/(?<!\*)\*(?!\*)([^*]+?)\*(?!\*)/g, '<em>$1</em>');

        // Convert bullet points to proper HTML
        formattedContent = formattedContent.replace(/• /g, '• ');

        return `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                    <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                </div>
                <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                    <p class="text-gray-800 text-sm">${formattedContent}</p>
                </div>
            </div>
            <span class="text-xs text-gray-500 ml-11">${timestamp}</span>
        `;
    }
    
    showTypingIndicator() {
        const chatMessages = document.getElementById('nala-messages');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'nala-typing-indicator';
        typingDiv.className = 'mb-4';
        typingDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                    <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                </div>
                <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                    <div class="typing-indicator flex space-x-1">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('nala-typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    showNotification(count = 1) {
        const notification = document.getElementById('nala-notification');
        const notificationCount = document.getElementById('notification-count');

        if (notification && notificationCount) {
            notificationCount.textContent = count;
            notification.classList.remove('hidden');
            notification.classList.add('flex');
        }
    }

    hideNotification() {
        const notification = document.getElementById('nala-notification');
        if (notification) {
            notification.classList.add('hidden');
            notification.classList.remove('flex');
        }
    }
    
    loadUserProfile() {
        // TODO: Load user profile from backend
        // This will be used for personalized recommendations
        this.userProfile = {
            id: null,
            skills: [],
            interests: [],
            career_goal: null,
            experience_level: 'beginner'
        };
    }
    
    initializeWelcomeMessage() {
        const welcomeContainer = document.getElementById('nala-welcome-message');
        if (!welcomeContainer) return;

        let welcomeMessage = '';

        if (this.isAuthenticated && this.userProfile) {
            // Personalized welcome for authenticated users
            const userName = this.userProfile.name || 'Pengguna';
            welcomeMessage = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                        <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                    </div>
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                        <p class="text-gray-800 text-sm">Halo ${userName}! Saya Nala, asisten AI Ngambiskuy. Saya siap membantu Anda dengan:</p>
                        <ul class="mt-2 text-xs text-gray-600 space-y-1">
                            <li>• Rekomendasi kursus yang tepat</li>
                            <li>• Analisis jalur karir</li>
                            <li>• Bantuan pembelajaran</li>
                            <li>• Pertanyaan tentang ${this.getContextualHelp()}</li>
                        </ul>
                    </div>
                </div>
                <span class="text-xs text-gray-500 ml-11">Baru saja</span>
            `;
        } else {
            // General welcome for non-authenticated users
            welcomeMessage = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                        <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                    </div>
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                        <p class="text-gray-800 text-sm">Halo! Saya Nala, Ngambiskuy Advance Learning Assistance. Saya dapat membantu Anda dengan informasi umum tentang platform kami.</p>
                        <p class="text-xs text-gray-600 mt-2">Login untuk pengalaman yang lebih personal!</p>
                    </div>
                </div>
                <span class="text-xs text-gray-500 ml-11">Baru saja</span>
            `;
        }

        welcomeContainer.innerHTML = welcomeMessage;
    }

    getContextualHelp() {
        const contextHelp = {
            'homepage': 'platform Ngambiskuy',
            'user_dashboard': 'dashboard dan progress Anda',
            'tutor_dashboard': 'manajemen kursus',
            'course_detail': 'kursus ini',
            'learning_page': 'materi pembelajaran',
            'exam_taking': 'ujian dan tips',
            'course_creation': 'pembuatan kursus'
        };

        return contextHelp[this.currentContext] || 'platform Ngambiskuy';
    }

    getConversationContext() {
        // Return last 5 messages for context with route information
        const context = this.messageHistory.slice(-5);
        return {
            messages: context,
            route: this.currentRoute,
            context: this.currentContext,
            user: this.userProfile
        };
    }

    saveMessageHistory() {
        try {
            localStorage.setItem('nala_chat_history', JSON.stringify(this.messageHistory));
        } catch (error) {
            console.warn('Could not save Nala chat history to localStorage:', error);
        }
    }

    loadMessageHistory() {
        // Deprecated: Now using database storage
        // Keep for backward compatibility with localStorage
        try {
            const saved = localStorage.getItem('nala_chat_history');
            if (saved) {
                this.messageHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('Could not load Nala chat history from localStorage:', error);
        }
    }

    /**
     * Load chat history from database
     */
    async loadChatHistory() {
        if (!this.isAuthenticated) {
            return; // Only load for authenticated users
        }

        try {
            const response = await fetch('/api/nala-chat/history', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.conversations.data.length > 0) {
                    // Get the most recent conversation
                    const latestConversation = data.conversations.data[0];
                    this.currentConversationId = latestConversation.id;

                    // Load messages for the latest conversation
                    await this.loadConversationMessages(latestConversation.id);
                }
            }
        } catch (error) {
            console.warn('Could not load chat history from database:', error);
        }
    }

    /**
     * Load messages for a specific conversation
     */
    async loadConversationMessages(conversationId) {
        try {
            const response = await fetch(`/api/nala-chat/conversation/${conversationId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.conversation.messages) {
                    this.restoreMessagesToUI(data.conversation.messages);
                }
            }
        } catch (error) {
            console.warn('Could not load conversation messages:', error);
        }
    }

    /**
     * Restore messages to UI
     */
    restoreMessagesToUI(messages) {
        const chatMessages = document.getElementById('nala-messages');
        if (!chatMessages) return;

        // Clear existing messages except welcome message
        const welcomeMessage = document.getElementById('nala-welcome-message');
        chatMessages.innerHTML = '';
        if (welcomeMessage) {
            chatMessages.appendChild(welcomeMessage);
        }

        // Clear messageHistory to avoid duplicates
        this.messageHistory = [];

        // Sort messages by created_at to ensure proper chronological order
        const sortedMessages = messages.sort((a, b) => {
            return new Date(a.created_at) - new Date(b.created_at);
        });

        // Add messages to UI in chronological order
        sortedMessages.forEach(message => {
            // Use formatted_timestamp if available, otherwise format created_at
            let displayTimestamp = message.formatted_timestamp;
            if (!displayTimestamp && message.created_at) {
                // Format the timestamp to H:i format
                const date = new Date(message.created_at);
                displayTimestamp = date.toLocaleTimeString('id-ID', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'Asia/Jakarta'
                });
            }

            this.addMessageToUI(message.content, message.sender, displayTimestamp);
            // Also add to messageHistory for context
            this.messageHistory.push({
                content: message.content,
                sender: message.sender,
                timestamp: displayTimestamp
            });
        });
    }

    /**
     * Add message to UI (separate from addMessage to avoid saving to history again)
     */
    addMessageToUI(content, sender, timestamp) {
        const chatMessages = document.getElementById('nala-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-4 message-animation';

        if (sender === 'user') {
            messageDiv.innerHTML = this.getUserMessageHTML(content, timestamp);
        } else {
            messageDiv.innerHTML = this.getNalaMessageHTML(content, timestamp);
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Trigger animation
        setTimeout(() => {
            messageDiv.classList.add('show');
        }, 10);
    }

    /**
     * Clear chat history
     */
    async clearChatHistory() {
        if (!this.isAuthenticated) {
            // For non-authenticated users, just clear localStorage
            localStorage.removeItem('nala_chat_history');
            this.messageHistory = [];
            this.clearChatUI();
            return;
        }

        try {
            const response = await fetch('/api/nala-chat/clear-history', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.currentConversationId = null;
                    this.messageHistory = [];
                    this.clearChatUI();

                    // Show success message
                    this.addMessage('Chat history berhasil dihapus. Mulai percakapan baru!', 'ai');
                }
            }
        } catch (error) {
            console.error('Could not clear chat history:', error);
            this.addMessage('Maaf, gagal menghapus riwayat chat. Silakan coba lagi.', 'ai');
        }
    }

    /**
     * Clear chat UI
     */
    clearChatUI() {
        const chatMessages = document.getElementById('nala-messages');
        if (!chatMessages) return;

        // Keep only the welcome message
        const welcomeMessage = document.getElementById('nala-welcome-message');
        chatMessages.innerHTML = '';
        if (welcomeMessage) {
            chatMessages.appendChild(welcomeMessage);
        }
    }

    /**
     * Handle membership limit responses
     */
    handleMembershipLimit(data) {
        // Update quick actions to show upgrade suggestions
        if (data.suggestions) {
            this.updateQuickActionsWithUpgrade(data.suggestions);
        }

        // Show upgrade notification if needed
        if (data.limit_reached) {
            this.showUpgradeNotification(data.membership_level);
        }

        // Update status to show limit reached
        const statusElement = document.getElementById('nala-status');
        if (statusElement && data.limit_reached) {
            statusElement.textContent = 'Batas harian tercapai - Upgrade untuk lebih banyak chat';
        }
    }

    /**
     * Update quick actions with upgrade suggestions
     */
    updateQuickActionsWithUpgrade(suggestions) {
        const quickActionsContainer = document.getElementById('nala-quick-actions');
        if (!quickActionsContainer) return;

        // Clear existing actions
        quickActionsContainer.innerHTML = '';

        // Add upgrade suggestions
        suggestions.forEach(suggestion => {
            const button = document.createElement('button');
            button.className = 'nala-quick-action-btn px-3 py-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full text-xs hover:from-purple-700 hover:to-pink-700 transition-colors';
            button.textContent = suggestion;
            button.addEventListener('click', () => this.handleUpgradeAction(suggestion));
            quickActionsContainer.appendChild(button);
        });
    }

    /**
     * Handle upgrade action clicks
     */
    handleUpgradeAction(action) {
        // Redirect to pricing page or show upgrade modal
        // Adjust these URLs based on your routing
        const upgradeUrls = {
            'Lihat paket Basic': '/pricing#basic',
            'Upgrade ke Standard': '/pricing#standard',
            'Upgrade ke Pro': '/pricing#pro',
            'Bandingkan membership': '/pricing',
            'Coba gratis 7 hari': '/pricing#trial',
            'Lihat fitur Premium': '/pricing#features',
            'Fitur unlimited': '/pricing#pro',
            'Analisis mendalam': '/pricing#pro'
        };

        const url = upgradeUrls[action] || '/pricing';
        window.open(url, '_blank');
    }

    /**
     * Show upgrade notification
     */
    showUpgradeNotification(membershipLevel) {
        // Show a subtle notification badge
        const notification = document.getElementById('nala-notification');
        if (notification) {
            notification.classList.remove('hidden');
            notification.classList.add('flex');

            // Change notification color to indicate upgrade needed
            notification.classList.add('bg-gradient-to-r', 'from-yellow-500', 'to-orange-500');
            notification.classList.remove('bg-red-500');

            const notificationCount = document.getElementById('notification-count');
            if (notificationCount) {
                notificationCount.textContent = '!';
            }
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Auto-update user profile from AI conversation
     */
    async updateUserProfile(field, value) {
        if (!this.isAuthenticated || !this.currentConversationId) {
            return false;
        }

        try {
            const response = await fetch('/api/nala-chat/update-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                },
                body: JSON.stringify({
                    field: field,
                    value: value,
                    conversation_id: this.currentConversationId
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    console.log(`Profile updated: ${field} = ${value}`);
                    return true;
                }
            }
        } catch (error) {
            console.warn('Could not update user profile:', error);
        }

        return false;
    }

    /**
     * Parse AI response for profile data extraction
     */
    parseProfileDataFromResponse(aiResponse, userMessage) {
        // Look for patterns in AI responses that indicate profile data collection
        const profilePatterns = {
            job_title: /pekerjaan.*?(?:sebagai|adalah|di)\s*([^.!?]+)/i,
            company: /(?:bekerja di|kerja di|di perusahaan)\s*([^.!?]+)/i,
            experience_years: /(?:pengalaman|berpengalaman)\s*(\d+)\s*tahun/i,
            skills: /(?:skill|keahlian|kemampuan).*?(?:adalah|yaitu|meliputi)\s*([^.!?]+)/i,
            learning_interests: /(?:ingin belajar|minat belajar|tertarik dengan)\s*([^.!?]+)/i,
            career_goals: /(?:tujuan karir|ingin menjadi|cita-cita)\s*([^.!?]+)/i,
            location: /(?:tinggal di|domisili|lokasi)\s*([^.!?]+)/i,
            education_level: /(?:pendidikan|lulusan)\s*(sma|diploma|s1|s2|s3)/i
        };

        // Check user message for profile information
        for (const [field, pattern] of Object.entries(profilePatterns)) {
            const match = userMessage.match(pattern);
            if (match && match[1]) {
                let value = match[1].trim();

                // Clean up the extracted value
                value = value.replace(/[,.]$/, ''); // Remove trailing punctuation

                // Special handling for different field types
                if (field === 'experience_years') {
                    value = parseInt(value);
                } else if (field === 'skills' || field === 'learning_interests') {
                    value = value.split(/[,&dan]+/).map(s => s.trim()).filter(s => s.length > 0);
                }

                // Auto-update profile
                this.updateUserProfile(field, value);
            }
        }
    }
}

// Initialize Nala AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('nala-chat-container')) {
        window.nalaAIAssistant = new NalaAIAssistant();
        console.log('Nala AI Assistant initialized successfully!');
    }
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NalaAIAssistant;
}
