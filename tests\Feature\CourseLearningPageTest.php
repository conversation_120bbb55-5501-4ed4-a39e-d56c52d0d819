<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use App\Models\CourseEnrollment;
use App\Models\LessonProgress;
use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseLearningPageTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tutor;
    protected $course;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create();
        $this->tutor = User::factory()->create(['role' => 'tutor']);
        $this->user = User::factory()->create(['role' => 'student']);
        
        $this->course = Course::factory()->create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $this->category->id,
            'status' => 'published',
            'published_at' => now(),
            'is_free' => true,
            'target_audience' => ['Web Developer', 'Frontend Developer', 'Fullstack Developer'],
            'learning_outcomes' => ['Learn HTML', 'Learn CSS', 'Build websites'],
            'requirements' => ['Basic computer skills', 'Internet access'],
            'tags' => ['html', 'css', 'web-development']
        ]);

        // Create chapters and lessons
        $chapter = CourseChapter::factory()->create([
            'course_id' => $this->course->id,
            'is_published' => true
        ]);

        CourseLesson::factory()->count(3)->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter->id,
            'is_published' => true
        ]);
    }

    /** @test */
    public function user_can_access_free_course_learning_page()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertViewIs('course.learn.index');
        $response->assertViewHas(['course', 'userProgress', 'progressPercentage', 'enrollment']);
    }

    /** @test */
    public function course_learning_page_handles_array_target_audience()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Web Developer, Frontend Developer, Fullstack Developer');
        $response->assertSee('Junior Web Developer');
    }

    /** @test */
    public function course_learning_page_handles_null_target_audience()
    {
        $this->course->update(['target_audience' => null]);
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Tech Professional');
        $response->assertSee('Junior Developer');
    }

    /** @test */
    public function course_learning_page_shows_progress_correctly()
    {
        $this->actingAs($this->user);
        
        // Create enrollment
        CourseEnrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $this->course->id,
            'status' => 'active',
            'enrolled_at' => now()
        ]);

        // Complete one lesson
        $lesson = $this->course->chapters->first()->lessons->first();
        LessonProgress::create([
            'user_id' => $this->user->id,
            'lesson_id' => $lesson->id,
            'status' => 'completed',
            'progress_percentage' => 100,
            'completed_at' => now()
        ]);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('33%'); // 1 out of 3 lessons completed
    }

    /** @test */
    public function course_learning_page_shows_next_lesson()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Lanjutkan Belajar');
        
        $nextLesson = $this->course->chapters->first()->lessons->first();
        $response->assertSee($nextLesson->title);
    }

    /** @test */
    public function course_learning_page_shows_completion_when_all_lessons_done()
    {
        $this->actingAs($this->user);
        
        // Complete all lessons
        foreach ($this->course->chapters as $chapter) {
            foreach ($chapter->lessons as $lesson) {
                LessonProgress::create([
                    'user_id' => $this->user->id,
                    'lesson_id' => $lesson->id,
                    'status' => 'completed',
                    'progress_percentage' => 100,
                    'completed_at' => now()
                ]);
            }
        }

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Selamat! Kursus Selesai');
        $response->assertSee('100%');
    }

    /** @test */
    public function unauthorized_user_cannot_access_paid_course()
    {
        $paidCourse = Course::factory()->create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $this->category->id,
            'status' => 'published',
            'published_at' => now(),
            'is_free' => false,
            'price' => 100000
        ]);

        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $paidCourse));

        $response->assertRedirect(route('course.show', $paidCourse));
        $response->assertSessionHas('error');
    }

    /** @test */
    public function course_learning_page_shows_ai_insights()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('AI Learning Assistant');
        $response->assertSee('Powered by Ngambiskuy ICE');
        $response->assertSee('AI Learning Insights');
        $response->assertSee('Kekuatan Anda');
        $response->assertSee('Area Pengembangan');
        $response->assertSee('Prediksi Karir');
    }

    /** @test */
    public function course_learning_page_shows_community_features()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Learning Community');
        $response->assertSee('Forum Diskusi');
        $response->assertSee('Study Group');
        $response->assertSee('Live Session');
    }

    /** @test */
    public function course_learning_page_shows_quick_actions()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Quick Actions');
        $response->assertSee('Download Materi');
        $response->assertSee('Catatan Pribadi');
        $response->assertSee('Practice Quiz');
        $response->assertSee('Bantuan AI');
    }

    /** @test */
    public function course_learning_page_shows_instructor_info()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Tentang Instruktur');
        $response->assertSee($this->tutor->name);
        $response->assertSee('Kirim Pesan');
    }

    /** @test */
    public function course_learning_page_handles_missing_enrollment_gracefully()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        // Should still work for free courses even without explicit enrollment
    }

    /** @test */
    public function course_learning_page_shows_correct_statistics()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('course.learn', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Progress');
        $response->assertSee('Selesai');
        $response->assertSee('Waktu');
        $response->assertSee('Badge');
        $response->assertSee('Achievement');
    }
}
