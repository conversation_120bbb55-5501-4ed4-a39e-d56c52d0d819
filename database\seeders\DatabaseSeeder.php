<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CategorySeeder::class,
            UserSeeder::class,
            TutorProfileSeeder::class,
            MembershipPlanSeeder::class,
            CourseSeeder::class,
            CourseChapterSeeder::class,
            CourseLessonSeeder::class,
            CourseEnrollmentSeeder::class,
            QuizSeeder::class,
            AssignmentSeeder::class,
            ExamSeeder::class,
            BlogPostSeeder::class,
            SavedArticleSeeder::class,
            CertificateTestSeeder::class,
            ExamCertificateTestSeeder::class,
            // Note: Quiz attempts, answers, assignment submissions, and lesson progress
            // will be created when students interact with the platform
        ]);
    }
}
