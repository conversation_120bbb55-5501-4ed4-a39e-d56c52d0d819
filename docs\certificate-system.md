# Certificate System Documentation

## Overview
The Ngambiskuy platform now includes a professional certificate generation system that allows users to download certificates for completed courses. This system ensures that only users who have completed 100% of a course can access their certificates.

## Features

### 1. Automatic Progress Tracking
- Real-time progress calculation based on completed lessons
- Progress is calculated from `LessonProgress` model where `status = 'completed'`
- Only courses with 100% completion are eligible for certificates

### 2. Professional Certificate Design
- Landscape A4 format for professional appearance
- Ngambiskuy branding with official logo
- Clean, modern design with proper typography
- Includes all necessary information:
  - Student name
  - Course title
  - Instructor name
  - Completion date
  - Certificate ID
  - Total lessons completed
  - Course level

### 3. Security Features
- User authentication required
- Enrollment verification
- Progress verification (100% completion required)
- Unique certificate IDs for verification

## Implementation Details

### Controllers
- `CertificateController`: Handles certificate generation and validation
- Methods:
  - `downloadCourseCertificate()`: Generates and downloads PDF certificate
  - `previewCourseCertificate()`: Shows certificate preview in browser

### Routes
```php
// Certificate routes (protected by auth)
Route::get('/courses/{course}/certificate/download', [CertificateController::class, 'downloadCourseCertificate'])->name('course.certificate.download');
Route::get('/courses/{course}/certificate/preview', [CertificateController::class, 'previewCourseCertificate'])->name('course.certificate.preview');
```

### Views
- `resources/views/certificates/course-certificate.blade.php`: Certificate template

### Dependencies
- `barryvdh/laravel-dompdf`: PDF generation library

## User Interface Updates

### User Courses Page
- Fixed progress calculation to show real data from database
- Separated courses into three tabs:
  - **Kursus Diikuti**: In-progress courses (< 100% completion)
  - **Kursus Tersedia**: Available courses for enrollment
  - **Selesai**: Completed courses (100% completion) with certificate download

### Completed Courses Features
- Download certificate button (primary action)
- Preview certificate button (opens in new tab)
- Review course button (access course content)
- Visual indicators showing course completion status

## Certificate ID Format
Certificate IDs follow the format: `NGMB-{COURSE_ID_PREFIX}-{USER_ID_PREFIX}-{YEAR}`

Example: `NGMB-A1B2-C3D4-2024`

## Validation Rules
1. User must be authenticated
2. User must be enrolled in the course
3. Course must have lessons
4. User must have completed 100% of lessons
5. All lesson progress must have `status = 'completed'`

## Error Handling
- 404 error if user not enrolled
- 404 error if course has no lessons
- Redirect with error message if course not completed
- Proper error messages for all validation failures

## File Naming Convention
Downloaded certificates use the format:
`Sertifikat-{Course-Title}-{User-Name}.pdf`

Special characters and spaces are replaced with hyphens for file system compatibility.

## Future Enhancements
- Certificate verification system via QR codes
- Email delivery of certificates
- Certificate templates for different course types
- Batch certificate generation for administrators
- Certificate revocation system
