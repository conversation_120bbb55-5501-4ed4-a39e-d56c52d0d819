<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ExamQuestion extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'exam_id',
        'question',
        'type',
        'points',
        'sort_order',
        'explanation',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'points' => 'integer',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the exam that owns the question.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the options for the question.
     */
    public function options(): HasMany
    {
        return $this->hasMany(ExamQuestionOption::class, 'question_id')->orderBy('sort_order');
    }

    /**
     * Get the correct option for the question.
     */
    public function correctOption(): BelongsTo
    {
        return $this->belongsTo(ExamQuestionOption::class, 'correct_option_id');
    }

    /**
     * Get the answers for this question.
     */
    public function answers(): HasMany
    {
        return $this->hasMany(ExamAnswer::class, 'question_id');
    }

    /**
     * Check if the question is multiple choice.
     */
    public function isMultipleChoice()
    {
        return $this->type === 'multiple_choice';
    }

    /**
     * Check if the question is true/false.
     */
    public function isTrueFalse()
    {
        return $this->type === 'true_false';
    }

    /**
     * Check if the question is short answer.
     */
    public function isShortAnswer()
    {
        return $this->type === 'short_answer';
    }

    /**
     * Get the type label.
     */
    public function getTypeLabelAttribute()
    {
        return match($this->type) {
            'multiple_choice' => 'Pilihan Ganda',
            'true_false' => 'Benar/Salah',
            'short_answer' => 'Jawaban Singkat',
            default => $this->type
        };
    }
}
