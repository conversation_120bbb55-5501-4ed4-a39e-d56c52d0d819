<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_enrollments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('exam_id'); // Foreign key to exams table
            $table->uuid('user_id'); // Foreign key to users table

            // Enrollment Information
            $table->decimal('amount_paid', 10, 2)->default(0); // Amount paid for the exam
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            $table->string('payment_method')->nullable(); // Payment method used
            $table->string('payment_reference')->nullable(); // Payment reference/transaction ID

            // Access Control
            $table->timestamp('enrolled_at')->useCurrent(); // When user enrolled
            $table->timestamp('expires_at')->nullable(); // When access expires (if applicable)
            $table->boolean('is_active')->default(true); // Whether enrollment is active

            // Completion Tracking
            $table->integer('attempts_used')->default(0); // Number of attempts used
            $table->decimal('best_score', 5, 2)->nullable(); // Best score achieved
            $table->boolean('has_passed')->default(false); // Whether user has passed
            $table->timestamp('first_attempt_at')->nullable(); // When first attempt was made
            $table->timestamp('last_attempt_at')->nullable(); // When last attempt was made
            $table->timestamp('passed_at')->nullable(); // When user passed the exam

            // Certificate
            $table->boolean('certificate_issued')->default(false); // Whether certificate was issued
            $table->timestamp('certificate_issued_at')->nullable(); // When certificate was issued
            $table->string('certificate_number')->nullable(); // Certificate number

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('exam_id')->references('id')->on('exams')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Unique constraint - one enrollment per user per exam
            $table->unique(['exam_id', 'user_id']);

            // Indexes
            $table->index('exam_id');
            $table->index('user_id');
            $table->index('payment_status');
            $table->index('is_active');
            $table->index('has_passed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_enrollments');
    }
};
