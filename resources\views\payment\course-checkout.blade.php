@extends('layouts.app')

@section('title', 'Checkout Course - Ngambiskuy')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-8 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Checkout Course</h1>
                <p class="text-gray-600 mt-2">Beli sekali, akses selamanya!</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">
                <!-- Course Summary -->
                <div class="order-2 lg:order-1">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Course Details</h2>
                    
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        @if($course->thumbnail)
                        <img src="{{ asset('storage/' . $course->thumbnail) }}" 
                             alt="{{ $course->title }}" 
                             class="w-full h-48 object-cover rounded-lg mb-4">
                        @endif
                        
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $course->title }}</h3>
                        <p class="text-gray-600 mb-4">by {{ $course->tutor->name }}</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L10 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                                {{ $course->duration ?? 'Flexible duration' }}
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $course->level_indonesian }}
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Akses selamanya
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $course->total_lessons }} lessons
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <span class="text-3xl font-bold text-emerald-600">
                                {{ $course->formatted_price }}
                            </span>
                        </div>
                    </div>

                    <!-- Revenue Breakdown -->
                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="font-semibold text-gray-900 mb-3">Revenue Breakdown</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Course Price</span>
                                <span class="font-medium">{{ $course->formatted_price }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Platform Fee (5%)</span>
                                <span class="font-medium">IDR {{ number_format($platformFee, 0, ',', '.') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tutor Earnings (60%)</span>
                                <span class="font-medium">IDR {{ number_format($tutorEarnings, 0, ',', '.') }}</span>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>With referral code: 80%</span>
                                <span>IDR {{ number_format($course->calculateTutorEarnings(true), 0, ',', '.') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="order-1 lg:order-2">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
                    
                    <form action="{{ route('payment.course.process', $course) }}" method="POST" id="payment-form">
                        @csrf

                        <!-- Payment Method -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
                            <div class="space-y-3">
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="bank_transfer" class="mr-3" checked>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Bank Transfer</div>
                                            <div class="text-sm text-gray-600">BCA, Mandiri, BNI, BRI</div>
                                        </div>
                                    </div>
                                </label>

                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="e_wallet" class="mr-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">E-Wallet</div>
                                            <div class="text-sm text-gray-600">GoPay, OVO, DANA, LinkAja</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Referral Code -->
                        <div class="mb-6">
                            <label for="referral_code" class="block text-sm font-medium text-gray-700 mb-2">
                                Referral Code (Optional)
                            </label>
                            <input type="text" 
                                   id="referral_code" 
                                   name="referral_code" 
                                   placeholder="Enter referral code"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500">
                            <p class="text-sm text-gray-600 mt-1">
                                With referral code, tutor gets 80% revenue (without: 60%)
                            </p>
                        </div>

                        <!-- NALA Membership Upsell -->
                        <div class="mb-6 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
                            <h4 class="font-semibold text-emerald-900 mb-2">🤖 Want AI-Powered Learning?</h4>
                            <p class="text-sm text-emerald-800 mb-3">
                                Get NALA Membership for AI teaching assistants, unlimited prompts, and personalized learning paths!
                            </p>
                            <a href="{{ route('payment.pricing') }}" 
                               class="text-sm text-emerald-600 hover:text-emerald-700 font-medium">
                                View NALA Membership Plans →
                            </a>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-6">
                            <label class="flex items-start">
                                <input type="checkbox" name="terms_agreed" required class="mt-1 mr-3">
                                <span class="text-sm text-gray-700">
                                    I agree to the 
                                    <a href="#" class="text-emerald-600 hover:text-emerald-700">Terms and Conditions</a> 
                                    and 
                                    <a href="#" class="text-emerald-600 hover:text-emerald-700">Privacy Policy</a> 
                                    of Ngambiskuy
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" 
                                class="w-full bg-emerald-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200">
                            Buy Course Now
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
