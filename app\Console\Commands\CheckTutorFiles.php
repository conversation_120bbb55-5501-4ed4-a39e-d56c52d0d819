<?php

namespace App\Console\Commands;

use App\Models\TutorProfile;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CheckTutorFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tutor:check-files {--fix : Fix missing file references}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check tutor file storage and fix missing files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking tutor files...');

        $profiles = TutorProfile::whereNotNull('identity_photo_path')
            ->orWhereNotNull('portfolio_path')
            ->orWhereNotNull('npwp_photo_path')
            ->get();

        if ($profiles->isEmpty()) {
            $this->info('No tutor profiles with file references found.');
            return Command::SUCCESS;
        }

        $this->info("Found {$profiles->count()} profiles with file references:");

        $missingFiles = [];

        foreach ($profiles as $profile) {
            $this->line("\nProfile ID: {$profile->id}");
            $userName = $profile->user ? $profile->user->name : 'Unknown';
            $this->line("User: {$userName}");

            // Check identity photo
            if ($profile->identity_photo_path) {
                $exists = Storage::disk('local')->exists($profile->identity_photo_path);
                $this->line("  KTP: {$profile->identity_photo_path} " . ($exists ? '✓' : '✗ MISSING'));
                if (!$exists) {
                    $missingFiles[] = [
                        'profile_id' => $profile->id,
                        'field' => 'identity_photo_path',
                        'path' => $profile->identity_photo_path
                    ];
                }
            }

            // Check portfolio
            if ($profile->portfolio_path) {
                $exists = Storage::disk('local')->exists($profile->portfolio_path);
                $this->line("  Portfolio: {$profile->portfolio_path} " . ($exists ? '✓' : '✗ MISSING'));
                if (!$exists) {
                    $missingFiles[] = [
                        'profile_id' => $profile->id,
                        'field' => 'portfolio_path',
                        'path' => $profile->portfolio_path
                    ];
                }
            }

            // Check NPWP photo
            if ($profile->npwp_photo_path) {
                $exists = Storage::disk('local')->exists($profile->npwp_photo_path);
                $this->line("  NPWP: {$profile->npwp_photo_path} " . ($exists ? '✓' : '✗ MISSING'));
                if (!$exists) {
                    $missingFiles[] = [
                        'profile_id' => $profile->id,
                        'field' => 'npwp_photo_path',
                        'path' => $profile->npwp_photo_path
                    ];
                }
            }
        }

        if (empty($missingFiles)) {
            $this->info("\n✅ All files exist!");
            return Command::SUCCESS;
        }

        $this->warn("\n⚠ Found " . count($missingFiles) . " missing files:");
        foreach ($missingFiles as $missing) {
            $this->line("  Profile {$missing['profile_id']}: {$missing['field']} -> {$missing['path']}");
        }

        if ($this->option('fix')) {
            $this->info("\nFixing missing file references...");
            
            foreach ($missingFiles as $missing) {
                $profile = TutorProfile::find($missing['profile_id']);
                if ($profile) {
                    $profile->update([$missing['field'] => null]);
                    $this->line("  ✓ Cleared {$missing['field']} for profile {$missing['profile_id']}");
                }
            }
            
            $this->info("✅ Fixed missing file references!");
        } else {
            $this->info("\nRun with --fix to clear missing file references from database.");
        }

        return Command::SUCCESS;
    }
}
