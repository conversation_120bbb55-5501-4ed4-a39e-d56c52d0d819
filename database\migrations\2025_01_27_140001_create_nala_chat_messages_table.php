<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nala_chat_messages', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('conversation_id'); // Foreign key to nala_chat_conversations table
            
            // Message content
            $table->enum('sender', ['user', 'ai']); // Who sent the message
            $table->text('content'); // Message content
            $table->text('formatted_content')->nullable(); // HTML formatted content for AI messages
            
            // Message metadata
            $table->json('metadata')->nullable(); // Additional data (context, suggestions, etc.)
            $table->boolean('is_prohibited')->default(false); // If message triggered prohibition
            $table->boolean('is_limit_reached')->default(false); // If message hit membership limit
            $table->string('membership_level')->nullable(); // User's membership when message was sent
            
            // Processing information
            $table->integer('processing_time_ms')->nullable(); // Time taken to process AI response
            $table->string('ai_model')->nullable(); // AI model used (e.g., 'gemini-2.0-flash')
            $table->integer('tokens_used')->nullable(); // Tokens consumed for this message
            
            // Status
            $table->enum('status', ['sent', 'delivered', 'failed', 'deleted'])->default('sent');
            $table->text('error_message')->nullable(); // Error message if failed
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('conversation_id')->references('id')->on('nala_chat_conversations')->onDelete('cascade');
            
            // Indexes
            $table->index(['conversation_id', 'created_at']);
            $table->index(['sender', 'created_at']);
            $table->index('status');
            $table->index('is_prohibited');
            $table->index('membership_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nala_chat_messages');
    }
};
