<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Exam;

class CareerController extends Controller
{
    /**
     * Handle career-related questions
     */
    public function handleCareerQuestion($message, $context, $userProfile, $membership)
    {
        $lowerMessage = strtolower($message);
        
        // Determine the type of career question
        if ($this->isCareerPathQuery($lowerMessage)) {
            return $this->generateCareerPathAnalysis($userProfile, $membership);
        }
        
        if ($this->isSalaryQuery($lowerMessage)) {
            return $this->provideSalaryInsights($message, $userProfile);
        }
        
        if ($this->isSkillGapQuery($lowerMessage)) {
            return $this->analyzeSkillGaps($userProfile, $membership);
        }
        
        if ($this->isIndustryQuery($lowerMessage)) {
            return $this->provideIndustryInsights($message, $userProfile);
        }
        
        if ($this->isJobMarketQuery($lowerMessage)) {
            return $this->provideJobMarketAnalysis($userProfile);
        }
        
        // Default career response
        return $this->getGeneralCareerResponse($userProfile);
    }

    /**
     * Generate career path analysis
     */
    private function generateCareerPathAnalysis($userProfile, $membership)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        $currentJob = $userProfile['professional_info']['job_title'] ?? 'posisi saat ini';
        $experience = $userProfile['professional_info']['experience_years'] ?? 0;
        $skills = $userProfile['professional_info']['skills'] ?? [];
        $careerGoals = $userProfile['career_aspirations']['career_goals'] ?? [];

        $response = "Halo {$name}! Mari kita lihat jalur karir Anda:\n\n";

        // Current position analysis
        $response .= "📊 **Posisi Saat Ini:**\n";
        $response .= "• Jabatan: {$currentJob}\n";
        $response .= "• Pengalaman: {$experience} tahun\n";

        if (!empty($skills)) {
            $topSkills = implode(', ', array_slice($skills, 0, 3));
            $response .= "• Skills utama: {$topSkills}\n";
        }
        $response .= "\n";
        
        // Career progression recommendations
        $response .= "🎯 **Rekomendasi Jalur Karir:**\n";
        
        if ($experience < 2) {
            $response .= "**Fase: Early Career (0-2 tahun)**\n";
            $response .= "• Fokus: Membangun fondasi skill teknis\n";
            $response .= "• Target: Junior → Mid-level dalam 2-3 tahun\n";
            $response .= "• Prioritas: Sertifikasi dan project portfolio\n\n";
        } elseif ($experience < 5) {
            $response .= "**Fase: Mid-level (2-5 tahun)**\n";
            $response .= "• Fokus: Spesialisasi dan leadership skills\n";
            $response .= "• Target: Senior role atau team lead\n";
            $response .= "• Prioritas: Advanced certifications, mentoring\n\n";
        } else {
            $response .= "**Fase: Senior (5+ tahun)**\n";
            $response .= "• Fokus: Strategic thinking dan management\n";
            $response .= "• Target: Principal, Manager, atau C-level\n";
            $response .= "• Prioritas: Business skills, industry expertise\n\n";
        }
        
        // Specific recommendations based on goals
        if (!empty($careerGoals)) {
            $response .= "🚀 **Berdasarkan Tujuan Anda:**\n";
            foreach (array_slice($careerGoals, 0, 2) as $goal) {
                $recommendations = $this->getCareerSpecificRecommendations($goal, $experience);
                $response .= "• **{$goal}**: {$recommendations}\n";
            }
            $response .= "\n";
        }
        
        // Skill development recommendations
        $skillRecommendations = $this->getSkillDevelopmentRecommendations($userProfile);
        if (!empty($skillRecommendations)) {
            $response .= "📚 **Skills yang Perlu Dikembangkan:**\n";
            foreach ($skillRecommendations as $skill => $reason) {
                $response .= "• **{$skill}**: {$reason}\n";
            }
            $response .= "\n";
        }
        
        $response .= "💡 Mau analisis lebih detail untuk industri atau posisi tertentu? Tanya saja!";
        
        return $response;
    }

    /**
     * Provide salary insights
     */
    private function provideSalaryInsights($message, $userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        $experience = $userProfile['professional_info']['experience_years'] ?? 0;
        $location = $userProfile['basic_info']['location'] ?? 'Indonesia';
        
        // Extract job title or industry from message
        $jobTitle = $this->extractJobTitleFromMessage($message);
        if (!$jobTitle) {
            $jobTitle = $userProfile['professional_info']['job_title'] ?? 'posisi teknologi';
        }
        
        $response = "Insight Gaji untuk {$name}:\n\n";
        
        $response .= "💰 **Estimasi Gaji {$jobTitle}** (di {$location}):\n\n";
        
        // Provide salary ranges based on experience
        $salaryRanges = $this->getSalaryRanges($jobTitle, $experience);
        
        foreach ($salaryRanges as $level => $range) {
            $response .= "**{$level}**: {$range}\n";
        }
        
        $response .= "\n📈 **Faktor yang Mempengaruhi Gaji:**\n";
        $response .= "• Lokasi (Jakarta 20-30% lebih tinggi)\n";
        $response .= "• Ukuran perusahaan (Startup vs Corporate)\n";
        $response .= "• Sertifikasi dan skill spesialis\n";
        $response .= "• Kemampuan bahasa Inggris\n";
        $response .= "• Portfolio dan track record\n\n";
        
        $response .= "🎯 **Tips Negosiasi Gaji:**\n";
        $response .= "• Research market rate sebelum interview\n";
        $response .= "• Highlight unique value proposition\n";
        $response .= "• Pertimbangkan total compensation package\n";
        $response .= "• Timing yang tepat untuk meminta kenaikan\n\n";
        
        // Personalized advice based on experience
        if ($experience < 2) {
            $response .= "💡 **Untuk early career**: Fokus pada skill building dulu, gaji akan mengikuti seiring pengalaman.\n\n";
        } else {
            $response .= "💡 **Dengan pengalaman Anda**: Saatnya untuk negotiate dan aim for higher compensation.\n\n";
        }
        
        $response .= "Mau tips spesifik untuk industri atau posisi tertentu?";
        
        return $response;
    }

    /**
     * Analyze skill gaps
     */
    private function analyzeSkillGaps($userProfile, $membership)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        $currentSkills = $userProfile['professional_info']['skills'] ?? [];
        $careerGoals = $userProfile['career_aspirations']['career_goals'] ?? [];
        $experience = $userProfile['professional_info']['experience_years'] ?? 0;
        
        $response = "Analisis Skill Gap untuk {$name}:\n\n";
        
        if (!empty($currentSkills)) {
            $response .= "✅ **Skills yang Sudah Dimiliki:**\n";
            foreach (array_slice($currentSkills, 0, 5) as $skill) {
                $response .= "• {$skill}\n";
            }
            $response .= "\n";
        }
        
        // Identify missing skills based on career goals
        $missingSkills = $this->identifyMissingSkills($currentSkills, $careerGoals, $experience);
        
        if (!empty($missingSkills)) {
            $response .= "🎯 **Skills yang Perlu Dikembangkan:**\n\n";
            
            foreach ($missingSkills as $category => $skills) {
                $response .= "**{$category}:**\n";
                foreach ($skills as $skill => $importance) {
                    $priority = $importance === 'high' ? '🔥 HIGH' : ($importance === 'medium' ? '⚡ MEDIUM' : '💡 LOW');
                    $response .= "• {$skill} ({$priority})\n";
                }
                $response .= "\n";
            }
        }
        
        // Recommend courses and certifications
        $recommendations = $this->getSkillDevelopmentRecommendations($userProfile);
        if (!empty($recommendations)) {
            $response .= "📚 **Rekomendasi Pembelajaran:**\n";
            foreach (array_slice($recommendations, 0, 3) as $skill => $reason) {
                $response .= "• **{$skill}**: {$reason}\n";
                
                // Find relevant courses
                $courses = $this->findRelevantCourses($skill);
                if (!empty($courses)) {
                    $course = $courses[0];
                    $price = $course['is_free'] ? 'GRATIS' : 'Rp ' . number_format($course['price'], 0, ',', '.');
                    $response .= "  📖 Kursus: {$course['title']} ({$price})\n";
                }
            }
            $response .= "\n";
        }
        
        $response .= "💡 Mau roadmap detail untuk mengembangkan skill tertentu? Sebutkan skill yang ingin difokuskan!";
        
        return $response;
    }

    /**
     * Provide industry insights
     */
    private function provideIndustryInsights($message, $userProfile)
    {
        $industry = $this->extractIndustryFromMessage($message);
        if (!$industry) {
            $industry = $userProfile['professional_info']['industry'] ?? 'teknologi';
        }
        
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        
        $response = "Insight Industri {$industry} untuk {$name}:\n\n";
        
        $insights = $this->getIndustryInsights($industry);
        
        $response .= "📊 **Tren Industri {$industry}:**\n";
        foreach ($insights['trends'] as $trend) {
            $response .= "• {$trend}\n";
        }
        $response .= "\n";
        
        $response .= "🔥 **Skills yang Sedang Hot:**\n";
        foreach ($insights['hot_skills'] as $skill) {
            $response .= "• {$skill}\n";
        }
        $response .= "\n";
        
        $response .= "💼 **Peluang Karir:**\n";
        foreach ($insights['opportunities'] as $opportunity) {
            $response .= "• {$opportunity}\n";
        }
        $response .= "\n";
        
        $response .= "⚠️ **Tantangan:**\n";
        foreach ($insights['challenges'] as $challenge) {
            $response .= "• {$challenge}\n";
        }
        $response .= "\n";
        
        $response .= "💡 Mau analisis lebih spesifik untuk posisi tertentu di industri ini?";
        
        return $response;
    }

    /**
     * Provide job market analysis
     */
    private function provideJobMarketAnalysis($userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        $location = $userProfile['basic_info']['location'] ?? 'Indonesia';
        
        $response = "Analisis Pasar Kerja untuk {$name} di {$location}:\n\n";
        
        $response .= "📈 **Kondisi Pasar Kerja Teknologi:**\n";
        $response .= "• Demand tinggi untuk developer dan data scientist\n";
        $response .= "• Remote work semakin diterima (70% perusahaan)\n";
        $response .= "• Startup dan tech company berkembang pesat\n";
        $response .= "• Kompetisi ketat untuk posisi senior\n\n";
        
        $response .= "🎯 **Posisi dengan Demand Tinggi:**\n";
        $response .= "• Full Stack Developer\n";
        $response .= "• Data Scientist/Analyst\n";
        $response .= "• DevOps Engineer\n";
        $response .= "• UI/UX Designer\n";
        $response .= "• Digital Marketing Specialist\n\n";
        
        $response .= "💰 **Salary Trends:**\n";
        $response .= "• Tech roles: 15-25% increase YoY\n";
        $response .= "• Remote positions: 10-20% premium\n";
        $response .= "• Certified professionals: 20-30% higher\n";
        $response .= "• Bilingual candidates: 15% advantage\n\n";
        
        $response .= "🚀 **Tips Sukses di Job Market:**\n";
        $response .= "• Build strong online presence (LinkedIn, GitHub)\n";
        $response .= "• Continuous learning dan upskilling\n";
        $response .= "• Network dengan professionals di industri\n";
        $response .= "• Showcase projects dan achievements\n\n";
        
        $response .= "Mau strategi spesifik untuk posisi atau industri tertentu?";
        
        return $response;
    }

    /**
     * Get general career response
     */
    private function getGeneralCareerResponse($userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';
        
        return "Halo {$name}! Saya siap membantu dengan pertanyaan karir Anda. Saya bisa memberikan:

🎯 Analisis jalur karir berdasarkan profil
💰 Insight gaji dan negosiasi
📊 Analisis skill gap dan rekomendasi pembelajaran
🏢 Tren industri dan peluang kerja
📈 Strategi pengembangan karir

Apa yang ingin Anda ketahui tentang karir Anda?";
    }

    /**
     * Check if message is asking about career path
     */
    private function isCareerPathQuery($message)
    {
        $keywords = ['jalur karir', 'career path', 'roadmap karir', 'pengembangan karir', 'next step'];
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if message is asking about salary
     */
    private function isSalaryQuery($message)
    {
        $keywords = ['gaji', 'salary', 'penghasilan', 'income', 'kompensasi'];
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if message is asking about skill gaps
     */
    private function isSkillGapQuery($message)
    {
        $keywords = ['skill gap', 'skill yang kurang', 'kemampuan yang perlu', 'belajar apa'];
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if message is asking about industry
     */
    private function isIndustryQuery($message)
    {
        $keywords = ['industri', 'industry', 'sektor', 'bidang', 'tren'];
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if message is asking about job market
     */
    private function isJobMarketQuery($message)
    {
        $keywords = ['pasar kerja', 'job market', 'lowongan', 'peluang kerja', 'demand'];
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get career-specific recommendations
     */
    private function getCareerSpecificRecommendations($goal, $experience)
    {
        $recommendations = [
            'software developer' => $experience < 3 ? 'Fokus pada full-stack skills dan portfolio projects' : 'Spesialisasi di architecture atau lead role',
            'data scientist' => $experience < 3 ? 'Master Python, SQL, dan statistics fundamentals' : 'Advanced ML dan business domain expertise',
            'product manager' => $experience < 3 ? 'Learn agile, user research, dan basic tech skills' : 'Strategic thinking dan stakeholder management',
            'ui/ux designer' => $experience < 3 ? 'Build portfolio dan user research skills' : 'Design systems dan team leadership',
            'digital marketer' => $experience < 3 ? 'Master analytics, SEO, dan content creation' : 'Growth hacking dan marketing automation'
        ];
        
        $goal = strtolower($goal);
        foreach ($recommendations as $career => $rec) {
            if (strpos($goal, $career) !== false) {
                return $rec;
            }
        }
        
        return 'Fokus pada continuous learning dan networking di bidang ini';
    }

    /**
     * Get skill development recommendations
     */
    private function getSkillDevelopmentRecommendations($userProfile)
    {
        $experience = $userProfile['professional_info']['experience_years'] ?? 0;
        $currentSkills = $userProfile['professional_info']['skills'] ?? [];
        
        $recommendations = [];
        
        // Basic recommendations for all levels
        if (!in_array('communication', array_map('strtolower', $currentSkills))) {
            $recommendations['Communication Skills'] = 'Essential untuk semua level karir';
        }
        
        if ($experience < 3) {
            $recommendations['Problem Solving'] = 'Fundamental skill untuk early career';
            $recommendations['Time Management'] = 'Penting untuk produktivitas';
        } else {
            $recommendations['Leadership'] = 'Persiapan untuk senior role';
            $recommendations['Strategic Thinking'] = 'Skill untuk management level';
        }
        
        return $recommendations;
    }

    /**
     * Identify missing skills based on career goals
     */
    private function identifyMissingSkills($currentSkills, $careerGoals, $experience)
    {
        $skillMap = [
            'Technical Skills' => [
                'Programming' => 'high',
                'Database Management' => 'medium',
                'Cloud Computing' => 'medium',
                'DevOps' => 'low'
            ],
            'Soft Skills' => [
                'Leadership' => $experience >= 3 ? 'high' : 'medium',
                'Communication' => 'high',
                'Project Management' => 'medium'
            ],
            'Business Skills' => [
                'Data Analysis' => 'medium',
                'Strategic Planning' => $experience >= 5 ? 'high' : 'low'
            ]
        ];
        
        // Filter out skills user already has
        $currentSkillsLower = array_map('strtolower', $currentSkills);
        
        foreach ($skillMap as $category => &$skills) {
            foreach ($skills as $skill => $importance) {
                if (in_array(strtolower($skill), $currentSkillsLower)) {
                    unset($skills[$skill]);
                }
            }
        }
        
        return array_filter($skillMap, function($skills) {
            return !empty($skills);
        });
    }

    /**
     * Find relevant courses for a skill
     */
    private function findRelevantCourses($skill)
    {
        return Course::where('status', 'published')
            ->where(function ($query) use ($skill) {
                $query->where('title', 'like', "%{$skill}%")
                      ->orWhere('description', 'like', "%{$skill}%");
            })
            ->orderBy('total_students', 'desc')
            ->limit(1)
            ->get(['id', 'title', 'price', 'is_free'])
            ->toArray();
    }

    /**
     * Extract job title from message
     */
    private function extractJobTitleFromMessage($message)
    {
        $jobTitles = [
            'software developer', 'web developer', 'mobile developer',
            'data scientist', 'data analyst', 'product manager',
            'ui designer', 'ux designer', 'digital marketer'
        ];
        
        foreach ($jobTitles as $title) {
            if (strpos(strtolower($message), $title) !== false) {
                return $title;
            }
        }
        
        return null;
    }

    /**
     * Extract industry from message
     */
    private function extractIndustryFromMessage($message)
    {
        $industries = ['teknologi', 'fintech', 'e-commerce', 'startup', 'banking', 'healthcare'];
        
        foreach ($industries as $industry) {
            if (strpos(strtolower($message), $industry) !== false) {
                return $industry;
            }
        }
        
        return null;
    }

    /**
     * Get salary ranges based on job title and experience
     */
    private function getSalaryRanges($jobTitle, $experience)
    {
        $baseSalaries = [
            'software developer' => [0 => '8-15 juta', 3 => '15-25 juta', 7 => '25-40 juta'],
            'data scientist' => [0 => '10-18 juta', 3 => '18-30 juta', 7 => '30-50 juta'],
            'product manager' => [0 => '12-20 juta', 3 => '20-35 juta', 7 => '35-60 juta'],
            'ui/ux designer' => [0 => '6-12 juta', 3 => '12-22 juta', 7 => '22-35 juta'],
            'digital marketer' => [0 => '5-10 juta', 3 => '10-20 juta', 7 => '20-35 juta']
        ];
        
        $jobTitle = strtolower($jobTitle);
        $ranges = $baseSalaries['software developer']; // default
        
        foreach ($baseSalaries as $title => $salaryRange) {
            if (strpos($jobTitle, $title) !== false) {
                $ranges = $salaryRange;
                break;
            }
        }
        
        $result = [];
        if ($experience < 3) {
            $result['Junior (0-2 tahun)'] = $ranges[0];
        }
        if ($experience >= 2) {
            $result['Mid-level (3-6 tahun)'] = $ranges[3];
        }
        if ($experience >= 6) {
            $result['Senior (7+ tahun)'] = $ranges[7];
        }
        
        return $result;
    }

    /**
     * Get industry insights
     */
    private function getIndustryInsights($industry)
    {
        $insights = [
            'teknologi' => [
                'trends' => ['AI/ML adoption', 'Cloud-first approach', 'Remote work normalization'],
                'hot_skills' => ['Python', 'React', 'AWS', 'Docker', 'Kubernetes'],
                'opportunities' => ['Startup ecosystem growth', 'Digital transformation', 'Fintech expansion'],
                'challenges' => ['Talent shortage', 'Rapid technology change', 'Competition']
            ],
            'fintech' => [
                'trends' => ['Digital banking', 'Cryptocurrency', 'Embedded finance'],
                'hot_skills' => ['Blockchain', 'Security', 'API development', 'Compliance'],
                'opportunities' => ['Financial inclusion', 'SME lending', 'Wealth management'],
                'challenges' => ['Regulation compliance', 'Security threats', 'Customer trust']
            ]
        ];
        
        return $insights[$industry] ?? $insights['teknologi'];
    }
}
