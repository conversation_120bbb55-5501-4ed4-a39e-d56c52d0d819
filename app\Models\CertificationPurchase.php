<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CertificationPurchase extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'course_id',
        'payment_id',
        'certification_type',
        'status',
        'amount_paid',
        'purchased_at',
        'nala_prompts_allocated',
        'nala_prompts_used',
        'nala_prompts_remaining',
        'has_basic_career_predictor',
        'has_ice_access',
        'has_ai_teaching_assistant',
        'completed_at',
        'certificate_number',
        'certificate_file_path',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount_paid' => 'decimal:2',
        'purchased_at' => 'datetime',
        'nala_prompts_allocated' => 'integer',
        'nala_prompts_used' => 'integer',
        'nala_prompts_remaining' => 'integer',
        'has_basic_career_predictor' => 'boolean',
        'has_ice_access' => 'boolean',
        'has_ai_teaching_assistant' => 'boolean',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that purchased the certification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for this certification.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the payment for this certification purchase.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Scope a query to only include active certifications.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include completed certifications.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query by certification type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('certification_type', $type);
    }

    /**
     * Check if the certification is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Use NALA prompts for this certification.
     */
    public function useNalaPrompts(int $count = 1): bool
    {
        if ($this->nala_prompts_remaining >= $count) {
            $this->update([
                'nala_prompts_used' => $this->nala_prompts_used + $count,
                'nala_prompts_remaining' => $this->nala_prompts_remaining - $count,
            ]);
            return true;
        }

        return false;
    }

    /**
     * Check if user can use NALA prompts for this certification.
     */
    public function canUseNalaPrompts(int $count = 1): bool
    {
        return $this->nala_prompts_remaining >= $count;
    }

    /**
     * Get the formatted amount paid.
     */
    public function getFormattedAmountPaidAttribute(): string
    {
        return 'IDR ' . number_format($this->amount_paid, 0, ',', '.');
    }

    /**
     * Get certification type name in Indonesian.
     */
    public function getCertificationTypeNameAttribute(): string
    {
        return match($this->certification_type) {
            'short' => 'Sertifikasi Singkat',
            'medium' => 'Sertifikasi Menengah',
            'comprehensive' => 'Sertifikasi Komprehensif',
            default => ucfirst($this->certification_type)
        };
    }

    /**
     * Complete the certification.
     */
    public function complete(string $certificateNumber = null, string $certificateFilePath = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'certificate_number' => $certificateNumber ?? $this->generateCertificateNumber(),
            'certificate_file_path' => $certificateFilePath,
        ]);
    }

    /**
     * Generate a unique certificate number.
     */
    private function generateCertificateNumber(): string
    {
        $prefix = strtoupper(substr($this->certification_type, 0, 3));
        $year = now()->year;
        $month = now()->format('m');
        $sequence = str_pad($this->id, 6, '0', STR_PAD_LEFT);
        
        return "CERT-{$prefix}-{$year}{$month}-{$sequence}";
    }
}
