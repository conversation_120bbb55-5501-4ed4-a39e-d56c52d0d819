<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class CourseValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $tutor;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a tutor user
        $this->tutor = User::factory()->create([
            'is_tutor' => true,
            'tutor_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        // Create tutor profile
        \App\Models\TutorProfile::create([
            'user_id' => $this->tutor->id,
            'bio' => 'Test tutor bio',
            'expertise' => ['Programming', 'Web Development'],
            'experience_years' => 5,
            'education' => 'Computer Science',
            'certifications' => ['Laravel Certified'],
            'status' => 'approved',
        ]);

        // Create a category
        $this->category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Programming courses',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_validates_course_creation_with_form_request()
    {
        // Create a simple validation test using Laravel's validation
        $validator = \Illuminate\Support\Facades\Validator::make([], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_title', $validator->errors()->toArray());
        $this->assertArrayHasKey('course_category', $validator->errors()->toArray());
        $this->assertArrayHasKey('course_description', $validator->errors()->toArray());
        $this->assertArrayHasKey('course_level', $validator->errors()->toArray());
        $this->assertArrayHasKey('course_type', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_course_title_length()
    {
        // Test minimum length (less than 10 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Short', // 5 characters
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 50),
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_title', $validator->errors()->toArray());

        // Test maximum length (more than 255 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => str_repeat('A', 256),
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 50),
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_title', $validator->errors()->toArray());

        // Test valid length (10-255 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 50),
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function it_validates_course_description_length()
    {
        // Test minimum length (less than 50 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => 'Too short', // 9 characters
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_description', $validator->errors()->toArray());

        // Test maximum length (more than 1000 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 1001),
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_description', $validator->errors()->toArray());

        // Test valid length (50-1000 characters)
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => 'This is a valid course description that is definitely long enough to meet the minimum requirements for course descriptions.',
            'course_level' => 'beginner',
            'course_type' => 'free'
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
        ]);

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function it_validates_paid_course_price()
    {
        // Test missing price for paid course
        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.store-course'), [
                'course_title' => 'Valid Course Title',
                'course_category' => $this->category->id,
                'course_description' => str_repeat('A', 50),
                'course_level' => 'beginner',
                'course_type' => 'paid'
            ]);

        $response->assertSessionHasErrors(['course_price']);

        // Test minimum price
        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.store-course'), [
                'course_title' => 'Valid Course Title',
                'course_category' => $this->category->id,
                'course_description' => str_repeat('A', 50),
                'course_level' => 'beginner',
                'course_type' => 'paid',
                'course_price' => 25000
            ]);

        $response->assertSessionHasErrors(['course_price']);
    }

    /** @test */
    public function it_validates_thumbnail_file()
    {
        Storage::fake('public');

        // Test invalid file type
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);

        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.store-course'), [
                'course_title' => 'Valid Course Title',
                'course_category' => $this->category->id,
                'course_description' => str_repeat('A', 50),
                'course_level' => 'beginner',
                'course_type' => 'free',
                'thumbnail' => $invalidFile
            ]);

        $response->assertSessionHasErrors(['thumbnail']);
    }

    /** @test */
    public function it_creates_course_with_valid_data()
    {
        Storage::fake('public');

        $validImage = UploadedFile::fake()->image('thumbnail.jpg', 800, 600);

        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.store-course'), [
                'course_title' => 'Complete React Course',
                'course_category' => $this->category->id,
                'course_description' => 'This is a comprehensive React course that covers all the fundamentals and advanced topics.',
                'course_level' => 'beginner',
                'course_duration' => '20 hours',
                'course_type' => 'paid',
                'course_price' => 299000,
                'learning_outcomes' => [
                    'Students will learn React fundamentals',
                    'Students will build real projects'
                ],
                'requirements' => [
                    'Basic JavaScript knowledge',
                    'HTML and CSS basics'
                ],
                'target_audience' => [
                    'Beginner developers',
                    'JavaScript developers'
                ],
                'thumbnail' => $validImage
            ]);

        $response->assertRedirect(route('tutor.courses'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('courses', [
            'title' => 'Complete React Course',
            'tutor_id' => $this->tutor->id,
            'category_id' => $this->category->id,
            'price' => 299000,
            'is_free' => false,
            'status' => 'draft'
        ]);
    }

    /** @test */
    public function it_validates_numeric_price_correctly()
    {
        // Test valid prices
        $validPrices = [30000, 50000, 100000, 299000];

        foreach ($validPrices as $price) {
            $response = $this->actingAs($this->tutor)
                ->post(route('tutor.store-course'), [
                    'course_title' => 'Valid Course Title That Is Long Enough',
                    'course_description' => 'This is a test course description that is definitely long enough to meet the minimum requirements for course descriptions.',
                    'course_category' => $this->category->id,
                    'course_level' => 'beginner',
                    'course_type' => 'paid',
                    'course_price' => $price,
                ]);

            $response->assertSessionDoesntHaveErrors(['course_price']);
        }

        // Test invalid prices
        $invalidPrices = [0, 15000, 29999];

        foreach ($invalidPrices as $price) {
            $response = $this->actingAs($this->tutor)
                ->post(route('tutor.store-course'), [
                    'course_title' => 'Valid Course Title That Is Long Enough',
                    'course_description' => 'This is a test course description that is definitely long enough to meet the minimum requirements for course descriptions.',
                    'course_category' => $this->category->id,
                    'course_level' => 'beginner',
                    'course_type' => 'paid',
                    'course_price' => $price,
                ]);

            $response->assertSessionHasErrors(['course_price']);
        }
    }

    /** @test */
    public function it_validates_price_with_numeric_rules()
    {
        // Test that price validation works correctly for numeric fields
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 50),
            'course_level' => 'beginner',
            'course_type' => 'paid',
            'course_price' => 25000, // Below minimum
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|min:30000|numeric',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('course_price', $validator->errors()->toArray());

        // Test valid price
        $validator = \Illuminate\Support\Facades\Validator::make([
            'course_title' => 'Valid Course Title That Is Long Enough',
            'course_category' => $this->category->id,
            'course_description' => str_repeat('A', 50),
            'course_level' => 'beginner',
            'course_type' => 'paid',
            'course_price' => 50000, // Valid price
        ], [
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|min:30000|numeric',
        ]);

        $this->assertFalse($validator->fails());
    }
}
