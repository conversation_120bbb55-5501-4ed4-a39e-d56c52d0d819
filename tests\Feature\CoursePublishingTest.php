<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CoursePublishingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $tutor;
    protected $course;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a category
        $this->category = Category::factory()->create([
            'name' => 'Programming',
            'slug' => 'programming',
            'is_active' => true,
        ]);

        // Create a tutor user
        $this->tutor = User::factory()->create([
            'is_tutor' => true,
            'is_admin' => false,
            'is_superadmin' => false,
            'tutor_status' => 'approved',
            'referral_code' => 'TEST1234',
        ]);

        // Create a course
        $this->course = Course::factory()->create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $this->category->id,
            'title' => 'Test Course',
            'description' => 'Test course description',
            'status' => 'draft',
            'is_free' => false,
            'price' => 50000,
        ]);
    }

    /** @test */
    public function tutor_can_publish_course_with_content()
    {
        // Create chapter and lesson
        $chapter = CourseChapter::factory()->create([
            'course_id' => $this->course->id,
            'title' => 'Test Chapter',
            'is_published' => true,
        ]);

        $lesson = CourseLesson::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter->id,
            'title' => 'Test Lesson',
            'type' => 'video',
            'is_published' => true,
        ]);

        $response = $this->actingAs($this->tutor)
            ->post('/tutor/courses/' . $this->course->id . '/publish');

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Kursus berhasil diterbitkan! Sekarang siswa dapat mendaftar di kursus Anda.');

        $this->course->refresh();
        $this->assertEquals('published', $this->course->status);
        $this->assertNotNull($this->course->published_at);
    }

    /** @test */
    public function tutor_cannot_publish_course_without_content()
    {
        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.courses.publish', $this->course));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Tidak dapat menerbitkan kursus. Pastikan kursus memiliki minimal 1 bab dan 1 materi yang dipublikasikan.');

        $this->course->refresh();
        $this->assertEquals('draft', $this->course->status);
        $this->assertNull($this->course->published_at);
    }

    /** @test */
    public function tutor_can_save_course_as_draft()
    {
        // First publish the course
        $this->course->update([
            'status' => 'published',
            'published_at' => now(),
        ]);

        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.courses.save-draft', $this->course));

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Kursus berhasil disimpan sebagai draft.');

        $this->course->refresh();
        $this->assertEquals('draft', $this->course->status);
        $this->assertNull($this->course->published_at);
    }

    /** @test */
    public function non_tutor_cannot_access_course_publishing()
    {
        $user = User::factory()->create([
            'is_tutor' => false,
            'is_admin' => false,
            'is_superadmin' => false,
        ]);

        $response = $this->actingAs($user)
            ->post(route('tutor.courses.publish', $this->course));

        $response->assertStatus(403);
    }

    /** @test */
    public function tutor_cannot_publish_other_tutors_course()
    {
        $otherTutor = User::factory()->create([
            'is_tutor' => true,
            'is_admin' => false,
            'is_superadmin' => false,
            'tutor_status' => 'approved',
            'referral_code' => 'OTHER123',
        ]);

        $response = $this->actingAs($otherTutor)
            ->post(route('tutor.courses.publish', $this->course));

        $response->assertStatus(403);
    }

    /** @test */
    public function curriculum_index_shows_course_status_correctly()
    {
        $response = $this->actingAs($this->tutor)
            ->get(route('tutor.curriculum.index', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Status Kursus');
        $response->assertSee('Draft');
        $response->assertSee('Publikasikan Kursus');
    }

    /** @test */
    public function curriculum_index_shows_referral_code_section()
    {
        $response = $this->actingAs($this->tutor)
            ->get(route('tutor.curriculum.index', $this->course));

        $response->assertStatus(200);
        $response->assertSee('Kode Referral');
        $response->assertSee('Link Kursus dengan Referral');
        $response->assertSee('Kode Referral Anda');
    }

    /** @test */
    public function referral_link_contains_correct_course_url()
    {
        $response = $this->actingAs($this->tutor)
            ->get(route('tutor.curriculum.index', $this->course));

        $expectedUrl = route('course.show', $this->course->slug) . '?ref=' . $this->tutor->getReferralCode();
        $response->assertSee($expectedUrl, false);
    }
}
