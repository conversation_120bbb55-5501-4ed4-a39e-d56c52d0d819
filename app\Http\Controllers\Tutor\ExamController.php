<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\ExamQuestionOption;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExamController extends Controller
{
    /**
     * Display a listing of the tutor's exams.
     */
    public function index()
    {
        $user = Auth::user();

        $exams = Exam::with(['category', 'questions'])
            ->where('tutor_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $stats = [
            'total_exams' => Exam::where('tutor_id', $user->id)->count(),
            'published_exams' => Exam::where('tutor_id', $user->id)->where('is_published', true)->count(),
            'total_enrollments' => 0, // TODO: Implement with real enrollment data
            'total_revenue' => 0, // TODO: Implement with real payment data
        ];

        return view('tutor.exams.index', compact('exams', 'stats'));
    }

    /**
     * Show the form for creating a new exam.
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name')->get();
        return view('tutor.exams.create', compact('categories'));
    }

    /**
     * Store a newly created exam in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'category_id' => 'required|exists:categories,id',
            'exam_type' => 'required|in:free,paid',
            'price' => 'required_if:exam_type,paid|numeric|min:0',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'time_limit' => 'required|integer|min:1|max:300',
            'max_attempts' => 'required|integer|min:1|max:10',
            'passing_score' => 'required|integer|min:0|max:100',
            'shuffle_questions' => 'boolean',
            'show_results_immediately' => 'boolean',
            'certificate_enabled' => 'boolean',
            'instructions' => 'nullable|string|max:2000',
            'questions' => 'required|array|min:1',
            'questions.*.question' => 'required|string',
            'questions.*.type' => 'required|in:multiple_choice,true_false,short_answer',
            'questions.*.points' => 'required|integer|min:1|max:100',
            'questions.*.explanation' => 'nullable|string',
            'questions.*.options' => 'required_if:questions.*.type,multiple_choice,true_false|array',
            'questions.*.correct_answer' => 'required_unless:questions.*.type,short_answer',
        ]);

        // Determine if exam is free and set price
        $isFree = $validated['exam_type'] === 'free';
        $price = $isFree ? 0 : ($validated['price'] ?? 0);

        // Validate minimum price for paid exams
        if (!$isFree && $price < 15000) {
            return back()->withErrors(['price' => 'Harga minimum untuk ujian berbayar adalah IDR 15.000'])->withInput();
        }

        try {
            DB::beginTransaction();

            // Create the exam
            $exam = Exam::create([
                'tutor_id' => Auth::id(),
                'title' => $validated['title'],
                'description' => $validated['description'],
                'category_id' => $validated['category_id'],
                'price' => $price,
                'difficulty_level' => $validated['difficulty_level'],
                'time_limit' => $validated['time_limit'],
                'max_attempts' => $validated['max_attempts'],
                'passing_score' => $validated['passing_score'],
                'shuffle_questions' => $validated['shuffle_questions'] ?? false,
                'show_results_immediately' => $validated['show_results_immediately'] ?? true,
                'certificate_enabled' => $validated['certificate_enabled'] ?? false,
                'instructions' => $validated['instructions'],
                'is_published' => false, // Default to draft
            ]);

            // Create questions and options
            foreach ($validated['questions'] as $index => $questionData) {
                $question = ExamQuestion::create([
                    'exam_id' => $exam->id,
                    'question' => $questionData['question'],
                    'type' => $questionData['type'],
                    'points' => $questionData['points'],
                    'sort_order' => $index + 1,
                    'explanation' => $questionData['explanation'] ?? null,
                    'is_required' => true,
                ]);

                // Create options for multiple choice and true/false questions
                if (in_array($questionData['type'], ['multiple_choice', 'true_false'])) {
                    foreach ($questionData['options'] as $optionIndex => $optionText) {
                        if (!empty($optionText)) {
                            $isCorrect = false;

                            if ($questionData['type'] === 'multiple_choice') {
                                $correctIndex = array_search($questionData['correct_answer'], ['A', 'B', 'C', 'D']);
                                $isCorrect = $optionIndex === $correctIndex;
                            } elseif ($questionData['type'] === 'true_false') {
                                $isCorrect = ($optionIndex === 0 && $questionData['correct_answer'] === 'A') ||
                                           ($optionIndex === 1 && $questionData['correct_answer'] === 'B');
                            }

                            ExamQuestionOption::create([
                                'question_id' => $question->id,
                                'option_text' => $optionText,
                                'is_correct' => $isCorrect,
                                'sort_order' => $optionIndex + 1,
                            ]);
                        }
                    }
                }
            }

            DB::commit();

            return redirect()->route('tutor.exams.show', $exam)
                ->with('success', 'Ujian berhasil dibuat! Anda dapat mengedit atau menerbitkannya.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Terjadi kesalahan saat membuat ujian. Silakan coba lagi.');
        }
    }

    /**
     * Display the specified exam.
     */
    public function show(Exam $exam)
    {
        // Ensure the exam belongs to the authenticated tutor
        if ($exam->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to exam.');
        }

        $exam->load(['questions.options', 'category', 'enrollments', 'attempts']);

        $stats = [
            'total_questions' => $exam->questions->count(),
            'total_points' => $exam->questions->sum('points'),
            'total_enrollments' => $exam->enrollments->count(),
            'total_attempts' => $exam->attempts->count(),
            'average_score' => $exam->attempts->avg('score_percentage') ?? 0,
            'pass_rate' => $exam->attempts->count() > 0
                ? ($exam->attempts->where('is_passed', true)->count() / $exam->attempts->count()) * 100
                : 0,
        ];

        return view('tutor.exams.show', compact('exam', 'stats'));
    }

    /**
     * Show the form for editing the specified exam.
     */
    public function edit(Exam $exam)
    {
        // Ensure the exam belongs to the authenticated tutor
        if ($exam->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to exam.');
        }

        $exam->load(['questions.options', 'category']);
        $categories = Category::active()->orderBy('name')->get();

        return view('tutor.exams.edit', compact('exam', 'categories'));
    }

    /**
     * Update the specified exam in storage.
     */
    public function update(Request $request, Exam $exam)
    {
        // Ensure the exam belongs to the authenticated tutor
        if ($exam->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to exam.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'category_id' => 'required|exists:categories,id',
            'exam_type' => 'required|in:free,paid',
            'price' => 'required_if:exam_type,paid|numeric|min:0',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'time_limit' => 'required|integer|min:1|max:300',
            'max_attempts' => 'required|integer|min:1|max:10',
            'passing_score' => 'required|integer|min:0|max:100',
            'shuffle_questions' => 'boolean',
            'show_results_immediately' => 'boolean',
            'certificate_enabled' => 'boolean',
            'instructions' => 'nullable|string|max:2000',
            'questions' => 'nullable|array',
            'questions.*.question' => 'required_with:questions|string',
            'questions.*.type' => 'required_with:questions|in:multiple_choice,true_false,short_answer',
            'questions.*.points' => 'required_with:questions|integer|min:1|max:100',
            'questions.*.explanation' => 'nullable|string',
            'questions.*.options' => 'required_if:questions.*.type,multiple_choice,true_false|array',
            'questions.*.correct_answer' => 'required_unless:questions.*.type,short_answer',
        ]);

        // Determine if exam is free and set price
        $isFree = $validated['exam_type'] === 'free';
        $price = $isFree ? 0 : ($validated['price'] ?? 0);

        // Validate minimum price for paid exams
        if (!$isFree && $price < 15000) {
            return back()->withErrors(['price' => 'Harga minimum untuk ujian berbayar adalah IDR 15.000'])->withInput();
        }

        try {
            DB::beginTransaction();

            // Update exam basic information
            $exam->update([
                'title' => $validated['title'],
                'description' => $validated['description'],
                'category_id' => $validated['category_id'],
                'price' => $price,
                'difficulty_level' => $validated['difficulty_level'],
                'time_limit' => $validated['time_limit'],
                'max_attempts' => $validated['max_attempts'],
                'passing_score' => $validated['passing_score'],
                'shuffle_questions' => $validated['shuffle_questions'] ?? false,
                'show_results_immediately' => $validated['show_results_immediately'] ?? true,
                'certificate_enabled' => $validated['certificate_enabled'] ?? false,
                'instructions' => $validated['instructions'],
            ]);

            // Update questions if provided
            if (isset($validated['questions']) && is_array($validated['questions'])) {
                // Delete existing questions and their options
                $exam->questions()->delete();

                // Create new questions and options
                foreach ($validated['questions'] as $index => $questionData) {
                    $question = ExamQuestion::create([
                        'exam_id' => $exam->id,
                        'question' => $questionData['question'],
                        'type' => $questionData['type'],
                        'points' => $questionData['points'],
                        'sort_order' => $index + 1,
                        'explanation' => $questionData['explanation'] ?? null,
                        'is_required' => true,
                    ]);

                    // Create options for multiple choice and true/false questions
                    if (in_array($questionData['type'], ['multiple_choice', 'true_false']) && isset($questionData['options'])) {
                        foreach ($questionData['options'] as $optionIndex => $optionText) {
                            if (empty(trim($optionText))) continue;

                            $optionLetter = chr(65 + $optionIndex); // A, B, C, D
                            $isCorrect = isset($questionData['correct_answer']) && $questionData['correct_answer'] === $optionLetter;

                            ExamQuestionOption::create([
                                'question_id' => $question->id,
                                'option_text' => $optionText,
                                'is_correct' => $isCorrect,
                                'sort_order' => $optionIndex + 1,
                            ]);
                        }
                    }
                }
            }

            DB::commit();

            return redirect()->route('tutor.exams.show', $exam)
                ->with('success', 'Ujian berhasil diperbarui!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Terjadi kesalahan saat memperbarui ujian. Silakan coba lagi.');
        }
    }

    /**
     * Remove the specified exam from storage.
     */
    public function destroy(Exam $exam)
    {
        // Ensure the exam belongs to the authenticated tutor
        if ($exam->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to exam.');
        }

        // Check if exam has enrollments
        if ($exam->enrollments()->count() > 0) {
            return back()->with('error', 'Tidak dapat menghapus ujian yang sudah memiliki peserta.');
        }

        $exam->delete();

        return redirect()->route('tutor.exams')
            ->with('success', 'Ujian berhasil dihapus!');
    }

    /**
     * Publish or unpublish the exam.
     */
    public function togglePublish(Exam $exam)
    {
        // Ensure the exam belongs to the authenticated tutor
        if ($exam->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to exam.');
        }

        // Check if exam has questions before publishing
        if (!$exam->is_published && $exam->questions()->count() === 0) {
            return back()->with('error', 'Tidak dapat menerbitkan ujian tanpa soal.');
        }

        $exam->update(['is_published' => !$exam->is_published]);

        $message = $exam->is_published
            ? 'Ujian berhasil diterbitkan!'
            : 'Ujian berhasil disembunyikan dari publik.';

        return back()->with('success', $message);
    }

    /**
     * Download CSV template for exam questions
     */
    public function downloadQuestionTemplate()
    {
        $csvContent = [
            '# TEMPLATE SOAL UJIAN - PANDUAN PENGGUNAAN',
            '# Kolom yang WAJIB diisi: question, type, points',
            '# Tipe soal yang didukung:',
            '#   - multiple_choice: Pilihan ganda (isi option_a sampai option_d, correct_answer: A/B/C/D)',
            '#   - true_false: Benar/Salah (kosongkan option_c dan option_d, correct_answer: A untuk Benar, B untuk Salah)',
            '#   - short_answer: Jawaban singkat (kosongkan semua option, correct_answer boleh kosong)',
            '# Points: angka 1-100',
            '# Explanation: opsional, penjelasan jawaban yang benar',
            '# Sort_order: urutan soal (opsional, akan diurutkan otomatis jika kosong)',
            '',
            'question,type,option_a,option_b,option_c,option_d,correct_answer,points,explanation,sort_order',
            'Siapa presiden pertama Indonesia?,multiple_choice,Soekarno,Soeharto,Habibie,Megawati,A,10,Soekarno adalah presiden pertama Republik Indonesia yang memproklamirkan kemerdekaan,1',
            'Apakah Indonesia adalah negara kepulauan?,true_false,Benar,Salah,,,A,5,Indonesia memiliki lebih dari 17000 pulau sehingga disebut negara kepulauan,2',
            'Sebutkan ibu kota provinsi Jawa Barat,short_answer,,,,,,10,Bandung adalah ibu kota provinsi Jawa Barat,3'
        ];

        $filename = 'template_soal_ujian_' . date('Y-m-d') . '.csv';

        return response()->streamDownload(function () use ($csvContent) {
            echo implode("\n", $csvContent);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
