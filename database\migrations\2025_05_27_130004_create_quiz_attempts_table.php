<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_attempts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('quiz_id'); // Foreign key to lesson_quizzes table
            
            // Attempt Information
            $table->integer('attempt_number')->default(1); // Which attempt this is (1st, 2nd, etc.)
            $table->timestamp('started_at'); // When the attempt started
            $table->timestamp('submitted_at')->nullable(); // When the attempt was submitted
            $table->timestamp('completed_at')->nullable(); // When the attempt was completed
            $table->integer('time_taken')->nullable(); // Time taken in seconds
            
            // Scoring
            $table->integer('total_questions')->default(0); // Total number of questions
            $table->integer('correct_answers')->default(0); // Number of correct answers
            $table->decimal('score_percentage', 5, 2)->default(0); // Score as percentage
            $table->integer('total_points')->default(0); // Total points earned
            $table->integer('max_points')->default(0); // Maximum possible points
            
            // Status
            $table->enum('status', ['in_progress', 'submitted', 'completed', 'abandoned'])->default('in_progress');
            $table->boolean('is_passed')->default(false); // Whether the attempt passed
            
            // Additional Data
            $table->json('question_order')->nullable(); // Order of questions if shuffled
            $table->json('metadata')->nullable(); // Additional metadata
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('quiz_id')->references('id')->on('lesson_quizzes')->onDelete('cascade');
            
            // Indexes
            $table->index('user_id');
            $table->index('quiz_id');
            $table->index('status');
            $table->index('is_passed');
            $table->index(['user_id', 'quiz_id', 'attempt_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_attempts');
    }
};
