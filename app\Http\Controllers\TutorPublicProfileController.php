<?php

namespace App\Http\Controllers;

use App\Models\TutorProfile;
use App\Models\Course;
use App\Models\Exam;
use App\Models\BlogPost;
use Illuminate\Http\Request;

class TutorPublicProfileController extends Controller
{
    /**
     * Display the specified tutor's public profile.
     */
    public function show($slug)
    {
        // Find the tutor profile by slug
        $profile = TutorProfile::bySlug($slug)->with('user')->first();

        if (!$profile || $profile->status !== 'approved') {
            abort(404, 'Tutor tidak ditemukan atau belum disetujui.');
        }

        // Get tutor's published courses
        $courses = Course::with(['category'])
            ->where('tutor_id', $profile->user_id)
            ->published()
            ->orderBy('published_at', 'desc')
            ->limit(6)
            ->get();

        // Get tutor's published exams
        $exams = Exam::with(['category'])
            ->where('tutor_id', $profile->user_id)
            ->where('is_published', true)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        // Get tutor's published blog posts
        $blogPosts = BlogPost::with(['category'])
            ->where('author_id', $profile->user_id)
            ->published()
            ->orderBy('published_at', 'desc')
            ->limit(6)
            ->get();

        // Calculate statistics
        $stats = [
            'total_courses' => Course::where('tutor_id', $profile->user_id)->published()->count(),
            'total_exams' => Exam::where('tutor_id', $profile->user_id)->where('is_published', true)->count(),
            'total_blog_posts' => BlogPost::where('author_id', $profile->user_id)->published()->count(),
            'total_students' => Course::where('tutor_id', $profile->user_id)
                ->published()
                ->withCount('enrolledStudents')
                ->get()
                ->sum('enrolled_students_count'),
            'average_rating' => Course::where('tutor_id', $profile->user_id)
                ->published()
                ->avg('average_rating') ?: 5.0,
        ];

        return view('tutor.public-profile', compact('profile', 'courses', 'exams', 'blogPosts', 'stats'));
    }
}
