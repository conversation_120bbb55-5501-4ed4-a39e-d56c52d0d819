<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserMembership extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'membership_plan_id',
        'payment_id',
        'status',
        'starts_at',
        'expires_at',
        'cancelled_at',
        'cancellation_reason',
        'team_size',
        'team_members',
        'is_team_leader',
        'team_leader_id',
        'nala_prompts_allocated',
        'nala_prompts_used_today',
        'nala_prompts_remaining',
        'nala_usage_reset_date',
        'has_unlimited_nala',
        'has_ice_full',
        'has_ai_teaching_assistants_courses',
        'has_ai_teaching_assistants_tryout',
        'has_free_certifications',
        'has_blog_access',
        'career_path_predictor',
        'has_priority_support',
        'total_nala_prompts_used',
        'last_nala_usage',
        'last_accessed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'team_members' => 'array',
        'is_team_leader' => 'boolean',
        'nala_prompts_allocated' => 'integer',
        'nala_prompts_used_today' => 'integer',
        'nala_prompts_remaining' => 'integer',
        'nala_usage_reset_date' => 'date',
        'has_unlimited_nala' => 'boolean',
        'has_ice_full' => 'boolean',
        'has_ai_teaching_assistants_courses' => 'boolean',
        'has_ai_teaching_assistants_tryout' => 'boolean',
        'has_free_certifications' => 'boolean',
        'has_priority_support' => 'boolean',
        'total_nala_prompts_used' => 'integer',
        'last_nala_usage' => 'datetime',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the membership.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the membership plan.
     */
    public function membershipPlan(): BelongsTo
    {
        return $this->belongsTo(MembershipPlan::class);
    }

    /**
     * Get the payment for this membership.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the team leader (for team members).
     */
    public function teamLeader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'team_leader_id');
    }

    /**
     * Scope a query to only include active memberships.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope a query to only include expired memberships.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere(function($q) {
                        $q->where('expires_at', '<=', now())
                          ->whereNotNull('expires_at');
                    });
    }

    /**
     * Check if the membership is currently active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active'
               && $this->starts_at <= now()
               && ($this->expires_at === null || $this->expires_at > now());
    }

    /**
     * Check if the membership is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired'
               || ($this->expires_at !== null && $this->expires_at <= now());
    }

    /**
     * Get days remaining until expiration.
     */
    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null; // Lifetime membership
        }

        $days = now()->diffInDays($this->expires_at, false);
        return $days > 0 ? $days : 0;
    }

    /**
     * Reset daily NALA prompts if needed.
     */
    public function resetDailyNalaPromptsIfNeeded(): void
    {
        $today = Carbon::today();

        if (!$this->nala_usage_reset_date || $this->nala_usage_reset_date->lt($today)) {
            $this->update([
                'nala_prompts_used_today' => 0,
                'nala_prompts_remaining' => $this->nala_prompts_allocated,
                'nala_usage_reset_date' => $today,
            ]);
        }
    }

    /**
     * Use NALA prompts.
     */
    public function useNalaPrompts(int $count = 1): bool
    {
        if ($this->has_unlimited_nala) {
            $this->increment('total_nala_prompts_used', $count);
            $this->update(['last_nala_usage' => now()]);
            return true;
        }

        $this->resetDailyNalaPromptsIfNeeded();

        if ($this->nala_prompts_remaining >= $count) {
            $this->update([
                'nala_prompts_used_today' => $this->nala_prompts_used_today + $count,
                'nala_prompts_remaining' => $this->nala_prompts_remaining - $count,
                'total_nala_prompts_used' => $this->total_nala_prompts_used + $count,
                'last_nala_usage' => now(),
            ]);
            return true;
        }

        return false;
    }

    /**
     * Check if user can use NALA prompts.
     */
    public function canUseNalaPrompts(int $count = 1): bool
    {
        if ($this->has_unlimited_nala) {
            return true;
        }

        $this->resetDailyNalaPromptsIfNeeded();
        return $this->nala_prompts_remaining >= $count;
    }

    /**
     * Get the formatted expiration date.
     */
    public function getFormattedExpirationAttribute(): string
    {
        if (!$this->expires_at) {
            return 'Seumur hidup';
        }

        return $this->expires_at->format('d M Y');
    }

    /**
     * Cancel the membership.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);
    }

    /**
     * Expire the membership.
     */
    public function expire(): void
    {
        $this->update([
            'status' => 'expired',
        ]);
    }
}
