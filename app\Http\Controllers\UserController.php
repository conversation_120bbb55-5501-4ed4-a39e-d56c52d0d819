<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\ExamAttempt;
use App\Models\LessonProgress;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Get real data from database
        $enrolledCourses = CourseEnrollment::where('user_id', $user->id)->count();
        $completedCourses = CourseEnrollment::where('user_id', $user->id)
            ->where('status', 'completed')
            ->count();

        // Get exam attempts
        $examAttempts = ExamAttempt::where('user_id', $user->id)->count();
        $passedExams = ExamAttempt::where('user_id', $user->id)
            ->where('is_passed', true)
            ->count();

        // Calculate total learning hours from lesson progress
        $totalMinutes = LessonProgress::where('user_id', $user->id)
            ->sum('time_spent_seconds') / 60;
        $totalHours = round($totalMinutes / 60, 1);

        // Calculate learning streak (simplified - days with activity in last 30 days)
        $learningStreak = LessonProgress::where('user_id', $user->id)
            ->where('last_accessed_at', '>=', now()->subDays(30))
            ->distinct('user_id')
            ->count();

        // Calculate XP points (simplified calculation)
        $xpPoints = ($completedCourses * 100) + ($passedExams * 50) + (int)($totalHours * 10);

        $stats = [
            'enrolled_courses' => $enrolledCourses,
            'completed_courses' => $completedCourses,
            'certificates' => $passedExams, // Certificates from passed exams
            'xp_points' => $xpPoints,
            'learning_streak' => min($learningStreak, 30), // Cap at 30 days
            'total_hours' => $totalHours,
            'exam_attempts' => $examAttempts,
            'passed_exams' => $passedExams
        ];

        // Get recent activities (enrollments, exam attempts, lesson progress)
        $recentActivities = collect();

        // Recent course enrollments
        $recentEnrollments = CourseEnrollment::with('course')
            ->where('user_id', $user->id)
            ->orderBy('enrolled_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($enrollment) {
                return [
                    'type' => 'course_enrollment',
                    'title' => 'Mendaftar kursus: ' . $enrollment->course->title,
                    'date' => $enrollment->enrolled_at,
                    'icon' => 'book',
                    'color' => 'blue',
                    'url' => route('course.show', $enrollment->course)
                ];
            });

        // Recent exam attempts
        $recentExamAttempts = ExamAttempt::with('exam')
            ->where('user_id', $user->id)
            ->orderBy('submitted_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($attempt) {
                return [
                    'type' => 'exam_attempt',
                    'title' => 'Mengikuti ujian: ' . $attempt->exam->title,
                    'date' => $attempt->submitted_at,
                    'icon' => 'clipboard',
                    'color' => $attempt->is_passed ? 'green' : 'red',
                    'url' => route('exams.result', [$attempt->exam, $attempt]),
                    'score' => $attempt->score_percentage
                ];
            });

        $recentActivities = $recentActivities->merge($recentEnrollments)
            ->merge($recentExamAttempts)
            ->sortByDesc('date')
            ->take(5);

        // Get real recommended courses from database
        $recommendedCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('is_featured', true)
            ->orderBy('average_rating', 'desc')
            ->orderBy('total_students', 'desc')
            ->limit(4)
            ->get()
            ->map(function($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'description' => $course->description,
                    'tutor' => $course->tutor->name,
                    'category' => $course->category->name ?? 'Umum',
                    'price' => $course->is_free ? 'Gratis' : 'Rp ' . number_format($course->price, 0, ',', '.'),
                    'level' => ucfirst($course->level),
                    'rating' => $course->average_rating ?: 0,
                    'students' => $course->total_students,
                    'url' => route('course.show', $course),
                    'thumbnail' => $course->thumbnail ? asset('storage/' . $course->thumbnail) : null
                ];
            });

        return view('user.dashboard', compact('user', 'stats', 'recentActivities', 'recommendedCourses'));
    }

    /**
     * Show the user profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Show the user courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get real enrolled courses from database with proper progress calculation
        $enrolledCourses = CourseEnrollment::with(['course.tutor', 'course.category', 'course.chapters.lessons'])
            ->where('user_id', $user->id)
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) use ($user) {
                $course = $enrollment->course;

                // Get all lesson IDs for this course
                $lessonIds = $course->chapters->flatMap(function ($chapter) {
                    return $chapter->lessons->pluck('id');
                });

                $totalLessons = $lessonIds->count();

                if ($totalLessons > 0) {
                    // Get user's progress for this course
                    $courseProgress = LessonProgress::where('user_id', $user->id)
                        ->whereIn('lesson_id', $lessonIds)
                        ->get();

                    $completedLessons = $courseProgress->where('status', 'completed')->count();
                    $progressPercentage = round(($completedLessons / $totalLessons) * 100);

                    // Get last accessed time
                    $lastAccessed = $courseProgress->max('last_accessed_at') ?? $enrollment->updated_at;
                } else {
                    $progressPercentage = 0;
                    $lastAccessed = $enrollment->updated_at;
                }

                return [
                    'enrollment' => $enrollment,
                    'course' => $course,
                    'progress' => $progressPercentage,
                    'last_accessed' => $lastAccessed,
                    'status' => $enrollment->status,
                    'is_completed' => $progressPercentage >= 100
                ];
            });

        // Separate completed and in-progress courses
        $completedCourses = $enrolledCourses->where('is_completed', true);
        $inProgressCourses = $enrolledCourses->where('is_completed', false);

        // Get available courses from database
        $availableCourses = Course::with(['tutor', 'category'])
            ->published()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return view('user.courses', compact('user', 'enrolledCourses', 'inProgressCourses', 'completedCourses', 'availableCourses'));
    }

    /**
     * Show the user progress page.
     */
    public function progress()
    {
        $user = Auth::user();

        // Get user's enrolled courses
        $enrolledCourses = CourseEnrollment::where('user_id', $user->id)
            ->with(['course.chapters.lessons'])
            ->get();

        $coursesInProgress = [];
        $totalLessons = 0;
        $completedLessons = 0;

        foreach ($enrolledCourses as $enrollment) {
            $course = $enrollment->course;

            // Get all lesson IDs for this course
            $lessonIds = $course->chapters->flatMap(function ($chapter) {
                return $chapter->lessons->pluck('id');
            });

            $totalLessons += $lessonIds->count();

            // Get user's progress for this course
            $courseProgress = LessonProgress::where('user_id', $user->id)
                ->whereIn('lesson_id', $lessonIds)
                ->get();

            $courseCompletedLessons = $courseProgress->where('status', 'completed')->count();
            $completedLessons += $courseCompletedLessons;

            // Calculate course progress percentage
            $courseProgressPercentage = $lessonIds->count() > 0
                ? round(($courseCompletedLessons / $lessonIds->count()) * 100)
                : 0;

            // Include courses that have any progress (> 0%)
            if ($courseProgressPercentage > 0) {
                $lastAccessed = $courseProgress->max('last_accessed_at');
                $totalTimeSpent = $courseProgress->sum('time_spent_seconds');

                $coursesInProgress[] = [
                    'id' => $course->id,
                    'title' => $course->title,
                    'instructor' => $course->tutor->name,
                    'progress' => $courseProgressPercentage,
                    'completed_modules' => $courseCompletedLessons,
                    'total_modules' => $lessonIds->count(),
                    'time_spent' => $this->formatTimeSpent($totalTimeSpent),
                    'last_accessed' => $lastAccessed ? $lastAccessed->diffForHumans() : 'Belum pernah',
                    'slug' => $course->slug
                ];
            }
        }

        // Calculate overall progress
        $overallProgress = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        // Create learning path based on enrolled courses (ordered by difficulty/level)
        $learningPath = [];
        if ($enrolledCourses->count() > 0) {
            // Sort courses by level (beginner -> intermediate -> advanced) and enrollment date
            $sortedEnrollments = $enrolledCourses->sortBy(function ($enrollment) {
                $levelOrder = ['beginner' => 1, 'intermediate' => 2, 'advanced' => 3];
                return ($levelOrder[$enrollment->course->level] ?? 4) . '_' . $enrollment->enrolled_at;
            });

            foreach ($sortedEnrollments as $index => $enrollment) {
                $course = $enrollment->course;
                $lessonIds = $course->chapters->flatMap(function ($chapter) {
                    return $chapter->lessons->pluck('id');
                });

                $courseProgress = LessonProgress::where('user_id', $user->id)
                    ->whereIn('lesson_id', $lessonIds)
                    ->get();

                $courseCompletedLessons = $courseProgress->where('status', 'completed')->count();
                $courseProgressPercentage = $lessonIds->count() > 0
                    ? round(($courseCompletedLessons / $lessonIds->count()) * 100)
                    : 0;

                $isCompleted = $courseProgressPercentage >= 100;
                $isCurrent = $courseProgressPercentage > 0 && $courseProgressPercentage < 100;

                // Only mark one course as current (the first one that's in progress)
                $shouldBeCurrent = $isCurrent && count(array_filter($learningPath, fn($step) => $step['current'])) === 0;

                $learningPath[] = [
                    'title' => $course->title,
                    'description' => $course->description,
                    'completed' => $isCompleted,
                    'current' => $shouldBeCurrent,
                    'progress' => $courseProgressPercentage,
                    'level' => ucfirst($course->level)
                ];
            }
        }

        $progressData = [
            'overall_progress' => $overallProgress,
            'courses_in_progress' => $coursesInProgress,
            'completed_modules' => $completedLessons,
            'total_modules' => $totalLessons,
            'learning_path' => $learningPath
        ];

        return view('user.progress', compact('user', 'progressData'));
    }

    /**
     * Format time spent in seconds to human readable format.
     */
    private function formatTimeSpent($seconds)
    {
        if ($seconds < 60) {
            return $seconds . ' detik';
        } elseif ($seconds < 3600) {
            return round($seconds / 60) . ' menit';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = round(($seconds % 3600) / 60);
            return $hours . ' jam ' . ($minutes > 0 ? $minutes . ' menit' : '');
        }
    }

    /**
     * Show the user certificates page.
     */
    public function certificates()
    {
        $user = Auth::user();

        // Use the same logic as courses() method to get completed courses
        $enrolledCourses = CourseEnrollment::with(['course.tutor', 'course.category', 'course.chapters.lessons'])
            ->where('user_id', $user->id)
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) use ($user) {
                $course = $enrollment->course;

                // Get all lesson IDs for this course
                $lessonIds = $course->chapters->flatMap(function ($chapter) {
                    return $chapter->lessons->pluck('id');
                });

                $totalLessons = $lessonIds->count();

                if ($totalLessons > 0) {
                    // Get user's progress for this course
                    $courseProgress = LessonProgress::where('user_id', $user->id)
                        ->whereIn('lesson_id', $lessonIds)
                        ->get();

                    $completedLessons = $courseProgress->where('status', 'completed')->count();
                    $progressPercentage = round(($completedLessons / $totalLessons) * 100);

                    // Get last accessed time
                    $lastAccessed = $courseProgress->max('last_accessed_at') ?? $enrollment->updated_at;
                } else {
                    $progressPercentage = 0;
                    $lastAccessed = $enrollment->updated_at;
                }

                return [
                    'enrollment' => $enrollment,
                    'course' => $course,
                    'progress' => $progressPercentage,
                    'last_accessed' => $lastAccessed,
                    'status' => $enrollment->status,
                    'is_completed' => $progressPercentage >= 100
                ];
            });

        // Get only completed courses (same logic as courses method)
        $completedCourses = $enrolledCourses->where('is_completed', true);

        // Convert to certificate format
        $courseCertificates = $completedCourses->map(function($enrollmentData) {
            $course = $enrollmentData['course'];
            $enrollment = $enrollmentData['enrollment'];

            return [
                'type' => 'course',
                'title' => $course->title,
                'tutor' => $course->tutor->name,
                'completed_at' => $enrollment->updated_at,
                'certificate_id' => 'COURSE-' . strtoupper(substr($enrollment->id, 0, 8)),
                'course_id' => $course->id,
                'level' => $course->level,
                'duration' => $course->duration_hours . ' jam'
            ];
        });

        // Get exam certificates (passed exams)
        $examCertificates = ExamAttempt::with(['exam.tutor'])
            ->where('user_id', $user->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->get()
            ->map(function($attempt) {
                return [
                    'type' => 'exam',
                    'title' => $attempt->exam->title,
                    'tutor' => $attempt->exam->tutor->name,
                    'completed_at' => $attempt->completed_at,
                    'certificate_id' => 'EXAM-' . strtoupper(substr($attempt->id, 0, 8)),
                    'score' => $attempt->score_percentage,
                    'exam_id' => $attempt->exam->id
                ];
            });

        // Combine and sort all certificates
        $allCertificates = $courseCertificates->concat($examCertificates)
            ->sortByDesc('completed_at');

        // Calculate stats
        $stats = [
            'total_certificates' => $allCertificates->count(),
            'course_certificates' => $courseCertificates->count(),
            'exam_certificates' => $examCertificates->count(),
            'average_rating' => 4.8 // You can calculate this from actual ratings later
        ];

        return view('user.certificates', compact('user', 'allCertificates', 'stats'));
    }

    /**
     * Show the user settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('user.settings', compact('user'));
    }

    /**
     * Show the user exams page.
     */
    public function exams()
    {
        $user = Auth::user();

        // Get user's exam enrollments with attempts
        $examEnrollments = \App\Models\ExamEnrollment::with(['exam.tutor', 'exam.category', 'exam.questions'])
            ->where('user_id', $user->id)
            ->where('payment_status', 'paid')
            ->orderBy('enrolled_at', 'desc')
            ->get();

        // Get user's exam attempts
        $examAttempts = \App\Models\ExamAttempt::with(['exam.tutor'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Calculate stats
        $stats = [
            'total_enrollments' => $examEnrollments->count(),
            'total_attempts' => \App\Models\ExamAttempt::where('user_id', $user->id)->count(),
            'passed_exams' => \App\Models\ExamAttempt::where('user_id', $user->id)->where('is_passed', true)->count(),
            'average_score' => \App\Models\ExamAttempt::where('user_id', $user->id)->avg('score_percentage') ?: 0,
        ];

        return view('user.exams', compact('user', 'examEnrollments', 'examAttempts', 'stats'));
    }

    /**
     * Show the user blog reading history page.
     */
    public function blog()
    {
        $user = Auth::user();

        // Get user's saved articles with blog post data
        $savedArticles = $user->savedBlogPosts()
            ->with(['author', 'category'])
            ->get();

        // Get recent posts for discovery
        $recentPosts = \App\Models\BlogPost::with(['author', 'category'])
            ->published()
            ->orderBy('published_at', 'desc')
            ->limit(6)
            ->get();

        // Get featured posts for recommendations
        $featuredPosts = \App\Models\BlogPost::with(['author', 'category'])
            ->published()
            ->featured()
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // Calculate stats
        $uniqueCategories = $savedArticles->pluck('category.id')->filter()->unique();
        $totalReadTime = $savedArticles->sum('read_time') ?? 0;

        $stats = [
            'total_saved' => $savedArticles->count(),
            'categories_saved' => $uniqueCategories->count(),
            'total_read_time' => $totalReadTime,
        ];

        return view('user.blog', compact('user', 'savedArticles', 'recentPosts', 'featuredPosts', 'stats'));
    }
}
