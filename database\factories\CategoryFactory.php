<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Programming',
            'Web Development',
            'Mobile Development',
            'Data Science',
            'Machine Learning',
            'UI/UX Design',
            'Digital Marketing',
            'Business',
            'Photography',
            'Music',
        ]);

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraph(),
            'icon' => $this->faker->randomElement([
                'code',
                'desktop-computer',
                'device-mobile',
                'chart-bar',
                'brain',
                'color-swatch',
                'speakerphone',
                'briefcase',
                'camera',
                'music-note',
            ]),
            'color' => $this->faker->hexColor(),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Indicate that the category is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a programming category.
     */
    public function programming(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Learn programming languages and software development',
            'icon' => 'code',
            'color' => '#3B82F6',
        ]);
    }

    /**
     * Create a web development category.
     */
    public function webDevelopment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Web Development',
            'slug' => 'web-development',
            'description' => 'Build modern web applications and websites',
            'icon' => 'desktop-computer',
            'color' => '#10B981',
        ]);
    }

    /**
     * Create a design category.
     */
    public function design(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'UI/UX Design',
            'slug' => 'ui-ux-design',
            'description' => 'Learn user interface and user experience design',
            'icon' => 'color-swatch',
            'color' => '#F59E0B',
        ]);
    }
}
