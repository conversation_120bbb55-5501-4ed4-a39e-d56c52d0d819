<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\User;
use Illuminate\Database\Seeder;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all student users (non-tutors)
        $students = User::where('is_tutor', false)->get();

        // Get all courses (both free and paid)
        $allCourses = Course::all();

        // Get specific tutors for targeted enrollments
        $sariDewi = User::where('email', '<EMAIL>')->first();
        $ahmadRahman = User::where('email', '<EMAIL>')->first();
        $superAdmin = User::where('email', '<EMAIL>')->first();

        if ($students->count() > 0 && $allCourses->count() > 0) {
            // Create enrollments for <PERSON><PERSON>'s courses (she should have students)
            $sariCourses = Course::where('tutor_id', $sariDewi->id)->get();
            foreach ($sariCourses as $course) {
                // Enroll multiple students in each of <PERSON><PERSON>'s courses
                $studentsToEnroll = $students->random(min(5, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => collect(['active', 'completed'])->random(),
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => now()->subDays(rand(1, 90)), // Random enrollment dates in last 90 days
                    ]);
                }
            }

            // Create enrollments for Ahmad's courses
            $ahmadCourses = Course::where('tutor_id', $ahmadRahman->id)->get();
            foreach ($ahmadCourses as $course) {
                $studentsToEnroll = $students->random(min(3, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => collect(['active', 'completed'])->random(),
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => now()->subDays(rand(1, 60)),
                    ]);
                }
            }

            // Create enrollments for SuperAdmin's courses
            $superAdminCourses = Course::where('tutor_id', $superAdmin->id)->get();
            foreach ($superAdminCourses as $course) {
                $studentsToEnroll = $students->random(min(4, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => collect(['active', 'completed'])->random(),
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => now()->subDays(rand(1, 120)),
                    ]);
                }
            }

            // Create some additional random enrollments for variety
            for ($i = 0; $i < 10; $i++) {
                $randomStudent = $students->random();
                $randomCourse = $allCourses->random();

                // Check if enrollment already exists
                $existingEnrollment = CourseEnrollment::where('user_id', $randomStudent->id)
                    ->where('course_id', $randomCourse->id)
                    ->first();

                if (!$existingEnrollment) {
                    CourseEnrollment::create([
                        'user_id' => $randomStudent->id,
                        'course_id' => $randomCourse->id,
                        'status' => collect(['active', 'completed'])->random(),
                        'amount_paid' => $randomCourse->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $randomStudent->id . '_' . $randomCourse->id,
                        'enrolled_at' => now()->subDays(rand(1, 180)),
                    ]);
                }
            }
        }
    }
}
