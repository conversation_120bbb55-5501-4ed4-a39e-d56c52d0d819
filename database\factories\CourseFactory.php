<?php

namespace Database\Factories;

use App\Models\Course;
use App\Models\User;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    protected $model = Course::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        
        return [
            'tutor_id' => User::factory(),
            'category_id' => Category::factory(),
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraph(2),
            'long_description' => $this->faker->paragraphs(3, true),
            'level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'duration' => $this->faker->randomElement(['2 jam', '5 jam', '10 jam', '20 jam']),
            'price' => $this->faker->randomFloat(2, 0, 500000),
            'is_free' => $this->faker->boolean(30), // 30% chance of being free
            'language' => 'id',
            'thumbnail' => null,
            'preview_video' => null,
            'status' => $this->faker->randomElement(['draft', 'published']),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'published_at' => function (array $attributes) {
                return $attributes['status'] === 'published' ? $this->faker->dateTimeBetween('-1 year', 'now') : null;
            },
            'learning_outcomes' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
            'requirements' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
            'target_audience' => [
                $this->faker->randomElement(['Pemula', 'Mahasiswa', 'Profesional']),
                $this->faker->randomElement(['Developer', 'Designer', 'Analyst']),
            ],
            'meta_title' => $title,
            'meta_description' => $this->faker->paragraph(),
            'tags' => [
                $this->faker->word(),
                $this->faker->word(),
                $this->faker->word(),
            ],
            'total_students' => $this->faker->numberBetween(0, 5000),
            'average_rating' => $this->faker->randomFloat(2, 3.0, 5.0),
            'total_reviews' => $this->faker->numberBetween(0, 1000),
            'total_lessons' => $this->faker->numberBetween(5, 50),
            'total_duration_minutes' => $this->faker->numberBetween(120, 3600),
        ];
    }

    /**
     * Indicate that the course is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ]);
    }

    /**
     * Indicate that the course is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free' => true,
            'price' => 0,
        ]);
    }

    /**
     * Indicate that the course is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free' => false,
            'price' => $this->faker->randomFloat(2, 50000, 500000),
        ]);
    }

    /**
     * Indicate that the course is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the course is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }

    /**
     * Set specific level.
     */
    public function level(string $level): static
    {
        return $this->state(fn (array $attributes) => [
            'level' => $level,
        ]);
    }

    /**
     * Set specific target audience.
     */
    public function targetAudience(array $audience): static
    {
        return $this->state(fn (array $attributes) => [
            'target_audience' => $audience,
        ]);
    }

    /**
     * Create a programming course.
     */
    public function programming(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Belajar ' . $this->faker->randomElement(['JavaScript', 'Python', 'PHP', 'Java']),
            'level' => $this->faker->randomElement(['beginner', 'intermediate']),
            'target_audience' => ['Web Developer', 'Software Engineer', 'Programmer'],
            'learning_outcomes' => [
                'Memahami konsep dasar programming',
                'Menulis kode yang clean dan efisien',
                'Membuat aplikasi sederhana',
            ],
            'requirements' => [
                'Pengetahuan dasar komputer',
                'Text editor atau IDE',
                'Motivasi untuk belajar',
            ],
            'tags' => ['programming', 'coding', 'development'],
        ]);
    }

    /**
     * Create a design course.
     */
    public function design(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Belajar ' . $this->faker->randomElement(['UI/UX Design', 'Graphic Design', 'Web Design']),
            'level' => 'beginner',
            'target_audience' => ['Designer', 'Creative Professional', 'Entrepreneur'],
            'learning_outcomes' => [
                'Memahami prinsip design',
                'Menggunakan tools design',
                'Membuat portfolio design',
            ],
            'requirements' => [
                'Kreativitas dan minat design',
                'Komputer dengan software design',
                'Portfolio kosong siap diisi',
            ],
            'tags' => ['design', 'creative', 'visual'],
        ]);
    }
}
