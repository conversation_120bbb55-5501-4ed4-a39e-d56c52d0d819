<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_submissions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('assignment_id'); // Foreign key to lesson_assignments table
            
            // Submission Information
            $table->longText('submission_text')->nullable(); // Text submission
            $table->json('submitted_files')->nullable(); // Array of submitted file paths
            $table->timestamp('submitted_at'); // When the assignment was submitted
            $table->boolean('is_late')->default(false); // Whether submission was late
            $table->integer('days_late')->default(0); // How many days late
            
            // Grading
            $table->integer('score')->nullable(); // Score given by tutor
            $table->integer('max_score')->default(100); // Maximum possible score
            $table->decimal('score_percentage', 5, 2)->nullable(); // Score as percentage
            $table->longText('feedback')->nullable(); // Feedback from tutor
            $table->uuid('graded_by')->nullable(); // Who graded this submission
            $table->timestamp('graded_at')->nullable(); // When it was graded
            
            // Status
            $table->enum('status', ['submitted', 'graded', 'returned', 'resubmitted'])->default('submitted');
            $table->boolean('is_passed')->nullable(); // Whether the submission passed
            
            // Resubmission
            $table->uuid('original_submission_id')->nullable(); // If this is a resubmission
            $table->boolean('allow_resubmission')->default(false); // Whether resubmission is allowed
            $table->timestamp('resubmission_deadline')->nullable(); // Deadline for resubmission
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('assignment_id')->references('id')->on('lesson_assignments')->onDelete('cascade');
            $table->foreign('graded_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('original_submission_id')->references('id')->on('assignment_submissions')->onDelete('set null');
            
            // Indexes
            $table->index('user_id');
            $table->index('assignment_id');
            $table->index('status');
            $table->index('is_late');
            $table->index('graded_by');
            $table->index('submitted_at');
            $table->index(['user_id', 'assignment_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_submissions');
    }
};
