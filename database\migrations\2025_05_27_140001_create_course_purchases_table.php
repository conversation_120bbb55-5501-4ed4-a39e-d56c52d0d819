<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_purchases', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('course_id'); // Foreign key to courses table
            $table->uuid('payment_id')->nullable(); // Foreign key to payments table

            // Purchase Details
            $table->enum('status', ['active', 'cancelled'])->default('active');
            $table->decimal('amount_paid', 10, 2);
            $table->timestamp('purchased_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();

            // Referral tracking
            $table->uuid('referrer_id')->nullable(); // User who referred the buyer
            $table->boolean('has_referral_bonus')->default(false);

            // Note: NALA features are now handled by user membership, not course purchases

            // Access tracking
            $table->timestamp('last_accessed_at')->nullable();
            $table->integer('total_access_count')->default(0);

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('referrer_id')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['course_id', 'status']);
            $table->index('referrer_id');
            $table->unique(['user_id', 'course_id'], 'unique_user_course');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_purchases');
    }
};
