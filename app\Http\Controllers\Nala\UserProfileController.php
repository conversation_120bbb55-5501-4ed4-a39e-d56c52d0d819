<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UserProfileController extends Controller
{
    /**
     * Build user profile for AI personalization
     */
    public function buildUserProfile($user)
    {
        if (!$user) {
            return $this->getGuestProfile();
        }

        $profile = [
            'basic_info' => $this->getBasicInfo($user),
            'professional_info' => $this->getProfessionalInfo($user),
            'learning_profile' => $this->getLearningProfile($user),
            'career_aspirations' => $this->getCareerAspirations($user),
            'completion_percentage' => 0,
            'missing_data' => []
        ];

        // Calculate completion percentage and missing data
        $this->calculateProfileCompletion($profile);

        return $profile;
    }

    /**
     * Get basic user information
     */
    private function getBasicInfo($user)
    {
        return [
            'name' => $user->name ?? 'Pengguna',
            'email' => $user->email,
            'location' => $user->location ?? null,
            'education_level' => $user->education_level ?? null,
            'age_range' => $this->calculateAgeRange($user->birth_date ?? null),
            'joined_date' => $user->created_at ? $user->created_at->format('Y-m-d') : null
        ];
    }

    /**
     * Get professional information
     */
    private function getProfessionalInfo($user)
    {
        return [
            'job_title' => $user->job_title ?? null,
            'company' => $user->company ?? null,
            'industry' => $user->industry ?? null,
            'experience_years' => $user->experience_years ?? 0,
            'skills' => $this->parseSkills($user->skills ?? null),
            'employment_status' => $user->employment_status ?? null
        ];
    }

    /**
     * Get learning profile
     */
    private function getLearningProfile($user)
    {
        return [
            'learning_interests' => $this->parseLearningInterests($user->learning_interests ?? null),
            'preferred_learning_style' => $user->preferred_learning_style ?? null,
            'available_time_per_week' => $user->available_time_per_week ?? null,
            'learning_goals' => $this->parseLearningGoals($user->learning_goals ?? null),
            'current_skill_level' => $user->current_skill_level ?? 'beginner'
        ];
    }

    /**
     * Get career aspirations
     */
    private function getCareerAspirations($user)
    {
        return [
            'career_goals' => $this->parseCareerGoals($user->career_goals ?? null),
            'target_position' => $user->target_position ?? null,
            'industry_interests' => $this->parseIndustryInterests($user->industry_interests ?? null),
            'salary_expectations' => $this->parseSalaryExpectations($user->salary_expectations ?? null),
            'career_timeline' => $user->career_timeline ?? null
        ];
    }

    /**
     * Get guest profile for non-authenticated users
     */
    private function getGuestProfile()
    {
        return [
            'basic_info' => [
                'name' => 'Pengunjung',
                'email' => null,
                'location' => null,
                'education_level' => null,
                'age_range' => null,
                'joined_date' => null
            ],
            'professional_info' => [
                'job_title' => null,
                'company' => null,
                'industry' => null,
                'experience_years' => 0,
                'skills' => [],
                'employment_status' => null
            ],
            'learning_profile' => [
                'learning_interests' => [],
                'preferred_learning_style' => null,
                'available_time_per_week' => null,
                'learning_goals' => [],
                'current_skill_level' => 'beginner'
            ],
            'career_aspirations' => [
                'career_goals' => [],
                'target_position' => null,
                'industry_interests' => [],
                'salary_expectations' => null,
                'career_timeline' => null
            ],
            'completion_percentage' => 0,
            'missing_data' => ['all_fields']
        ];
    }

    /**
     * Calculate profile completion percentage
     */
    private function calculateProfileCompletion(&$profile)
    {
        $totalFields = 0;
        $completedFields = 0;
        $missingData = [];

        // Check basic info
        foreach ($profile['basic_info'] as $key => $value) {
            if ($key !== 'email' && $key !== 'joined_date') { // Skip non-editable fields
                $totalFields++;
                if (!empty($value)) {
                    $completedFields++;
                } else {
                    $missingData[] = $key;
                }
            }
        }

        // Check professional info
        foreach ($profile['professional_info'] as $key => $value) {
            $totalFields++;
            if (!empty($value)) {
                $completedFields++;
            } else {
                $missingData[] = $key;
            }
        }

        // Check learning profile
        foreach ($profile['learning_profile'] as $key => $value) {
            $totalFields++;
            if (!empty($value)) {
                $completedFields++;
            } else {
                $missingData[] = $key;
            }
        }

        // Check career aspirations
        foreach ($profile['career_aspirations'] as $key => $value) {
            $totalFields++;
            if (!empty($value)) {
                $completedFields++;
            } else {
                $missingData[] = $key;
            }
        }

        $profile['completion_percentage'] = $totalFields > 0 ? round(($completedFields / $totalFields) * 100, 2) : 0;
        $profile['missing_data'] = $missingData;
    }

    /**
     * Calculate age range from birth date
     */
    private function calculateAgeRange($birthDate)
    {
        if (!$birthDate) {
            return null;
        }

        $age = now()->diffInYears($birthDate);
        
        if ($age < 18) return 'under_18';
        if ($age < 25) return '18_24';
        if ($age < 35) return '25_34';
        if ($age < 45) return '35_44';
        if ($age < 55) return '45_54';
        return '55_plus';
    }

    /**
     * Parse skills from JSON or string
     */
    private function parseSkills($skills)
    {
        if (is_string($skills)) {
            $decoded = json_decode($skills, true);
            return is_array($decoded) ? $decoded : explode(',', $skills);
        }
        
        return is_array($skills) ? $skills : [];
    }

    /**
     * Parse learning interests
     */
    private function parseLearningInterests($interests)
    {
        if (is_string($interests)) {
            $decoded = json_decode($interests, true);
            return is_array($decoded) ? $decoded : explode(',', $interests);
        }
        
        return is_array($interests) ? $interests : [];
    }

    /**
     * Parse learning goals
     */
    private function parseLearningGoals($goals)
    {
        if (is_string($goals)) {
            $decoded = json_decode($goals, true);
            return is_array($decoded) ? $decoded : explode(',', $goals);
        }
        
        return is_array($goals) ? $goals : [];
    }

    /**
     * Parse career goals
     */
    private function parseCareerGoals($goals)
    {
        if (is_string($goals)) {
            $decoded = json_decode($goals, true);
            return is_array($decoded) ? $decoded : explode(',', $goals);
        }
        
        return is_array($goals) ? $goals : [];
    }

    /**
     * Parse industry interests
     */
    private function parseIndustryInterests($interests)
    {
        if (is_string($interests)) {
            $decoded = json_decode($interests, true);
            return is_array($decoded) ? $decoded : explode(',', $interests);
        }
        
        return is_array($interests) ? $interests : [];
    }

    /**
     * Parse salary expectations
     */
    private function parseSalaryExpectations($salary)
    {
        if (is_string($salary)) {
            $decoded = json_decode($salary, true);
            if (is_array($decoded) && isset($decoded['min'], $decoded['max'])) {
                return $decoded;
            }
        }
        
        return null;
    }

    /**
     * Update user profile from conversation
     */
    public function updateProfileFromConversation(Request $request)
    {
        if (!auth()->check()) {
            return response()->json(['success' => false, 'message' => 'Authentication required'], 401);
        }

        $request->validate([
            'field' => 'required|string',
            'value' => 'required'
        ]);

        $user = auth()->user();
        $field = $request->input('field');
        $value = $request->input('value');

        // Map conversation fields to database fields
        $fieldMapping = [
            'job_title' => 'job_title',
            'company' => 'company',
            'experience_years' => 'experience_years',
            'skills' => 'skills',
            'learning_interests' => 'learning_interests',
            'career_goals' => 'career_goals',
            'industry_interests' => 'industry_interests',
            'location' => 'location',
            'education_level' => 'education_level'
        ];

        if (!isset($fieldMapping[$field])) {
            return response()->json(['success' => false, 'message' => 'Invalid field'], 400);
        }

        $dbField = $fieldMapping[$field];

        // Handle array fields
        if (in_array($field, ['skills', 'learning_interests', 'career_goals', 'industry_interests'])) {
            $value = is_array($value) ? json_encode($value) : $value;
        }

        try {
            $user->update([$dbField => $value]);
            
            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'field' => $field,
                'value' => $value
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile'
            ], 500);
        }
    }
}
