<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certification_purchases', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('course_id'); // Foreign key to courses table (for certification)
            $table->uuid('payment_id')->nullable(); // Foreign key to payments table

            // Certification Details
            $table->enum('certification_type', ['short', 'medium', 'comprehensive']);
            $table->enum('status', ['active', 'completed', 'expired'])->default('active');
            $table->decimal('amount_paid', 10, 2);
            $table->timestamp('purchased_at')->nullable();

            // NALA Features for certification
            $table->integer('nala_prompts_allocated')->default(0);
            $table->integer('nala_prompts_used')->default(0);
            $table->integer('nala_prompts_remaining')->default(0);

            // Features based on certification type
            $table->boolean('has_basic_career_predictor')->default(false);
            $table->boolean('has_ice_access')->default(false);
            $table->boolean('has_ai_teaching_assistant')->default(false);

            // Completion tracking
            $table->timestamp('completed_at')->nullable();
            $table->string('certificate_number')->nullable();
            $table->string('certificate_file_path')->nullable();

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['course_id', 'certification_type']);
            $table->index('certificate_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certification_purchases');
    }
};
