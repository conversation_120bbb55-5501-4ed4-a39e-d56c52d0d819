<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseEnrollmentController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Enroll user in a course.
     */
    public function enroll(Request $request, Course $course)
    {
        $user = Auth::user();

        // Check if course is published
        if ($course->status !== 'published') {
            return back()->with('error', 'Kursus ini tidak tersedia untuk pendaftaran.');
        }

        // Check if user is already enrolled
        $existingEnrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            // If already enrolled, redirect to learning page
            return redirect()->route('course.learn', $course)
                ->with('info', 'Anda sudah terdaftar di kursus ini.');
        }

        // Check if user is the course tutor
        if ($course->tutor_id === $user->id) {
            return back()->with('error', 'Anda tidak dapat mendaftar di kursus Anda sendiri.');
        }

        try {
            DB::beginTransaction();

            // Create enrollment
            $enrollment = CourseEnrollment::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'status' => 'active',
                'amount_paid' => $course->price,
                'enrolled_at' => now(),
            ]);

            // Update course total students count
            $course->increment('total_students');

            DB::commit();

            // Redirect to learning page
            return redirect()->route('course.learn', $course)
                ->with('success', 'Selamat! Anda berhasil mendaftar di kursus ini. Mulai belajar sekarang!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->with('error', 'Terjadi kesalahan saat mendaftar kursus. Silakan coba lagi.');
        }
    }

    /**
     * Unenroll user from a course.
     */
    public function unenroll(Request $request, Course $course)
    {
        $user = Auth::user();

        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return back()->with('error', 'Anda tidak terdaftar di kursus ini.');
        }

        try {
            DB::beginTransaction();

            // Update enrollment status instead of deleting
            $enrollment->update([
                'status' => 'cancelled',
                'completed_at' => null,
            ]);

            // Decrement course total students count
            $course->decrement('total_students');

            DB::commit();

            return back()->with('success', 'Anda berhasil keluar dari kursus ini.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->with('error', 'Terjadi kesalahan saat keluar dari kursus. Silakan coba lagi.');
        }
    }
}
