@extends('layouts.user')

@section('title', 'Progress Belajar - Ngambiskuy')

@section('content')
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Progress Belajar</h1>
                <p class="text-gray-600 mt-1">Pantau kemajuan dan pencapaian belajar Anda secara real-time</p>
            </div>
        </div>
    </div>

    <!-- Overall Progress -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm p-6 mb-6 border border-blue-100">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-lg font-semibold text-gray-900">📊 Ringkasan Progress</h2>
                <p class="text-sm text-gray-600">Statistik pembelajaran Anda secara keseluruhan</p>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center bg-white rounded-lg p-4 shadow-sm">
                <div class="relative w-20 h-20 mx-auto mb-3">
                    <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="35" stroke="#e5e7eb" stroke-width="6" fill="none"/>
                        <circle cx="50" cy="50" r="35" stroke="#3b82f6" stroke-width="6" fill="none"
                                stroke-dasharray="219.8" stroke-dashoffset="{{ 219.8 - (219.8 * $progressData['overall_progress'] / 100) }}"
                                stroke-linecap="round"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-lg font-bold text-gray-900">{{ $progressData['overall_progress'] }}%</span>
                    </div>
                </div>
                <h3 class="font-semibold text-gray-900">Progress Total</h3>
                <p class="text-xs text-gray-600">Dari {{ $progressData['total_modules'] }} materi</p>
            </div>

            <div class="text-center bg-white rounded-lg p-4 shadow-sm">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900">{{ count($progressData['courses_in_progress']) }}</h3>
                <p class="text-xs text-gray-600">Kursus Aktif</p>
            </div>

            <div class="text-center bg-white rounded-lg p-4 shadow-sm">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900">{{ $progressData['completed_modules'] }}</h3>
                <p class="text-xs text-gray-600">Materi Selesai</p>
            </div>

            <div class="text-center bg-white rounded-lg p-4 shadow-sm">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900">{{ $progressData['total_modules'] - $progressData['completed_modules'] }}</h3>
                <p class="text-xs text-gray-600">Materi Tersisa</p>
            </div>
        </div>
    </div>

    @if(count($progressData['courses_in_progress']) > 0)
        <!-- Active Courses -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">📚 Kursus Aktif</h2>
                    <p class="text-sm text-gray-600">Kursus yang sedang Anda ikuti dan progress terkini</p>
                </div>
                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    {{ count($progressData['courses_in_progress']) }} Kursus
                </span>
            </div>
            <div class="space-y-4">
                @foreach($progressData['courses_in_progress'] as $course)
                    <div class="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $course['title'] }}</h3>
                                <p class="text-gray-600 text-sm">👨‍🏫 {{ $course['instructor'] }}</p>

                                <!-- Progress Status Badge -->
                                @if($course['progress'] == 100)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                        ✅ Selesai
                                    </span>
                                @elseif($course['progress'] >= 50)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-2">
                                        🔥 Hampir Selesai
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                                        📖 Sedang Belajar
                                    </span>
                                @endif
                            </div>
                            <div class="text-right ml-4">
                                <span class="text-2xl font-bold text-blue-600">{{ $course['progress'] }}%</span>
                                <p class="text-sm text-gray-600">{{ $course['completed_modules'] }}/{{ $course['total_modules'] }} materi</p>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span>Progress Pembelajaran</span>
                                <span>{{ $course['progress'] }}% selesai</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2.5 rounded-full transition-all duration-500" style="width: {{ $course['progress'] }}%"></div>
                            </div>
                        </div>

                        <!-- Course Stats -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $course['time_spent'] }}
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ $course['last_accessed'] }}
                                </div>
                            </div>
                            <a href="{{ route('course.learn', $course['slug']) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                @if($course['progress'] == 100)
                                    📖 Review Materi
                                @else
                                    ▶️ Lanjutkan Belajar
                                @endif
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <!-- No Active Courses -->
        <div class="bg-white rounded-xl shadow-sm p-8 mb-8 border border-gray-100">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Kursus Aktif</h3>
                <p class="text-gray-600 mb-6">Mulai perjalanan belajar Anda dengan memilih kursus yang sesuai dengan tujuan karir</p>
                <a href="{{ route('user.courses') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                    🚀 Jelajahi Kursus
                </a>
            </div>
        </div>
    @endif

    <!-- Learning Roadmap -->
    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl shadow-sm p-6 mb-8 border border-green-100">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-xl font-bold text-gray-900">🗺️ Roadmap Pembelajaran</h2>
                <p class="text-sm text-gray-600">Urutan kursus yang Anda ikuti berdasarkan tingkat kesulitan</p>
            </div>
            @if(count($progressData['learning_path']) > 0)
                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    {{ count(array_filter($progressData['learning_path'], fn($step) => $step['completed'])) }}/{{ count($progressData['learning_path']) }} Selesai
                </span>
            @endif
        </div>

        @if(count($progressData['learning_path']) > 0)
            <div class="space-y-6">
                @foreach($progressData['learning_path'] as $index => $step)
                    <div class="relative">
                        <div class="flex items-start">
                            <!-- Step Number/Status -->
                            <div class="flex-shrink-0 w-10 h-10 rounded-full {{ $step['completed'] ? 'bg-green-500' : ($step['current'] ? 'bg-blue-500' : 'bg-gray-300') }} flex items-center justify-center shadow-sm">
                                @if($step['completed'])
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <span class="text-white text-sm font-bold">{{ $index + 1 }}</span>
                                @endif
                            </div>

                            <!-- Content -->
                            <div class="ml-4 flex-1 bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold {{ $step['completed'] ? 'text-green-700' : ($step['current'] ? 'text-blue-700' : 'text-gray-900') }}">
                                            {{ $step['title'] }}
                                        </h3>
                                        <p class="text-gray-600 text-sm mt-1">{{ $step['description'] }}</p>

                                        <!-- Status Badge -->
                                        <div class="mt-3">
                                            @if($step['completed'])
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    ✅ Selesai ({{ $step['progress'] }}%)
                                                </span>
                                            @elseif($step['current'])
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    🔄 Sedang Berjalan ({{ $step['progress'] }}%)
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    ⏳ Belum Dimulai
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Progress Circle -->
                                    <div class="ml-4">
                                        <div class="relative w-12 h-12">
                                            <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 100 100">
                                                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                                                <circle cx="50" cy="50" r="40" stroke="{{ $step['completed'] ? '#10b981' : ($step['current'] ? '#3b82f6' : '#9ca3af') }}" stroke-width="8" fill="none"
                                                        stroke-dasharray="251.2" stroke-dashoffset="{{ 251.2 - (251.2 * $step['progress'] / 100) }}"
                                                        stroke-linecap="round"/>
                                            </svg>
                                            <div class="absolute inset-0 flex items-center justify-center">
                                                <span class="text-xs font-bold text-gray-700">{{ $step['progress'] }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Connecting Line -->
                        @if($index < count($progressData['learning_path']) - 1)
                            <div class="absolute left-5 top-10 w-0.5 h-6 {{ $step['completed'] ? 'bg-green-400' : 'bg-gray-300' }}"></div>
                        @endif
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 9m0 8V9m0 0V7"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Roadmap Pembelajaran Belum Tersedia</h3>
                <p class="text-gray-600 mb-6">Mulai mengikuti kursus untuk melihat roadmap pembelajaran yang dipersonalisasi berdasarkan progress Anda</p>
                <a href="{{ route('user.courses') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-medium rounded-lg transition-all duration-200">
                    🚀 Mulai Perjalanan Belajar
                </a>
            </div>
        @endif
    </div>

    <!-- Achievements & Motivational Section -->
    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-sm p-6 border border-yellow-100">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-xl font-bold text-gray-900">🏆 Pencapaian & Motivasi</h2>
                <p class="text-sm text-gray-600">Badge dan milestone yang telah Anda raih</p>
            </div>
        </div>

        @if($progressData['completed_modules'] > 0)
            <!-- Show some achievements based on progress -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <!-- First Lesson Achievement -->
                <div class="bg-white rounded-lg p-4 border border-gray-200 text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Langkah Pertama</h4>
                    <p class="text-xs text-gray-600">Menyelesaikan materi pertama</p>
                </div>

                @if($progressData['completed_modules'] >= 5)
                <!-- Progress Maker Achievement -->
                <div class="bg-white rounded-lg p-4 border border-gray-200 text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Progress Maker</h4>
                    <p class="text-xs text-gray-600">Menyelesaikan 5+ materi</p>
                </div>
                @endif

                @if($progressData['completed_modules'] >= 10)
                <!-- Dedicated Learner Achievement -->
                <div class="bg-white rounded-lg p-4 border border-gray-200 text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Dedicated Learner</h4>
                    <p class="text-xs text-gray-600">Menyelesaikan 10+ materi</p>
                </div>
                @endif
            </div>

            <!-- Motivational Message -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-lg">🎯</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900">Terus Semangat!</h4>
                        <p class="text-sm text-gray-600">
                            @if($progressData['overall_progress'] < 25)
                                Anda sudah memulai perjalanan yang luar biasa! Terus konsisten belajar setiap hari.
                            @elseif($progressData['overall_progress'] < 50)
                                Progress yang bagus! Anda sudah menyelesaikan {{ $progressData['overall_progress'] }}% dari pembelajaran.
                            @elseif($progressData['overall_progress'] < 75)
                                Luar biasa! Anda sudah lebih dari setengah jalan. Jangan berhenti sekarang!
                            @else
                                Hampir selesai! Anda tinggal sedikit lagi untuk menyelesaikan semua materi.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        @else
            <!-- No achievements yet -->
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Siap Meraih Pencapaian Pertama?</h3>
                <p class="text-gray-600 mb-6">Mulai belajar untuk mendapatkan badge dan pencapaian pertama Anda! Setiap langkah kecil adalah kemajuan yang berarti.</p>
                <a href="{{ route('user.courses') }}" class="inline-flex items-center px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-white font-medium rounded-lg transition-colors duration-200">
                    🌟 Mulai Perjalanan Belajar
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
