<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Ujian - Ngambiskuy')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    <style>
        /* Custom styles for exam interface */
        .exam-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .question-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .option-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .option-card.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
        }

        .option-card.selected .option-letter {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            transform: scale(1.1);
        }
        
        .timer-warning {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
        
        /* Question navigation styles */
        .question-nav-btn {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .question-nav-btn.answered {
            background-color: #10b981;
            color: white;
            border: 2px solid #10b981;
        }
        
        .question-nav-btn.current,
        .question-nav-btn.viewing {
            background-color: #3b82f6 !important;
            color: white !important;
            border: 2px solid #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
        }
        
        .question-nav-btn.unanswered {
            background-color: #f3f4f6;
            color: #6b7280;
            border: 2px solid #d1d5db;
        }
        
        .question-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* Auto-save indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 18px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .auto-save-indicator.saving {
            background: linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9));
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .auto-save-indicator.saved {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
            color: white;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .auto-save-indicator.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
            color: white;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth page transitions */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .question-nav-btn {
                width: 35px;
                height: 35px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="font-sans antialiased">
    <div class="exam-container">
        @yield('content')
    </div>

    <!-- Auto-save indicator -->
    <div id="autoSaveIndicator" class="auto-save-indicator" style="display: none;">
        <span id="autoSaveText">Menyimpan...</span>
    </div>

    <!-- Scripts -->
    <script>
        // Global exam utilities
        window.ExamUtils = {
            // Auto-save functionality
            autoSave: function(examId, questionId, data) {
                return new Promise((resolve, reject) => {
                    const indicator = document.getElementById('autoSaveIndicator');
                    const text = document.getElementById('autoSaveText');
                    
                    // Show saving indicator
                    indicator.className = 'auto-save-indicator saving';
                    indicator.style.display = 'block';
                    text.innerHTML = '<span class="loading-spinner mr-2"></span>Menyimpan...';
                    
                    fetch(`/exams/${examId}/auto-save`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            question_id: questionId,
                            ...data
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success indicator
                            indicator.className = 'auto-save-indicator saved';
                            text.textContent = 'Tersimpan';
                            
                            // Hide after 2 seconds
                            setTimeout(() => {
                                indicator.style.display = 'none';
                            }, 2000);
                            
                            resolve(data);
                        } else {
                            throw new Error(data.message || 'Failed to save');
                        }
                    })
                    .catch(error => {
                        // Show error indicator
                        indicator.className = 'auto-save-indicator error';
                        text.textContent = 'Gagal menyimpan';
                        
                        // Hide after 3 seconds
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 3000);
                        
                        console.error('Auto-save error:', error);
                        reject(error);
                    });
                });
            },
            
            // Timer utilities
            formatTime: function(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            },
            
            // Progress calculation
            calculateProgress: function(answered, total) {
                return total > 0 ? Math.round((answered / total) * 100) : 0;
            },
            
            // Question navigation
            updateQuestionStatus: function(questionIndex, isAnswered) {
                // Update current question status
                const btn = document.querySelector(`[data-question="${questionIndex}"]`);
                if (btn) {
                    btn.classList.remove('answered', 'unanswered');
                    btn.classList.add(isAnswered ? 'answered' : 'unanswered');
                }

                // Ensure current question maintains its blue color
                document.querySelectorAll('.question-nav-btn').forEach(button => {
                    button.classList.remove('current', 'viewing');
                });

                const currentBtn = document.querySelector(`[data-question="${questionIndex}"]`);
                if (currentBtn) {
                    currentBtn.classList.add('current', 'viewing');
                }
            },
            
            // Confirmation dialogs
            confirmSubmit: function(unansweredCount) {
                return new Promise((resolve) => {
                    const modal = ExamUtils.createSubmitModal(unansweredCount);
                    document.body.appendChild(modal);

                    // Handle confirm button
                    modal.querySelector('.confirm-submit').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(true);
                    };

                    // Handle cancel button
                    modal.querySelector('.cancel-submit').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(false);
                    };

                    // Handle backdrop click
                    modal.onclick = (e) => {
                        if (e.target === modal) {
                            document.body.removeChild(modal);
                            resolve(false);
                        }
                    };
                });
            },

            // Create custom submit modal
            createSubmitModal: function(unansweredCount) {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm';

                const content = unansweredCount > 0
                    ? `
                        <div class="bg-white rounded-2xl p-8 max-w-md mx-4 shadow-2xl transform transition-all">
                            <div class="text-center">
                                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-6">
                                    <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Peringatan!</h3>
                                <p class="text-gray-600 mb-2">Anda masih memiliki</p>
                                <p class="text-3xl font-bold text-yellow-600 mb-2">${unansweredCount}</p>
                                <p class="text-gray-600 mb-6">soal yang belum dijawab</p>
                                <p class="text-sm text-gray-500 mb-8">Apakah Anda yakin ingin menyelesaikan ujian sekarang?</p>
                                <div class="flex space-x-4">
                                    <button class="cancel-submit flex-1 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200">
                                        Batal
                                    </button>
                                    <button class="confirm-submit flex-1 px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white rounded-xl font-medium transition-all duration-200">
                                        Ya, Selesaikan
                                    </button>
                                </div>
                            </div>
                        </div>
                    `
                    : `
                        <div class="bg-white rounded-2xl p-8 max-w-md mx-4 shadow-2xl transform transition-all">
                            <div class="text-center">
                                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Selesaikan Ujian</h3>
                                <p class="text-gray-600 mb-6">Semua soal telah dijawab! Apakah Anda yakin ingin menyelesaikan ujian?</p>
                                <p class="text-sm text-gray-500 mb-8">Anda tidak dapat mengubah jawaban setelah ini.</p>
                                <div class="flex space-x-4">
                                    <button class="cancel-submit flex-1 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200">
                                        Batal
                                    </button>
                                    <button class="confirm-submit flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-xl font-medium transition-all duration-200">
                                        Ya, Selesaikan
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                modal.innerHTML = content;
                return modal;
            }
        };
        
        // Prevent accidental page refresh (only for external navigation)
        let isInternalNavigation = false;

        window.addEventListener('beforeunload', function(e) {
            // Only show warning for external navigation (refresh, close tab, etc.)
            if (!isInternalNavigation) {
                e.preventDefault();
                e.returnValue = 'Anda yakin ingin meninggalkan halaman? Progress ujian mungkin akan hilang.';
            }
        });

        // Mark internal navigation
        window.markInternalNavigation = function() {
            isInternalNavigation = true;
            setTimeout(() => {
                isInternalNavigation = false;
            }, 1000);
        };
        
        // Disable right-click context menu during exam
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Disable certain keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U, etc.
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>
