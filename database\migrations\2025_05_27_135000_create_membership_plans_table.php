<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_plans', function (Blueprint $table) {
            $table->uuid('id')->primary();

            // Plan Information
            $table->string('name'); // Free, Basic, Standard, Pro
            $table->string('slug')->unique(); // free, basic, standard, pro
            $table->text('description')->nullable();
            $table->enum('type', ['individual', 'team'])->default('individual');

            // Pricing
            $table->decimal('price', 10, 2)->default(0); // Monthly price in IDR
            $table->decimal('price_per_user', 10, 2)->nullable(); // For team plans
            $table->integer('duration_months')->default(1); // Subscription duration
            $table->boolean('is_free')->default(false);

            // NALA Features
            $table->integer('nala_prompts')->nullable(); // Daily prompts limit (null = unlimited)
            $table->boolean('has_unlimited_nala')->default(false);
            $table->boolean('has_ice_full')->default(false); // Intelligent Course Engine
            $table->boolean('has_ai_teaching_assistants_courses')->default(false);
            $table->boolean('has_ai_teaching_assistants_tryout')->default(false);
            $table->boolean('has_free_certifications')->default(false);
            $table->boolean('has_blog_access')->default(false); // Blog Access & Creation
            $table->enum('career_path_predictor', ['none', 'basic', 'enhanced', 'enhanced_with_job_board'])->default('none');
            $table->boolean('has_priority_support')->default(false);

            // Team Plan Settings
            $table->boolean('is_team_plan')->default(false);
            $table->integer('minimum_users')->default(1);
            $table->integer('maximum_users')->nullable();

            // Display and Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->string('color')->default('#3B82F6'); // Plan color for UI
            $table->json('features_list')->nullable(); // Additional features for display

            $table->timestamps();

            // Indexes
            $table->index('slug');
            $table->index('type');
            $table->index('is_active');
            $table->index('is_featured');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_plans');
    }
};
