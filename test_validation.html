<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6">Price Validation Test</h1>
        
        <form id="testForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Course Type *</label>
                <div class="space-y-2" data-validation="required" data-validation-group="course_type">
                    <label class="flex items-center">
                        <input type="radio" name="course_type" value="free" class="h-4 w-4 text-emerald-600">
                        <span class="ml-2 text-sm">Free</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="course_type" value="paid" class="h-4 w-4 text-emerald-600">
                        <span class="ml-2 text-sm">Paid</span>
                    </label>
                </div>
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <div class="mb-4">
                <label for="course_price" class="block text-sm font-medium text-gray-700 mb-2">Price (IDR) *</label>
                <input type="number" id="course_price" name="course_price" 
                       placeholder="30000" min="30000"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500"
                       data-validation="required_if:course_type,paid|min:30000|numeric">
                <div class="validation-message mt-1 text-sm hidden"></div>
                <p class="text-sm text-gray-600 mt-1">Minimum: IDR 30,000</p>
            </div>

            <button type="submit" class="w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700">
                Test Validation
            </button>
        </form>

        <div class="mt-6 p-4 bg-blue-50 rounded-md">
            <h3 class="font-medium text-blue-900">Test Cases:</h3>
            <ul class="text-sm text-blue-800 mt-2 space-y-1">
                <li>1. Select "Paid" and enter 123123 (should show green)</li>
                <li>2. Select "Paid" and enter 25000 (should show red)</li>
                <li>3. Select "Free" (price field should be ignored)</li>
            </ul>
        </div>
    </div>

    <script>
        // Simplified validation system for testing
        class FormValidator {
            constructor(formId) {
                this.form = document.getElementById(formId);
                this.validationRules = {
                    required: (value) => value.trim() !== '',
                    min: (value, param, field) => {
                        if (field && field.type === 'number') {
                            const numValue = parseFloat(value);
                            const minValue = parseFloat(param);
                            return !isNaN(numValue) && numValue >= minValue;
                        }
                        return value.length >= parseInt(param);
                    },
                    numeric: (value) => !isNaN(value) && value !== '',
                    required_if: (value, param, formData) => {
                        const [field, expectedValue] = param.split(',');
                        const fieldValue = formData.get(field);
                        return fieldValue !== expectedValue || value.trim() !== '';
                    }
                };
                this.init();
            }

            init() {
                const fields = this.form.querySelectorAll('[data-validation]');
                fields.forEach(field => {
                    field.addEventListener('input', () => this.validateField(field));
                    field.addEventListener('change', () => this.validateField(field));
                });

                // Radio button handling
                const radios = this.form.querySelectorAll('input[type="radio"]');
                radios.forEach(radio => {
                    radio.addEventListener('change', () => {
                        const priceField = document.getElementById('course_price');
                        if (priceField) {
                            setTimeout(() => this.validateField(priceField), 100);
                        }
                    });
                });
            }

            validateField(field) {
                const rules = field.dataset.validation.split('|');
                const value = field.value;
                const formData = new FormData(this.form);

                let isValid = true;
                let errorMessage = '';

                for (const rule of rules) {
                    const [ruleName, param] = rule.split(':');

                    if (!this.validationRules[ruleName]) continue;

                    let ruleValid;
                    if (ruleName === 'required_if') {
                        ruleValid = this.validationRules[ruleName](value, param, formData);
                    } else {
                        ruleValid = this.validationRules[ruleName](value, param, field);
                    }

                    if (!ruleValid) {
                        isValid = false;
                        errorMessage = this.getErrorMessage(ruleName, param, field);
                        break;
                    }
                }

                this.updateFieldUI(field, isValid, errorMessage);
                return isValid;
            }

            getErrorMessage(ruleName, param, field) {
                if (field.id === 'course_price') {
                    if (ruleName === 'required_if') return 'Price is required for paid courses';
                    if (ruleName === 'min') return 'Minimum price is IDR 30,000';
                    if (ruleName === 'numeric') return 'Price must be a number';
                }
                return 'Invalid input';
            }

            updateFieldUI(field, isValid, errorMessage) {
                field.classList.remove('border-red-500', 'border-green-500', 'bg-red-50', 'bg-green-50');
                
                if (field.value.trim() !== '') {
                    if (isValid) {
                        field.classList.add('border-green-500', 'bg-green-50');
                    } else {
                        field.classList.add('border-red-500', 'bg-red-50');
                    }
                }

                const container = field.closest('div');
                const messageEl = container.querySelector('.validation-message');
                if (messageEl) {
                    if (errorMessage) {
                        messageEl.textContent = errorMessage;
                        messageEl.classList.remove('hidden');
                        messageEl.className = 'validation-message mt-1 text-sm text-red-600';
                    } else {
                        messageEl.classList.add('hidden');
                    }
                }
            }
        }

        // Initialize validator
        document.addEventListener('DOMContentLoaded', function() {
            window.validator = new FormValidator('testForm');
        });

        // Form submission
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form validation test completed!');
        });
    </script>
</body>
</html>
