@extends('layouts.tutor')

@section('title', 'Buat Kursus Baru - Tutor Dashboard')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/validation-styles.css') }}">
<style>
    /* Enhanced validation styling with highest specificity */
    input.validation-field.border-red-500,
    textarea.validation-field.border-red-500,
    select.validation-field.border-red-500 {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
    }

    input.validation-field.border-green-500,
    textarea.validation-field.border-green-500,
    select.validation-field.border-green-500 {
        border-color: #10b981 !important;
        background-color: #f0fdf4 !important;
    }

    /* Override any conflicting Tailwind classes */
    .border-red-500.bg-red-50 {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
    }

    .border-green-500.bg-green-50 {
        border-color: #10b981 !important;
        background-color: #f0fdf4 !important;
    }

    /* Force override for input elements specifically */
    input[data-validation].border-red-500 {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
    }

    input[data-validation].border-green-500 {
        border-color: #10b981 !important;
        background-color: #f0fdf4 !important;
    }

    textarea[data-validation].border-red-500 {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
    }

    textarea[data-validation].border-green-500 {
        border-color: #10b981 !important;
        background-color: #f0fdf4 !important;
    }
</style>
@endpush

@section('content')
<div class="p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {{ session('error') }}
        </div>
    @endif

    @if($errors->any())
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <h4 class="font-medium">Ada kesalahan dalam form:</h4>
            <ul class="mt-2 list-disc list-inside">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold text-gray-900">Buat Kursus Baru</h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Course Creator
                    </span>
                </div>
                <p class="text-gray-600 mt-1">Bagikan pengetahuan Anda dengan siswa di seluruh Indonesia</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('tutor.courses') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kursus
                </a>
            </div>
        </div>
    </div>

    <!-- Nala Course Builder Option -->
    <div class="bg-gradient-to-r from-purple-600 via-blue-600 to-emerald-600 rounded-xl p-6 mb-6 text-white border border-purple-200 shadow-xl relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
            <div class="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12"></div>
        </div>

        <div class="relative flex items-center justify-between">
            <div class="flex items-start space-x-4">
                <!-- Nala Avatar -->
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full p-2 border border-white/30">
                        <img src="{{ asset('images/nala.png') }}" alt="Nala AI" class="w-full h-full object-cover rounded-full">
                    </div>
                </div>

                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <h2 class="text-xl font-bold">✨ Nala Course Builder</h2>
                        <span class="bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium border border-white/30">
                            AI Powered
                        </span>
                    </div>
                    <p class="text-white/90 mb-4 max-w-md">Biarkan Nala, AI assistant cerdas kami, membantu Anda membuat kurikulum yang terstruktur dan menarik dalam hitungan menit!</p>
                    <ul class="text-white/90 text-sm space-y-1">
                        <li class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Otomatis membuat outline kursus</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Saran materi dan quiz</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Estimasi durasi pembelajaran</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Rekomendasi harga optimal</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="text-center flex-shrink-0">
                <button id="ai-course-builder-btn" class="bg-white/95 backdrop-blur-sm text-purple-600 px-6 py-3 rounded-xl font-semibold hover:bg-white hover:scale-105 transition-all duration-200 shadow-lg border border-white/50">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span id="ai-builder-text">Gunakan Nala</span>
                    </div>
                </button>
                <p class="text-white/80 text-xs mt-2 font-medium">⚡ Hemat 80% waktu pembuatan</p>
            </div>
        </div>
    </div>

    <!-- Manual Course Creation Form -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">Atau Buat Manual</h2>
                <span class="text-sm text-gray-500">Langkah 1 dari 4</span>
            </div>

            <form action="{{ route('tutor.store-course') }}" method="POST" enctype="multipart/form-data" class="space-y-8" id="courseForm">
                @csrf
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Dasar</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="course_title" class="block text-sm font-medium text-gray-700 mb-2">Judul Kursus *</label>
                            <div class="relative">
                                <input type="text" id="course_title" name="course_title" value="{{ old('course_title') }}"
                                       placeholder="Contoh: Belajar React.js untuk Pemula"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_title') border-red-500 @enderror"
                                       data-validation="required|min:10|max:255">
                                <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="course_category" class="block text-sm font-medium text-gray-700 mb-2">Kategori *</label>
                            <div class="relative">
                                <select id="course_category" name="course_category"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_category') border-red-500 @enderror"
                                        data-validation="required">
                                    <option value="">Pilih kategori</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('course_category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="validation-icon absolute right-10 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Course Description -->
                <div>
                    <label for="course_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Kursus *</label>
                    <div class="relative">
                        <textarea id="course_description" name="course_description" rows="4"
                                  placeholder="Jelaskan apa yang akan dipelajari siswa dalam kursus ini..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_description') border-red-500 @enderror"
                                  data-validation="required|min:50|max:1000">{{ old('course_description') }}</textarea>
                        <div class="validation-icon absolute right-3 top-3 hidden">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <div class="validation-message text-sm hidden"></div>
                        <div class="text-xs text-gray-500">
                            <span id="description-count">0</span>/1000 karakter (minimum 50)
                        </div>
                    </div>
                    @error('course_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course Details -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Detail Kursus</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="course_level" class="block text-sm font-medium text-gray-700 mb-2">Level *</label>
                            <div class="relative">
                                <select id="course_level" name="course_level"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_level') border-red-500 @enderror"
                                        data-validation="required">
                                    <option value="">Pilih level</option>
                                    <option value="beginner" {{ old('course_level') == 'beginner' ? 'selected' : '' }}>Pemula</option>
                                    <option value="intermediate" {{ old('course_level') == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                                    <option value="advanced" {{ old('course_level') == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                                </select>
                                <div class="validation-icon absolute right-10 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_level')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="course_duration" class="block text-sm font-medium text-gray-700 mb-2">Estimasi Durasi (Jam) *</label>
                            <div class="relative">
                                <input type="number" id="course_duration" name="course_duration" value="{{ old('course_duration') }}"
                                       placeholder="20"
                                       min="1" max="500" step="1"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_duration') border-red-500 @enderror"
                                       data-validation="required|numeric|min:1|max:500">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <span class="text-gray-500 text-sm">jam</span>
                                </div>
                                <div class="validation-icon absolute right-12 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <!-- Inline validation message like blog system -->
                            <div id="course_duration_validation" class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_duration')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Kursus *</label>
                            <div class="space-y-3" data-validation="required" data-validation-group="course_type">
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="free" checked
                                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Gratis</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="paid"
                                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Berbayar</span>
                                </label>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            @error('course_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div id="price-field" class="hidden">
                            <label for="course_price" class="block text-sm font-medium text-gray-700 mb-2">Harga Kursus (IDR) *</label>
                            <div class="relative">
                                <input type="number" id="course_price" name="course_price" value="{{ old('course_price', 0) }}"
                                       placeholder="30000" min="30000" step="1000"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent @error('course_price') border-red-500 @enderror"
                                       data-validation="required_if:course_type,paid|min:30000|numeric|step:1000">
                                <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="validation-message mt-1 text-sm hidden"></div>
                            <p class="mt-1 text-xs text-gray-500">
                                Harga minimum IDR 30.000 dan harus kelipatan 1000. Platform fee 5%, tutor mendapat 60% (tanpa referral) atau 80% (dengan referral).
                            </p>
                            @error('course_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Tujuan Pembelajaran</h3>
                    <div id="learning-outcomes-container">
                        @if(old('learning_outcomes'))
                            @foreach(old('learning_outcomes') as $index => $outcome)
                                <div class="learning-outcome-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="learning_outcomes[]" value="{{ $outcome }}"
                                               placeholder="Contoh: Memahami konsep dasar React.js"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="learning-outcome-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="learning_outcomes[]" value=""
                                           placeholder="Contoh: Memahami konsep dasar React.js"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addLearningOutcome()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Tujuan Pembelajaran
                    </button>
                    @error('learning_outcomes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Requirements -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Prasyarat</h3>
                    <div id="requirements-container">
                        @if(old('requirements'))
                            @foreach(old('requirements') as $index => $requirement)
                                <div class="requirement-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="requirements[]" value="{{ $requirement }}"
                                               placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="requirement-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="requirements[]" value=""
                                           placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addRequirement()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Prasyarat
                    </button>
                    @error('requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Target Audience -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Target Audience</h3>
                    <div id="target-audience-container">
                        @if(old('target_audience'))
                            @foreach(old('target_audience') as $index => $audience)
                                <div class="target-audience-item flex items-center space-x-3 mb-3">
                                    <div class="flex-1">
                                        <input type="text" name="target_audience[]" value="{{ $audience }}"
                                               placeholder="Contoh: Pemula yang ingin belajar web development"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    </div>
                                    <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="target-audience-item flex items-center space-x-3 mb-3">
                                <div class="flex-1">
                                    <input type="text" name="target_audience[]" value=""
                                           placeholder="Contoh: Pemula yang ingin belajar web development"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                </div>
                                <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addTargetAudience()" class="mt-3 btn border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Target Audience
                    </button>
                    @error('target_audience')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course Thumbnail -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Kursus</label>
                    <div id="thumbnail-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-emerald-500 transition-colors @error('thumbnail') border-red-500 @enderror">
                        <div id="thumbnail-placeholder">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <label for="thumbnail" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">
                                        Upload thumbnail kursus
                                    </span>
                                    <span class="mt-1 block text-sm text-gray-500">
                                        PNG, JPG, JPEG hingga 10MB
                                    </span>
                                </label>
                            </div>
                        </div>
                        <div id="thumbnail-preview" class="hidden">
                            <img id="preview-image" src="" alt="Preview" class="mx-auto max-h-48 rounded-lg">
                            <div class="mt-4">
                                <p id="file-name" class="text-sm font-medium text-gray-900"></p>
                                <button type="button" onclick="removeThumbnail()" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                    Hapus gambar
                                </button>
                            </div>
                        </div>
                        <input id="thumbnail" name="thumbnail" type="file" class="sr-only" accept="image/jpeg,image/png,image/jpg">
                    </div>
                    @error('thumbnail')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6 border-t border-gray-200">
                    <button type="button" class="btn bg-gray-500 text-white hover:bg-gray-600 border-gray-500">
                        Simpan sebagai Draft
                    </button>
                    <button type="submit" class="btn bg-emerald-600 text-white hover:bg-emerald-700">
                        Lanjut ke Kurikulum
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </form>
    </div>

    <!-- Tips Section -->
    <div class="bg-teal-50 rounded-lg p-6 mt-6 border border-teal-200">
        <h3 class="text-lg font-semibold text-teal-900 mb-3">💡 Tips Membuat Kursus yang Menarik</h3>
        <ul class="text-teal-800 space-y-2 text-sm">
            <li>• Gunakan judul yang jelas dan spesifik</li>
            <li>• Jelaskan manfaat konkret yang akan didapat siswa</li>
            <li>• Tentukan target audience yang spesifik</li>
            <li>• Gunakan thumbnail yang menarik dan profesional</li>
            <li>• Buat tujuan pembelajaran yang terukur</li>
        </ul>
    </div>

    <!-- Revenue Info Section -->
    <div class="bg-emerald-50 rounded-lg p-6 mt-6 border border-emerald-200">
        <h3 class="text-lg font-semibold text-emerald-900 mb-3">💰 Informasi Revenue & Referral</h3>
        <div class="text-emerald-800 space-y-2 text-sm">
            {{-- <p><strong>Revenue Share:</strong></p>
            <ul class="ml-4 space-y-1">
                <li>• Tanpa kode referral: Tutor mendapat 60% dari harga kursus</li>
                <li>• Dengan kode referral: Tutor mendapat 80% dari harga kursus</li>
                <li>• Platform fee: 5% dari setiap penjualan</li>
            </ul> --}}
            <p class="mt-3"><strong>Setelah kursus dipublish:</strong></p>
            <ul class="ml-4 space-y-1">
                <li>• Anda akan mendapat link referral khusus untuk kursus ini</li>
                <li>• Bagikan link referral untuk mendapat revenue share 80%</li>
                <li>• Link referral hanya berlaku untuk pembelian kursus (bukan membership NALA)</li>
            </ul>
        </div>
    </div>
</div>

<script>
// Real-time validation system
class FormValidator {
    constructor(formId) {
        this.form = document.getElementById(formId);
        this.validationRules = {
            required: (value) => value.trim() !== '',
            min: (value, param, field) => {
                if (field && field.type === 'number') {
                    const numValue = parseFloat(value);
                    const minValue = parseFloat(param);
                    return !isNaN(numValue) && numValue >= minValue;
                }
                return value.length >= parseInt(param);
            },
            max: (value, param, field) => {
                if (field && field.type === 'number') {
                    return !isNaN(value) && parseFloat(value) <= parseFloat(param);
                }
                return value.length <= parseInt(param);
            },
            numeric: (value) => !isNaN(value) && value !== '',
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            url: (value) => /^https?:\/\/.+/.test(value),
            required_if: (value, param, formData) => {
                const [field, expectedValue] = param.split(',');
                const fieldValue = formData.get(field);
                return fieldValue !== expectedValue || value.trim() !== '';
            },
            step: (value, param) => {
                if (!value || isNaN(value)) return true; // Let numeric rule handle this
                const numValue = parseFloat(value);
                const stepValue = parseFloat(param);
                return numValue % stepValue === 0;
            }
        };
        this.init();
    }

    init() {
        // Add event listeners to all form fields with validation
        const fields = this.form.querySelectorAll('[data-validation]');
        fields.forEach(field => {
            this.addFieldListeners(field);
        });

        // Add listeners for radio button groups
        const radioGroups = this.form.querySelectorAll('[data-validation-group]');
        radioGroups.forEach(group => {
            const radios = group.querySelectorAll('input[type="radio"]');
            radios.forEach(radio => {
                radio.addEventListener('change', () => this.validateRadioGroup(group));
            });
        });

        // Character counter for description
        const descriptionField = document.getElementById('course_description');
        if (descriptionField) {
            descriptionField.addEventListener('input', this.updateCharacterCount.bind(this));
            this.updateCharacterCount(); // Initial count
        }

        // Price field validation when course type changes
        const courseTypeRadios = this.form.querySelectorAll('input[name="course_type"]');
        const priceField = document.getElementById('course_price');
        if (courseTypeRadios.length && priceField) {
            courseTypeRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    setTimeout(() => this.validateField(priceField), 100);
                });
            });
        }
    }

    addFieldListeners(field) {
        const events = ['input', 'blur', 'change'];
        events.forEach(event => {
            field.addEventListener(event, () => {
                setTimeout(() => this.validateField(field), 100);
            });
        });
    }

    validateField(field) {
        const rules = field.dataset.validation.split('|');
        const value = field.value;
        const formData = new FormData(this.form);

        let isValid = true;
        let errorMessage = '';

        // Special handling for price field - show requirements when empty and course is paid
        if (field.id === 'course_price') {
            const courseType = formData.get('course_type');
            if (courseType === 'paid' && value.trim() === '') {
                isValid = false;
                errorMessage = 'Harga kursus wajib diisi untuk kursus berbayar';
                this.updateFieldUI(field, isValid, errorMessage);
                return isValid;
            }
        }

        for (const rule of rules) {
            const [ruleName, param] = rule.split(':');

            if (!this.validationRules[ruleName]) continue;

            // Pass the correct third parameter based on rule type
            let ruleValid;
            if (ruleName === 'required_if') {
                ruleValid = this.validationRules[ruleName](value, param, formData);
            } else {
                ruleValid = this.validationRules[ruleName](value, param, field);
            }

            if (!ruleValid) {
                isValid = false;
                errorMessage = this.getErrorMessage(ruleName, param, field);
                break;
            }
        }

        this.updateFieldUI(field, isValid, errorMessage);
        return isValid;
    }

    validateRadioGroup(group) {
        const radios = group.querySelectorAll('input[type="radio"]');
        const isChecked = Array.from(radios).some(radio => radio.checked);

        this.updateGroupUI(group, isChecked, isChecked ? '' : 'Pilih salah satu opsi');
        return isChecked;
    }

    getErrorMessage(ruleName, param, field) {
        const fieldName = field.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || 'Field';

        // Special handling for price field
        if (field.id === 'course_price') {
            if (ruleName === 'required_if') {
                return 'Harga kursus wajib diisi untuk kursus berbayar';
            }
            if (ruleName === 'min') {
                return 'Harga minimum IDR 30.000';
            }
            if (ruleName === 'numeric') {
                return 'Harga harus berupa angka';
            }
            if (ruleName === 'step') {
                return 'Harga harus kelipatan 1000';
            }
        }

        const messages = {
            required: `${fieldName} wajib diisi`,
            min: field.type === 'number' ? `${fieldName} minimal ${param}` : `${fieldName} minimal ${param} karakter`,
            max: field.type === 'number' ? `${fieldName} maksimal ${param}` : `${fieldName} maksimal ${param} karakter`,
            numeric: `${fieldName} harus berupa angka`,
            email: `Format email tidak valid`,
            url: `Format URL tidak valid`,
            required_if: `${fieldName} wajib diisi`
        };

        // Special cases with more detailed explanations
        if (ruleName === 'min' && field.type === 'number') {
            if (field.id === 'course_price') {
                return `${fieldName} minimal ${this.formatCurrency(param)}`;
            } else if (field.id === 'course_duration') {
                return `${fieldName} minimal ${param} jam`;
            } else {
                return `${fieldName} minimal ${param}`;
            }
        }

        if (ruleName === 'min' && field.id === 'course_title') {
            return `Judul kursus terlalu pendek. Minimal ${param} karakter untuk judul yang informatif.`;
        }

        if (ruleName === 'min' && field.id === 'course_description') {
            return `Deskripsi terlalu singkat. Minimal ${param} karakter agar siswa memahami isi kursus.`;
        }

        return messages[ruleName] || `${fieldName} tidak valid`;
    }

    getSuccessMessage(field) {
        const fieldId = field.id;
        const successMessages = {
            'course_title': 'Judul kursus sudah sesuai',
            'course_description': 'Deskripsi kursus sudah lengkap',
            'course_price': 'Harga sudah sesuai ketentuan',
            'course_category': 'Kategori sudah dipilih',
            'course_level': 'Level sudah dipilih',
            'course_duration': 'Estimasi durasi sudah diisi'
        };

        return successMessages[fieldId] || 'Data sudah valid';
    }

    getFieldRequirements(field) {
        const rules = field.dataset.validation.split('|');
        const requirements = [];

        for (const rule of rules) {
            const [ruleName, param] = rule.split(':');

            switch (ruleName) {
                case 'required':
                    requirements.push({ text: 'Wajib diisi', rule: 'required' });
                    break;
                case 'min':
                    if (field.type === 'number') {
                        if (field.id === 'course_price') {
                            requirements.push({ text: `Minimal ${this.formatCurrency(param)}`, rule: 'min', param });
                        } else if (field.id === 'course_duration') {
                            requirements.push({ text: `Minimal ${param} jam`, rule: 'min', param });
                        } else {
                            requirements.push({ text: `Minimal ${param}`, rule: 'min', param });
                        }
                    } else {
                        requirements.push({ text: `Minimal ${param} karakter`, rule: 'min', param });
                    }
                    break;
                case 'max':
                    requirements.push({ text: `Maksimal ${param} karakter`, rule: 'max', param });
                    break;
                case 'numeric':
                    requirements.push({ text: 'Harus berupa angka', rule: 'numeric' });
                    break;
                case 'email':
                    requirements.push({ text: 'Format email yang valid', rule: 'email' });
                    break;
                case 'url':
                    requirements.push({ text: 'Format URL yang valid (https://...)', rule: 'url' });
                    break;
                case 'step':
                    requirements.push({ text: `Harus kelipatan ${param}`, rule: 'step', param });
                    break;
            }
        }

        return requirements;
    }

    updateFieldUI(field, isValid, errorMessage) {
        const container = field.closest('div');
        let validationMessage = container.querySelector('.validation-message');

        // Remove existing validation feedback
        const existingFeedback = container.querySelector('.validation-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Remove existing requirements
        const existingRequirements = container.querySelector('.validation-requirements');
        if (existingRequirements) {
            existingRequirements.remove();
        }

        // Update field styling
        field.classList.remove('border-red-500', 'border-green-500', 'bg-red-50', 'bg-green-50', 'validation-field');
        field.classList.add('validation-field');

        if (field.value.trim() !== '') {
            if (isValid) {
                field.classList.add('border-green-500', 'bg-green-50');
            } else {
                field.classList.add('border-red-500', 'bg-red-50');
            }
        }

        // Create enhanced validation feedback
        if (field.value.trim() !== '' || field.dataset.validation.includes('required') || field.dataset.validation.includes('required_if')) {
            this.createValidationFeedback(field, isValid, errorMessage, container);
        }

        // Show requirements on focus for important fields
        if (['course_title', 'course_description', 'course_price', 'course_duration'].includes(field.id)) {
            this.setupRequirementsDisplay(field, container);
        }
    }

    createValidationFeedback(field, isValid, errorMessage, container) {
        const feedback = document.createElement('div');
        feedback.className = 'validation-feedback';

        if (isValid && field.value.trim() !== '') {
            feedback.classList.add('success');
            feedback.innerHTML = `
                <div class="validation-feedback-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <strong>Bagus!</strong> ${this.getSuccessMessage(field)}
                </div>
            `;
        } else if (!isValid && errorMessage) {
            feedback.classList.add('error');
            feedback.innerHTML = `
                <div class="validation-feedback-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <strong>Perlu diperbaiki:</strong> ${errorMessage}
                    ${this.getHelpText(field)}
                </div>
            `;
        }

        if (feedback.innerHTML) {
            container.appendChild(feedback);
        }
    }

    setupRequirementsDisplay(field, container) {
        const showRequirements = () => {
            // Remove existing requirements
            const existingRequirements = container.querySelector('.validation-requirements');
            if (existingRequirements) {
                existingRequirements.remove();
            }

            const requirements = this.getFieldRequirements(field);
            if (requirements.length > 0) {
                const requirementsDiv = document.createElement('div');
                requirementsDiv.className = 'validation-requirements';

                const title = document.createElement('div');
                title.innerHTML = '<strong>Persyaratan:</strong>';
                requirementsDiv.appendChild(title);

                const list = document.createElement('ul');
                requirements.forEach(req => {
                    const li = document.createElement('li');
                    li.textContent = req.text;

                    // Check if requirement is met
                    const isReqMet = this.checkRequirement(field, req);
                    li.classList.add(isReqMet ? 'valid' : 'invalid');

                    list.appendChild(li);
                });

                requirementsDiv.appendChild(list);
                container.appendChild(requirementsDiv);
            }
        };

        const hideRequirements = () => {
            setTimeout(() => {
                const requirements = container.querySelector('.validation-requirements');
                if (requirements && !field.matches(':focus')) {
                    requirements.remove();
                }
            }, 200);
        };

        field.addEventListener('focus', showRequirements);
        field.addEventListener('blur', hideRequirements);
        field.addEventListener('input', () => {
            const requirements = container.querySelector('.validation-requirements');
            if (requirements) {
                showRequirements(); // Update requirements status
            }
        });
    }

    checkRequirement(field, requirement) {
        const value = field.value;

        switch (requirement.rule) {
            case 'required':
                return value.trim() !== '';
            case 'min':
                if (field.type === 'number') {
                    return !isNaN(value) && value !== '' && parseFloat(value) >= parseFloat(requirement.param);
                }
                return value.length >= parseInt(requirement.param);
            case 'max':
                if (field.type === 'number') {
                    return !isNaN(value) && value !== '' && parseFloat(value) <= parseFloat(requirement.param);
                }
                return value.length <= parseInt(requirement.param);
            case 'numeric':
                return !isNaN(value) && value !== '';
            case 'email':
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
            case 'url':
                return /^https?:\/\/.+/.test(value);
            case 'step':
                if (!value || isNaN(value)) return true;
                const numValue = parseFloat(value);
                const stepValue = parseFloat(requirement.param);
                return numValue % stepValue === 0;
            default:
                return true;
        }
    }

    getHelpText(field) {
        const helpTexts = {
            'course_title': '<br><small>💡 Tip: Gunakan judul yang jelas dan spesifik</small>',
            'course_description': '<br><small>💡 Tip: Jelaskan manfaat konkret yang akan didapat siswa</small>',
            'course_duration': '<br><small>💡 Tip: Contoh: "20 jam", "3 minggu", "2 bulan"</small>',
            'course_price': '<br><small>💡 Tip: Harga harus kelipatan 1000 dan minimal IDR 30.000</small>'
        };

        return helpTexts[field.id] || '';
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    }

    updateCharacterCount() {
        const descriptionField = document.getElementById('course_description');
        const countElement = document.getElementById('description-count');
        if (descriptionField && countElement) {
            countElement.textContent = descriptionField.value.length;
        }
    }

    updateGroupUI(group, isValid, errorMessage) {
        let validationMessage = group.querySelector('.validation-message');
        if (!validationMessage) {
            validationMessage = document.createElement('div');
            validationMessage.className = 'validation-message mt-1 text-sm';
            group.appendChild(validationMessage);
        }

        validationMessage.classList.remove('hidden', 'text-red-600', 'text-green-600');

        if (!isValid && errorMessage) {
            validationMessage.textContent = errorMessage;
            validationMessage.classList.add('text-red-600');
        } else if (isValid) {
            validationMessage.textContent = 'Pilihan sudah valid';
            validationMessage.classList.add('text-green-600');
        } else {
            validationMessage.classList.add('hidden');
        }
    }
}

// Initialize validator
window.courseValidator = new FormValidator('courseForm');

// Learning outcomes management
function addLearningOutcome() {
    const container = document.getElementById('learning-outcomes-container');
    const newItem = document.createElement('div');
    newItem.className = 'learning-outcome-item flex items-center space-x-3 mb-3';
    newItem.innerHTML = `
        <div class="flex-1">
            <input type="text" name="learning_outcomes[]" value=""
                   placeholder="Contoh: Memahami konsep dasar React.js"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
}

function removeLearningOutcome(button) {
    const container = document.getElementById('learning-outcomes-container');
    if (container.children.length > 1) {
        button.closest('.learning-outcome-item').remove();
    }
}

// Requirements management
function addRequirement() {
    const container = document.getElementById('requirements-container');
    const newItem = document.createElement('div');
    newItem.className = 'requirement-item flex items-center space-x-3 mb-3';
    newItem.innerHTML = `
        <div class="flex-1">
            <input type="text" name="requirements[]" value=""
                   placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
}

function removeRequirement(button) {
    const container = document.getElementById('requirements-container');
    if (container.children.length > 1) {
        button.closest('.requirement-item').remove();
    }
}

// Target audience management
function addTargetAudience() {
    const container = document.getElementById('target-audience-container');
    const newItem = document.createElement('div');
    newItem.className = 'target-audience-item flex items-center space-x-3 mb-3';
    newItem.innerHTML = `
        <div class="flex-1">
            <input type="text" name="target_audience[]" value=""
                   placeholder="Contoh: Pemula yang ingin belajar web development"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
}

function removeTargetAudience(button) {
    const container = document.getElementById('target-audience-container');
    if (container.children.length > 1) {
        button.closest('.target-audience-item').remove();
    }
}

// Price field toggle
function togglePriceField() {
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;
    const priceField = document.getElementById('price-field');
    const priceInput = document.getElementById('course_price');

    if (courseType === 'paid') {
        priceField.classList.remove('hidden');
        priceInput.setAttribute('required', 'required');
    } else {
        priceField.classList.add('hidden');
        priceInput.removeAttribute('required');
        priceInput.value = '0';
    }

    // Trigger validation for price field
    if (window.courseValidator) {
        setTimeout(() => window.courseValidator.validateField(priceInput), 100);
    }
}

// Thumbnail upload handling
document.getElementById('thumbnail').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung. Gunakan JPG, JPEG, atau PNG.');
            this.value = '';
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Ukuran file terlalu besar. Maksimal 10MB.');
            this.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            const placeholder = document.getElementById('thumbnail-placeholder');
            const preview = document.getElementById('thumbnail-preview');
            const previewImage = document.getElementById('preview-image');
            const fileName = document.getElementById('file-name');

            previewImage.src = e.target.result;
            fileName.textContent = file.name;

            placeholder.classList.add('hidden');
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

function removeThumbnail() {
    const thumbnailInput = document.getElementById('thumbnail');
    const placeholder = document.getElementById('thumbnail-placeholder');
    const preview = document.getElementById('thumbnail-preview');

    thumbnailInput.value = '';
    placeholder.classList.remove('hidden');
    preview.classList.add('hidden');
}

// Initialize validator when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const validator = new FormValidator('courseForm');
    // Make validator globally accessible
    window.courseValidator = validator;

    // Initialize form state - ensure price field is hidden since "Gratis" is default
    togglePriceField();

    // Add real-time validation for duration field
    const durationField = document.getElementById('course_duration');
    if (durationField) {
        durationField.addEventListener('input', function() {
            // Clear any existing validation messages for this field
            const existingError = this.parentElement.querySelector('.inline-validation-error');
            if (existingError) {
                existingError.remove();
            }
            this.classList.remove('border-red-500');
            this.classList.add('border-gray-300');

            // Show validation if field has value but is invalid
            const value = parseInt(this.value);
            if (this.value !== '' && (isNaN(value) || value < 1 || value > 500)) {
                showInlineValidationError(this, 'Field wajib diisi', 'Tip: Masukkan durasi dalam jam (1-500)');
            }
        });
    }
});



function addLearningOutcome() {
    const container = document.getElementById('learning-outcomes-container');
    const div = document.createElement('div');
    div.className = 'learning-outcome-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="learning_outcomes[]" value=""
                   placeholder="Contoh: Memahami konsep dasar React.js"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeLearningOutcome(button) {
    button.closest('.learning-outcome-item').remove();
}

function addRequirement() {
    const container = document.getElementById('requirements-container');
    const div = document.createElement('div');
    div.className = 'requirement-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="requirements[]" value=""
                   placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeRequirement(button) {
    button.closest('.requirement-item').remove();
}

function addTargetAudience() {
    const container = document.getElementById('target-audience-container');
    const div = document.createElement('div');
    div.className = 'target-audience-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <div class="flex-1">
            <input type="text" name="target_audience[]" value=""
                   placeholder="Contoh: Pemula yang ingin belajar web development"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
        </div>
        <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeTargetAudience(button) {
    button.closest('.target-audience-item').remove();
}



// Simple error display function
function showError(field, message, tip) {
    // Add red border
    field.classList.add('border-red-500');
    field.classList.remove('border-gray-300');

    // Create error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'validation-error mt-2 p-3 bg-red-50 border border-red-200 rounded-lg';
    errorDiv.innerHTML = `
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-red-800">${message}</p>
                <p class="text-xs text-red-600 mt-1">${tip}</p>
            </div>
        </div>
    `;

    // Insert after field's parent div
    field.parentElement.appendChild(errorDiv);
}



// Simple form validation
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();

    // Clear previous errors
    document.querySelectorAll('.validation-error').forEach(el => el.remove());
    document.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
        el.classList.add('border-gray-300');
    });

    let firstError = null;

    // Check title
    const title = document.getElementById('course_title');
    if (!title.value.trim() || title.value.trim().length < 10) {
        showError(title, 'Field wajib diisi', 'Tip: Minimal 10 karakter');
        if (!firstError) firstError = title;
    }

    // Check category
    const category = document.getElementById('course_category');
    if (!category.value) {
        showError(category, 'Field wajib diisi', 'Tip: Pilih kategori yang sesuai');
        if (!firstError) firstError = category;
    }

    // Check description
    const description = document.getElementById('course_description');
    if (!description.value.trim() || description.value.trim().length < 50) {
        showError(description, 'Field wajib diisi', 'Tip: Minimal 50 karakter');
        if (!firstError) firstError = description;
    }

    // Check level
    const level = document.getElementById('course_level');
    if (!level.value) {
        showError(level, 'Field wajib diisi', 'Tip: Pilih level yang sesuai');
        if (!firstError) firstError = level;
    }

    // Check duration
    const duration = document.getElementById('course_duration');
    const durationVal = parseInt(duration.value);
    if (!duration.value || isNaN(durationVal) || durationVal < 1 || durationVal > 500) {
        showError(duration, 'Field wajib diisi', 'Tip: Masukkan angka 1-500 jam');
        if (!firstError) firstError = duration;
    }

    // Check price if paid
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;
    if (courseType === 'paid') {
        const price = document.getElementById('course_price');
        const priceVal = parseInt(price.value);
        if (!price.value || isNaN(priceVal) || priceVal < 30000 || priceVal % 1000 !== 0) {
            showError(price, 'Field wajib diisi', 'Tip: Minimal IDR 30.000, kelipatan 1000');
            if (!firstError) firstError = price;
        }
    }

    // If errors, scroll to first one
    if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstError.focus();
        return;
    }

    // Clean up empty array inputs before submission
    const arrayContainers = ['learning-outcomes-container', 'requirements-container', 'target-audience-container'];
    arrayContainers.forEach(containerId => {
        const container = document.getElementById(containerId);
        const inputs = container.querySelectorAll('input');
        inputs.forEach(input => {
            if (!input.value || input.value.trim() === '') {
                input.remove();
            }
        });
    });

    // Show loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = `
        <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Menyimpan...
    `;

    // Submit the form
    this.submit();

    // Re-enable button after 10 seconds as fallback
    setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }, 10000);
});

function showValidationSummary() {
    const invalidFields = [];
    const fields = document.querySelectorAll('[data-validation]');

    fields.forEach(field => {
        if (window.courseValidator && !window.courseValidator.validateField(field)) {
            const label = field.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || field.name;
            invalidFields.push({ name: label, element: field });
        }
    });

    // Check radio groups
    const radioGroups = document.querySelectorAll('[data-validation-group]');
    radioGroups.forEach(group => {
        const radios = group.querySelectorAll('input[type="radio"]');
        const isChecked = Array.from(radios).some(radio => radio.checked);
        if (!isChecked) {
            const label = group.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || 'Radio group';
            invalidFields.push({ name: label, element: group });
        }
    });

    if (invalidFields.length > 0) {
        // Use the professional notification system
        if (window.courseValidator) {
            const message = `Form belum lengkap! ${invalidFields.length} field perlu diperbaiki. Klik untuk melihat detail.`;
            const notification = window.courseValidator.showNotification(message, 'error', 0);

            // Make notification clickable to show modal
            notification.style.cursor = 'pointer';
            notification.addEventListener('click', () => {
                showValidationModal(invalidFields);
                notification.remove();
            });
        }
    }
}

// Professional validation modal
function showValidationModal(invalidFields) {
    // Remove existing modal if any
    const existingModal = document.getElementById('validation-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal
    const modal = document.createElement('div');
    modal.id = 'validation-modal';
    modal.className = 'fixed inset-0 z-50 overflow-y-auto';
    modal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Form Belum Lengkap
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 mb-3">
                                Mohon lengkapi field yang masih kosong atau tidak valid:
                            </p>
                            <ul class="text-sm text-red-600 space-y-1 max-h-32 overflow-y-auto">
                                ${invalidFields.map((field, index) => `<li class="cursor-pointer hover:text-red-800 hover:bg-red-50 p-1 rounded" onclick="focusField('${field.element.id || field.element.getAttribute('data-validation-group') || 'field-' + index}')">• ${field.name}</li>`).join('')}
                            </ul>
                            <p class="text-xs text-gray-400 mt-2">
                                💡 Klik pada item di atas untuk langsung ke field tersebut
                            </p>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="closeValidationModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-emerald-600 text-base font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Saya Mengerti
                    </button>
                    <button type="button" onclick="focusFirstInvalidField()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:mt-0 sm:w-auto sm:text-sm">
                        Perbaiki Sekarang
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add click outside to close
    modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target.classList.contains('bg-gray-500')) {
            closeValidationModal();
        }
    });
}

function closeValidationModal() {
    const modal = document.getElementById('validation-modal');
    if (modal) {
        modal.remove();
    }
}

function focusField(fieldId) {
    closeValidationModal();

    const field = document.getElementById(fieldId) || document.querySelector(`[data-validation-group="${fieldId}"]`);
    if (field) {
        // Use the enhanced scroll function
        scrollToField(field);
    }
}

function focusFirstInvalidField() {
    closeValidationModal();

    // Find first invalid field and focus it
    const fields = document.querySelectorAll('[data-validation]');
    for (const field of fields) {
        if (window.courseValidator && !window.courseValidator.validateField(field)) {
            // Use the enhanced scroll function
            scrollToField(field);
            break;
        }
    }
}

// Initialize the validator when the page loads
document.addEventListener('DOMContentLoaded', function() {
    window.validator = new FormValidator('courseForm');
});

// Beautiful notification system
function showNotification(message, type = 'error') {
    // Remove existing notification
    const existing = document.getElementById('validation-notification');
    if (existing) existing.remove();

    const notification = document.createElement('div');
    notification.id = 'validation-notification';
    notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 ${type === 'error' ? 'border-red-500' : 'border-green-500'} transform transition-all duration-300 translate-x-full`;

    notification.innerHTML = `
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    ${type === 'error' ?
                        '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
                        '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'
                    }
                </div>
                <div class="ml-3 w-0 flex-1">
                    <p class="text-sm font-medium ${type === 'error' ? 'text-red-800' : 'text-green-800'}">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="inline-flex ${type === 'error' ? 'text-red-400 hover:text-red-600' : 'text-green-400 hover:text-green-600'}">
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}

// Form validation before submit
function validateFormBeforeSubmit() {
    const requiredFields = [
        { id: 'course_title', name: 'Judul Kursus' },
        { id: 'course_category', name: 'Kategori' },
        { id: 'course_description', name: 'Deskripsi Kursus' },
        { id: 'course_level', name: 'Level' },
        { id: 'course_duration', name: 'Estimasi Durasi' }
    ];

    // Check course type and add price if needed
    const courseType = document.querySelector('input[name="course_type"]:checked')?.value;
    if (!courseType) {
        showNotification('Pilih tipe kursus (Gratis atau Berbayar)');
        document.querySelector('input[name="course_type"]').focus();
        return false;
    }

    if (courseType === 'paid') {
        requiredFields.push({ id: 'course_price', name: 'Harga Kursus' });
    }

    // Check each required field
    for (const field of requiredFields) {
        const element = document.getElementById(field.id);
        if (!element || !element.value.trim()) {
            showNotification(`${field.name} wajib diisi!`);
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return false;
        }

        // Validate the field
        if (window.validator && !window.validator.validateField(element)) {
            showNotification(`${field.name} tidak valid. Silakan perbaiki.`);
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return false;
        }
    }

    return true;
}

// Nala Course Builder functionality
document.getElementById('ai-course-builder-btn').addEventListener('click', async function() {
    const btn = this;
    const btnText = document.getElementById('ai-builder-text');
    const originalText = btnText.innerHTML;

    // Show loading state with animation
    btn.disabled = true;
    btnText.innerHTML = '<svg class="w-4 h-4 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Nala sedang bekerja...';
    btn.classList.add('opacity-75');

    try {
        const response = await fetch('{{ route("tutor.ai-course-builder.generate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success && data.course_suggestion) {
            fillFormWithAISuggestion(data.course_suggestion);

            // Show success notification
            showNotification('✨ Nala berhasil membuat saran kursus yang amazing! Silakan review dan edit sesuai kebutuhan.', 'success');

            // Scroll to form
            document.querySelector('.bg-white.rounded-lg.shadow-sm').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else {
            throw new Error('Failed to generate course suggestion');
        }

    } catch (error) {
        console.error('Nala Course Builder Error:', error);
        showNotification('❌ Nala mengalami kendala. Silakan coba lagi dalam beberapa saat.', 'error');
    } finally {
        // Reset button state
        btn.disabled = false;
        btnText.innerHTML = originalText;
        btn.classList.remove('opacity-75');
    }
});

function fillFormWithAISuggestion(suggestion) {
    console.log('Filling form with AI suggestion:', suggestion);

    // Fill basic course information with correct field IDs
    const titleField = document.getElementById('course_title');
    if (titleField && suggestion.title) {
        titleField.value = suggestion.title;
        // Trigger validation
        titleField.dispatchEvent(new Event('input'));
    }

    const descField = document.getElementById('course_description');
    if (descField && suggestion.description) {
        descField.value = suggestion.description;
        // Trigger validation and character count
        descField.dispatchEvent(new Event('input'));
    }

    // Set category with correct field ID
    if (suggestion.category_id) {
        const categorySelect = document.getElementById('course_category');
        if (categorySelect) {
            categorySelect.value = suggestion.category_id;
            // Trigger validation
            categorySelect.dispatchEvent(new Event('change'));
        }
    }

    // Set level with correct field ID
    if (suggestion.level) {
        const levelSelect = document.getElementById('course_level');
        if (levelSelect) {
            levelSelect.value = suggestion.level;
            // Trigger validation
            levelSelect.dispatchEvent(new Event('change'));
        }
    }

    // Set duration with correct field ID
    if (suggestion.duration) {
        const durationField = document.getElementById('course_duration');
        if (durationField) {
            durationField.value = suggestion.duration;
            // Trigger validation
            durationField.dispatchEvent(new Event('input'));
        }
    }

    // Set course type and price with correct field names
    if (suggestion.is_free) {
        const freeRadio = document.querySelector('input[name="course_type"][value="free"]');
        if (freeRadio) {
            freeRadio.checked = true;
            freeRadio.dispatchEvent(new Event('change'));
            // Call the toggle function to hide price field
            togglePriceField();
        }
    } else {
        const paidRadio = document.querySelector('input[name="course_type"][value="paid"]');
        if (paidRadio) {
            paidRadio.checked = true;
            paidRadio.dispatchEvent(new Event('change'));
            // Call the toggle function to show price field
            togglePriceField();

            // Set price with correct field ID
            if (suggestion.price) {
                setTimeout(() => {
                    const priceInput = document.getElementById('course_price');
                    if (priceInput) {
                        priceInput.value = suggestion.price;
                        // Trigger validation
                        priceInput.dispatchEvent(new Event('input'));
                    }
                }, 200);
            }
        }
    }

    // Fill learning outcomes - clear existing and add new ones
    if (suggestion.learning_outcomes && suggestion.learning_outcomes.length > 0) {
        const container = document.getElementById('learning-outcomes-container');
        if (container) {
            // Clear existing outcomes
            container.innerHTML = '';

            // Add each outcome as separate input
            suggestion.learning_outcomes.forEach((outcome, index) => {
                const div = document.createElement('div');
                div.className = 'learning-outcome-item flex items-center space-x-3 mb-3';
                div.innerHTML = `
                    <div class="flex-1">
                        <input type="text" name="learning_outcomes[]" value="${outcome}"
                               placeholder="Contoh: Memahami konsep dasar React.js"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    </div>
                    <button type="button" onclick="removeLearningOutcome(this)" class="text-red-600 hover:text-red-800 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                `;
                container.appendChild(div);
            });
        }
    }

    // Fill target audience - clear existing and add new ones
    if (suggestion.target_audience && suggestion.target_audience.length > 0) {
        const container = document.getElementById('target-audience-container');
        if (container) {
            // Clear existing audience
            container.innerHTML = '';

            // Add each audience as separate input
            suggestion.target_audience.forEach((audience, index) => {
                const div = document.createElement('div');
                div.className = 'target-audience-item flex items-center space-x-3 mb-3';
                div.innerHTML = `
                    <div class="flex-1">
                        <input type="text" name="target_audience[]" value="${audience}"
                               placeholder="Contoh: Pemula yang ingin belajar web development"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    </div>
                    <button type="button" onclick="removeTargetAudience(this)" class="text-red-600 hover:text-red-800 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                `;
                container.appendChild(div);
            });
        }
    }

    // Fill requirements - clear existing and add new ones
    if (suggestion.requirements && suggestion.requirements.length > 0) {
        const container = document.getElementById('requirements-container');
        if (container) {
            // Clear existing requirements
            container.innerHTML = '';

            // Add each requirement as separate input
            suggestion.requirements.forEach((requirement, index) => {
                const div = document.createElement('div');
                div.className = 'requirement-item flex items-center space-x-3 mb-3';
                div.innerHTML = `
                    <div class="flex-1">
                        <input type="text" name="requirements[]" value="${requirement}"
                               placeholder="Contoh: Memiliki pengetahuan dasar HTML dan CSS"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    </div>
                    <button type="button" onclick="removeRequirement(this)" class="text-red-600 hover:text-red-800 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                `;
                container.appendChild(div);
            });
        }
    }

    // Show success message
    console.log('Form filled successfully with AI suggestion');
}
</script>
@endsection
