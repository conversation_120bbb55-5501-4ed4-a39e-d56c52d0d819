<header class="bg-white shadow-sm border-b sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <a href="{{ route('home') }}" class="flex items-center space-x-3">
                <img src="{{ asset('images/logo.svg') }}" alt="Ngambiskuy Logo" class="w-10 h-10">
                <span class="text-xl font-bold text-gray-900">Ngambiskuy</span>
            </a>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8">
                <a href="{{ route('courses.index') }}" class="text-gray-700 hover:text-primary transition-colors font-medium">Belajar</a>
                <a href="{{ route('tutor.register.terms') }}" class="text-gray-700 hover:text-primary transition-colors font-medium">Mengajar</a>
                <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="text-gray-700 hover:text-primary transition-colors font-medium">Coba Gratis</a>
                <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-primary transition-colors font-medium">Blog</a>
            </nav>

            <!-- Right side buttons -->
            <div class="hidden md:flex items-center space-x-4">
                @auth
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-700">Halo, {{ Auth::user()->name }}</span>

                        @if(Auth::user()->isTutor())
                            <a href="{{ route('tutor.dashboard') }}" class="btn btn-primary">Dashboard Tutor</a>
                        @elseif(Auth::user()->hasTutorProfile())
                            <a href="{{ route('tutor.register.status') }}" class="btn btn-outline">Status Aplikasi</a>
                            <a href="{{ route('user.dashboard') }}" class="btn btn-primary">Dashboard</a>
                        @else
                            <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline">Jadi Pengajar</a>
                            <a href="{{ route('user.dashboard') }}" class="btn btn-primary">Dashboard</a>
                        @endif

                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="btn btn-ghost">Keluar</button>
                        </form>
                    </div>
                @else
                    <a href="{{ route('login') }}" class="btn btn-ghost">Masuk</a>
                    <a href="{{ route('register') }}" class="btn btn-primary">Daftar</a>
                    <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline">Jadi Pengajar</a>
                @endauth
            </div>

            <!-- Mobile menu button -->
            <button class="md:hidden mobile-menu-toggle" type="button">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden mobile-menu hidden py-4 border-t">
            <div class="flex flex-col space-y-4">
                <a href="{{ route('courses.index') }}" class="text-gray-700 hover:text-primary font-medium">Belajar</a>
                <a href="{{ route('tutor.register.terms') }}" class="text-gray-700 hover:text-primary font-medium">Mengajar</a>
                <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="text-gray-700 hover:text-primary font-medium">Coba Gratis</a>
                <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-primary font-medium">Blog</a>
                <div class="flex flex-col space-y-2 pt-4 border-t">
                    @auth
                        <span class="text-gray-700 px-3 py-2">Halo, {{ Auth::user()->name }}</span>

                        @if(Auth::user()->isTutor())
                            <a href="{{ route('tutor.dashboard') }}" class="btn btn-primary w-full">Dashboard Tutor</a>
                        @elseif(Auth::user()->hasTutorProfile())
                            <a href="{{ route('tutor.register.status') }}" class="btn btn-outline w-full">Status Aplikasi</a>
                            <a href="{{ route('user.dashboard') }}" class="btn btn-primary w-full">Dashboard</a>
                        @else
                            <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline w-full">Jadi Pengajar</a>
                            <a href="{{ route('user.dashboard') }}" class="btn btn-primary w-full">Dashboard</a>
                        @endif

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="btn btn-ghost w-full justify-start">Keluar</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="btn btn-ghost w-full justify-start">Masuk</a>
                        <a href="{{ route('register') }}" class="btn btn-primary w-full">Daftar</a>
                        <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline w-full">Jadi Pengajar</a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</header>