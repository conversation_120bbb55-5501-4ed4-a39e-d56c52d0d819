<!-- <PERSON><PERSON> AI Assistant Component for Ngambiskuy -->
<div id="nala-chat-container" class="fixed bottom-6 right-6 z-50">
    <!-- Nala Toggle Button -->
    <button id="nala-chat-toggle"
        class="relative w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center group">
        <!-- Nala Avatar/Icon -->
        <div id="nala-avatar"
            class="w-10 h-10 bg-white rounded-full flex items-center justify-center transition-transform duration-300 overflow-hidden">
            <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
        </div>
        <!-- Chat Icon (hidden by default, shown when minimized) -->
        <svg id="chat-icon" class="w-7 h-7 transition-transform duration-300 hidden" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
            </path>
        </svg>
        <svg id="close-icon" class="w-7 h-7 transition-transform duration-300 hidden" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Notification Badge -->
        <div id="nala-notification"
            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full items-center justify-center hidden">
            <span id="notification-count">1</span>
        </div>

        <!-- Nala Name Label -->
        <div id="nala-label"
            class="absolute -left-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-1 rounded-lg text-sm font-medium opacity-0 pointer-events-none transition-all duration-300">
            Nala
        </div>
    </button>

    <!-- Nala Chat Window -->
    <div id="nala-chat-window"
        class="absolute bottom-20 right-0 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 transform scale-0 origin-bottom-right transition-all duration-300 opacity-0 hidden">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center overflow-hidden">
                        <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg">Nala</h3>
                        <p class="text-sm text-white/80" id="nala-status">Ngambiskuy Advance Learning Assistance</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Delete Chat Button (only for authenticated users) -->
                    <?php if(auth()->guard()->check()): ?>
                        <button id="nala-delete-chat" class="p-1 hover:bg-white/20 rounded-full transition-colors"
                            title="Hapus Riwayat Chat">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16">
                                </path>
                            </svg>
                        </button>
                    <?php endif; ?>

                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm" id="nala-online-status">Online</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="nala-messages" class="flex-1 p-4 h-80 overflow-y-auto bg-gray-50">
            <!-- Welcome Message (will be dynamically generated based on auth status) -->
            <div id="nala-welcome-message" class="mb-4">
                <!-- Content will be populated by JavaScript based on authentication status -->
            </div>

            <!-- Login Prompt for Unauthenticated Users -->
            <?php if(auth()->guard()->guest()): ?>
                <div id="nala-login-prompt" class="mb-4 hidden">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                            <img src="/images/nala.png" alt="Nala AI" class="w-full h-full object-cover">
                        </div>
                        <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm max-w-xs">
                            <p class="text-gray-800 text-sm mb-3">Untuk pengalaman terbaik dengan Nala, silakan login
                                terlebih dahulu!</p>
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('login')); ?>"
                                    class="px-3 py-2 bg-purple-600 text-white text-xs rounded-lg hover:bg-purple-700 transition-colors">
                                    Login
                                </a>
                                <a href="<?php echo e(route('register')); ?>"
                                    class="px-3 py-2 bg-gray-100 text-gray-700 text-xs rounded-lg hover:bg-gray-200 transition-colors">
                                    Daftar
                                </a>
                            </div>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 ml-11">Baru saja</span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 bg-white rounded-b-2xl">
            <div class="flex items-end space-x-3">
                <div class="flex-1 relative">
                    <textarea id="nala-input" placeholder="Tanya Nala apa saja..."
                        class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm resize-none overflow-hidden min-h-[44px] max-h-[120px] leading-5"
                        maxlength="500" rows="1"></textarea>
                    <div class="absolute right-3 bottom-2">
                        <span id="nala-char-count" class="text-xs text-gray-400">0/500</span>
                    </div>
                </div>
                <button id="nala-send-message"
                    class="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>

            <!-- Quick Actions (will be dynamically populated based on current route) -->
            <div id="nala-quick-actions" class="mt-3 flex flex-wrap gap-2">
                <!-- Default actions -->
                <button
                    class="nala-quick-action-btn px-3 py-1 bg-purple-50 text-purple-700 rounded-full text-xs hover:bg-purple-100 transition-colors"
                    data-action="course-recommendation">
                    Rekomendasi Kursus
                </button>
                <button
                    class="nala-quick-action-btn px-3 py-1 bg-purple-50 text-purple-700 rounded-full text-xs hover:bg-purple-100 transition-colors"
                    data-action="career-path">
                    Jalur Karir
                </button>
                <button
                    class="nala-quick-action-btn px-3 py-1 bg-purple-50 text-purple-700 rounded-full text-xs hover:bg-purple-100 transition-colors"
                    data-action="help">
                    Bantuan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Nala Chat Styles -->
<style>
    #nala-chat-window {
        max-height: 90vh;
        min-height: 400px;
    }

    @media (max-width: 640px) {
        #nala-chat-window {
            width: calc(100vw - 2rem);
            right: 1rem;
            left: 1rem;
            bottom: 5rem;
        }
    }

    #nala-messages::-webkit-scrollbar {
        width: 4px;
    }

    #nala-messages::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    #nala-messages::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    #nala-messages::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Nala hover effects */
    #nala-chat-toggle:hover #nala-label {
        opacity: 1;
    }

    /* Notification badge animation */
    #nala-notification {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }
    }

    .message-animation {
        animation: slideInUp 0.3s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        space-x: 1;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #9CA3AF;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
        animation-delay: -0.32s;
    }

    .typing-dot:nth-child(2) {
        animation-delay: -0.16s;
    }

    @keyframes typing {

        0%,
        80%,
        100% {
            transform: scale(0);
            opacity: 0.5;
        }

        40% {
            transform: scale(1);
            opacity: 1;
        }
    }
</style>

<!-- Pass authentication status and user data to JavaScript -->
<script>
    window.nalaConfig = {
        isAuthenticated: <?php echo e(auth()->check() ? 'true' : 'false'); ?>,
        <?php if(auth()->guard()->check()): ?>
        user: {
            name: '<?php echo e(auth()->user()->name); ?>',
            email: '<?php echo e(auth()->user()->email); ?>',
            role: '<?php echo e(auth()->user()->getRole()); ?>'
        },
    <?php endif; ?>
    currentRoute: '<?php echo e(Route::currentRouteName()); ?>',
        currentUrl: '<?php echo e(url()->current()); ?>',
        csrfToken: '<?php echo e(csrf_token()); ?>'
    };
</script>

<!-- Nala JavaScript is loaded via Vite -->
<?php $__env->startPush('scripts'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/ai-chat.js']); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/components/ai-chat.blade.php ENDPATH**/ ?>