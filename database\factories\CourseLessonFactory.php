<?php

namespace Database\Factories;

use App\Models\CourseLesson;
use App\Models\Course;
use App\Models\CourseChapter;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CourseLesson>
 */
class CourseLessonFactory extends Factory
{
    protected $model = CourseLesson::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        $type = $this->faker->randomElement(['video', 'text', 'quiz', 'assignment']);
        
        return [
            'course_id' => Course::factory(),
            'chapter_id' => CourseChapter::factory(),
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraph(),
            'content' => $this->faker->paragraphs(5, true),
            'type' => $type,
            'video_url' => $type === 'video' ? $this->faker->url() : null,
            'video_file' => null,
            'attachments' => $this->faker->randomElements([
                'slides.pdf',
                'exercise.zip',
                'reference.docx'
            ], $this->faker->numberBetween(0, 2)),
            'duration_minutes' => $this->faker->numberBetween(5, 60),
            'sort_order' => $this->faker->numberBetween(1, 20),
            'is_published' => $this->faker->boolean(85), // 85% chance of being published
            'is_free' => $this->faker->boolean(25), // 25% chance of being free
            'is_preview' => $this->faker->boolean(10), // 10% chance of being preview
        ];
    }

    /**
     * Indicate that the lesson is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the lesson is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free' => true,
        ]);
    }

    /**
     * Indicate that the lesson is a preview.
     */
    public function preview(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_preview' => true,
            'is_free' => true,
        ]);
    }

    /**
     * Set specific type.
     */
    public function type(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => $type,
            'video_url' => $type === 'video' ? $this->faker->url() : null,
        ]);
    }

    /**
     * Set specific sort order.
     */
    public function sortOrder(int $order): static
    {
        return $this->state(fn (array $attributes) => [
            'sort_order' => $order,
        ]);
    }

    /**
     * Create a video lesson.
     */
    public function video(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'video',
            'video_url' => $this->faker->url(),
            'duration_minutes' => $this->faker->numberBetween(10, 45),
        ]);
    }

    /**
     * Create a text lesson.
     */
    public function text(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'text',
            'video_url' => null,
            'content' => $this->faker->paragraphs(8, true),
            'duration_minutes' => $this->faker->numberBetween(5, 20),
        ]);
    }

    /**
     * Create a quiz lesson.
     */
    public function quiz(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'quiz',
            'video_url' => null,
            'content' => 'Quiz content will be managed separately',
            'duration_minutes' => $this->faker->numberBetween(10, 30),
        ]);
    }
}
