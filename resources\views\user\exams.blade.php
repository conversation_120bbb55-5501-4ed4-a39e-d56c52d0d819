@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
                <p class="text-gray-600 mt-1"><PERSON><PERSON><PERSON> uji<PERSON> yang telah <PERSON> ikuti dan lihat hasil</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('exams.index') }}" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <PERSON><PERSON><PERSON><PERSON>
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Ujian</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['total_enrollments'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Ujian Lulus</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['passed_exams'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Percobaan</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['total_attempts'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Rata-rata Skor</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['average_score'], 1) }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrolled Exams -->
    <div class="bg-white rounded-lg shadow-sm mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Ujian Terdaftar</h2>
        </div>
        <div class="p-6">
            @if($examEnrollments->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($examEnrollments as $enrollment)
                        <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-1">{{ $enrollment->exam->title }}</h3>
                                    <p class="text-sm text-gray-600">{{ $enrollment->exam->tutor->name }}</p>
                                </div>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                    Terdaftar
                                </span>
                            </div>

                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Durasi:</span>
                                    <span class="font-medium">{{ $enrollment->exam->time_limit ?? 'Tidak terbatas' }} menit</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Soal:</span>
                                    <span class="font-medium">{{ $enrollment->exam->questions->count() }} soal</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Terdaftar:</span>
                                    <span class="font-medium">{{ $enrollment->enrolled_at->format('d M Y') }}</span>
                                </div>
                            </div>

                            <a href="{{ route('exams.take', $enrollment->exam) }}" class="w-full btn btn-primary">
                                Mulai Ujian
                            </a>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                    <p class="text-gray-600 mb-6">Anda belum mendaftar ujian apapun. Jelajahi ujian yang tersedia.</p>
                    <a href="{{ route('exams.index') }}" class="btn btn-primary">
                        Jelajahi Ujian
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Recent Attempts -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Riwayat Ujian Terbaru</h2>
        </div>
        <div class="p-6">
            @if($examAttempts->count() > 0)
                <div class="space-y-4">
                    @foreach($examAttempts as $attempt)
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ $attempt->exam->title }}</h4>
                                <p class="text-sm text-gray-600">{{ $attempt->exam->tutor->name }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ $attempt->created_at->format('d M Y, H:i') }}</p>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center space-x-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $attempt->score_percentage }}%</p>
                                        <p class="text-xs text-gray-600">{{ $attempt->correct_answers }}/{{ $attempt->total_questions }}</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $attempt->is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $attempt->is_passed ? 'Lulus' : 'Tidak Lulus' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <p class="text-gray-600">Belum ada riwayat ujian.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
