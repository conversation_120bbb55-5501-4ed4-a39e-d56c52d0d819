<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tutor_id'); // Foreign key to users table
            $table->uuid('category_id'); // Foreign key to categories table

            // Basic Information
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->longText('long_description')->nullable();

            // Course Details
            $table->enum('level', ['beginner', 'intermediate', 'advanced']);
            $table->string('duration')->nullable(); // e.g., "20 jam"
            $table->decimal('price', 10, 2)->default(0); // Course price in IDR (minimum 30000 for paid courses)
            $table->boolean('is_free')->default(true); // Whether course is free or paid
            $table->string('language')->default('id'); // Course language

            // Platform Revenue (5% platform fee, 60% tutor without referral, 80% with referral)
            $table->decimal('platform_fee_percentage', 5, 2)->default(5.00);
            $table->decimal('tutor_revenue_percentage', 5, 2)->default(60.00);
            $table->decimal('tutor_revenue_with_referral_percentage', 5, 2)->default(80.00);

            // Media
            $table->string('thumbnail')->nullable(); // Course thumbnail image
            $table->string('preview_video')->nullable(); // Preview video URL

            // Status and Publishing
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();

            // Learning Outcomes
            $table->json('learning_outcomes')->nullable(); // What students will learn
            $table->json('requirements')->nullable(); // Course requirements
            $table->json('target_audience')->nullable(); // Who this course is for

            // SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('tags')->nullable(); // Course tags

            // Statistics (will be updated by triggers/events)
            $table->integer('total_students')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('total_reviews')->default(0);
            $table->integer('total_lessons')->default(0);
            $table->integer('total_duration_minutes')->default(0);

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('restrict');

            // Indexes
            $table->index('tutor_id');
            $table->index('category_id');
            $table->index('status');
            $table->index('is_featured');
            $table->index('published_at');
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
