<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class CourseLesson extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'course_id',
        'chapter_id',
        'title',
        'slug',
        'description',
        'content',
        'sort_order',
        'duration_minutes',
        'video_url',
        'video_file',
        'attachments',
        'type',
        'is_published',
        'is_free',
        'is_preview',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'attachments' => 'array',
        'is_published' => 'boolean',
        'is_free' => 'boolean',
        'is_preview' => 'boolean',
        'sort_order' => 'integer',
        'duration_minutes' => 'integer',
    ];

    /**
     * Get the course that owns the lesson.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the chapter that owns the lesson.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(CourseChapter::class, 'chapter_id');
    }

    /**
     * Get the quiz for this lesson.
     */
    public function quiz(): HasOne
    {
        return $this->hasOne(LessonQuiz::class, 'lesson_id');
    }

    /**
     * Get the assignment for this lesson.
     */
    public function assignment(): HasOne
    {
        return $this->hasOne(LessonAssignment::class, 'lesson_id');
    }

    /**
     * Get the progress records for this lesson.
     */
    public function progress(): HasMany
    {
        return $this->hasMany(LessonProgress::class, 'lesson_id');
    }

    /**
     * Get published lessons.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Get preview lessons.
     */
    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($lesson) {
            if (empty($lesson->slug)) {
                $lesson->slug = Str::slug($lesson->title);
            }
        });

        static::updating(function ($lesson) {
            if ($lesson->isDirty('title') && empty($lesson->slug)) {
                $lesson->slug = Str::slug($lesson->title);
            }
        });
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if ($this->duration_minutes < 60) {
            return $this->duration_minutes . ' menit';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($minutes > 0) {
            return $hours . ' jam ' . $minutes . ' menit';
        }

        return $hours . ' jam';
    }

    /**
     * Get lesson type in Indonesian.
     */
    public function getTypeIndonesianAttribute(): string
    {
        return match($this->type) {
            'video' => 'Video',
            'text' => 'Teks',
            'quiz' => 'Kuis',
            'assignment' => 'Tugas',
            default => $this->type,
        };
    }

    /**
     * Get the secure video URL for this lesson.
     */
    public function getSecureVideoUrlAttribute(): ?string
    {
        if ($this->type !== 'video') {
            return null;
        }

        // If it's a URL-based video, return the URL directly
        if ($this->video_url) {
            return $this->video_url;
        }

        // If it's an uploaded video file, return the secure route
        if ($this->video_file) {
            return route('secure.lesson.video', [
                'course' => $this->course_id,
                'lesson' => $this->id
            ]);
        }

        return null;
    }

    /**
     * Check if user can access this lesson.
     */
    public function canBeAccessedBy($user): bool
    {
        // If course is free, everyone can access
        if ($this->course->is_free) {
            return true;
        }

        // If lesson is preview, everyone can access
        if ($this->is_preview) {
            return true;
        }

        // If user is not authenticated, deny access
        if (!$user) {
            return false;
        }

        // If user is the course tutor, allow access
        if ($user->id === $this->course->tutor_id) {
            return true;
        }

        // Check if user has purchased the course
        return $user->enrollments()
                   ->where('course_id', $this->course_id)
                   ->where('status', 'active')
                   ->exists();
    }
}
