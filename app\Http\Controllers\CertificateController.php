<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\LessonProgress;
use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\UserMembership;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class CertificateController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Download certificate for a completed course.
     */
    public function downloadCourseCertificate(Course $course)
    {
        $user = Auth::user();

        // Load course with necessary relationships
        $course->load(['chapters.lessons', 'tutor']);

        // Check if user is enrolled in the course
        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            abort(404, 'Anda belum terdaftar dalam kursus ini.');
        }

        // Calculate course progress
        $lessonIds = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('id');
        });

        $totalLessons = $lessonIds->count();

        if ($totalLessons === 0) {
            abort(404, 'Kursus ini belum memiliki pelajaran.');
        }

        $courseProgress = LessonProgress::where('user_id', $user->id)
            ->whereIn('lesson_id', $lessonIds)
            ->get();

        $completedLessons = $courseProgress->where('status', 'completed')->count();
        $progressPercentage = round(($completedLessons / $totalLessons) * 100);

        // Check if course is completed (100% progress)
        if ($progressPercentage < 100) {
            return redirect()->back()->with('error', 'Anda harus menyelesaikan semua pelajaran untuk mendapatkan sertifikat.');
        }

        // Get completion date (latest completed lesson)
        $completionDate = $courseProgress->where('status', 'completed')
            ->max('completed_at') ?? $enrollment->updated_at;

        // Generate certificate ID
        $certificateId = 'NGMB-' . strtoupper(substr($course->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'course' => $course,
            'enrollment' => $enrollment,
            'completion_date' => Carbon::parse($completionDate),
            'certificate_id' => $certificateId,
            'total_lessons' => $totalLessons,
            'total_duration' => $course->total_duration_minutes ?? 0,
            'issue_date' => now(),
        ];

        // Generate PDF
        $pdf = Pdf::loadView('certificates.course-certificate', $certificateData);
        $pdf->setPaper('A4', 'landscape');

        // Download the certificate
        $filename = 'Sertifikat-' . str_replace(' ', '-', $course->title) . '-' . str_replace(' ', '-', $user->name) . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Preview certificate for a completed course.
     */
    public function previewCourseCertificate(Course $course)
    {
        $user = Auth::user();

        // Load course with necessary relationships
        $course->load(['chapters.lessons', 'tutor']);

        // Check if user is enrolled in the course
        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            abort(404, 'Anda belum terdaftar dalam kursus ini.');
        }

        // Calculate course progress
        $lessonIds = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('id');
        });

        $totalLessons = $lessonIds->count();

        if ($totalLessons === 0) {
            abort(404, 'Kursus ini belum memiliki pelajaran.');
        }

        $courseProgress = LessonProgress::where('user_id', $user->id)
            ->whereIn('lesson_id', $lessonIds)
            ->get();

        $completedLessons = $courseProgress->where('status', 'completed')->count();
        $progressPercentage = round(($completedLessons / $totalLessons) * 100);

        // Check if course is completed (100% progress)
        if ($progressPercentage < 100) {
            return redirect()->back()->with('error', 'Anda harus menyelesaikan semua pelajaran untuk melihat sertifikat.');
        }

        // Get completion date (latest completed lesson)
        $completionDate = $courseProgress->where('status', 'completed')
            ->max('completed_at') ?? $enrollment->updated_at;

        // Generate certificate ID
        $certificateId = 'NGMB-' . strtoupper(substr($course->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'course' => $course,
            'enrollment' => $enrollment,
            'completion_date' => Carbon::parse($completionDate),
            'certificate_id' => $certificateId,
            'total_lessons' => $totalLessons,
            'total_duration' => $course->total_duration_minutes ?? 0,
            'issue_date' => now(),
        ];

        return view('certificates.course-certificate', $certificateData);
    }

    /**
     * Download certificate for a passed exam.
     */
    public function downloadExamCertificate(Exam $exam)
    {
        $user = Auth::user();

        // Check if user has active membership (required for exam certificates)
        $membership = UserMembership::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if (!$membership) {
            abort(403, 'Sertifikat ujian hanya tersedia untuk member NALA. Silakan upgrade membership Anda.');
        }

        // Load exam with necessary relationships
        $exam->load(['tutor']);

        // Check if user has passed the exam
        $passedAttempt = ExamAttempt::where('user_id', $user->id)
            ->where('exam_id', $exam->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->first();

        if (!$passedAttempt) {
            abort(404, 'Anda belum lulus ujian ini atau belum mengikuti ujian.');
        }

        // Generate certificate ID
        $certificateId = 'EXAM-' . strtoupper(substr($exam->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'exam' => $exam,
            'attempt' => $passedAttempt,
            'completion_date' => Carbon::parse($passedAttempt->completed_at),
            'certificate_id' => $certificateId,
            'score_percentage' => $passedAttempt->score_percentage,
            'total_questions' => $passedAttempt->total_questions,
            'correct_answers' => $passedAttempt->correct_answers,
            'issue_date' => now(),
        ];

        // Generate PDF
        $pdf = Pdf::loadView('certificates.exam-certificate', $certificateData);
        $pdf->setPaper('A4', 'landscape');

        // Download the certificate
        $filename = 'Sertifikat-Ujian-' . str_replace(' ', '-', $exam->title) . '-' . str_replace(' ', '-', $user->name) . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Preview certificate for a passed exam.
     */
    public function previewExamCertificate(Exam $exam)
    {
        $user = Auth::user();

        // Check if user has active membership (required for exam certificates)
        $membership = UserMembership::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if (!$membership) {
            abort(403, 'Sertifikat ujian hanya tersedia untuk member NALA. Silakan upgrade membership Anda.');
        }

        // Load exam with necessary relationships
        $exam->load(['tutor']);

        // Check if user has passed the exam
        $passedAttempt = ExamAttempt::where('user_id', $user->id)
            ->where('exam_id', $exam->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->first();

        if (!$passedAttempt) {
            abort(404, 'Anda belum lulus ujian ini atau belum mengikuti ujian.');
        }

        // Generate certificate ID
        $certificateId = 'EXAM-' . strtoupper(substr($exam->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'exam' => $exam,
            'attempt' => $passedAttempt,
            'completion_date' => Carbon::parse($passedAttempt->completed_at),
            'certificate_id' => $certificateId,
            'score_percentage' => $passedAttempt->score_percentage,
            'total_questions' => $passedAttempt->total_questions,
            'correct_answers' => $passedAttempt->correct_answers,
            'issue_date' => now(),
        ];

        return view('certificates.exam-certificate', $certificateData);
    }
}
