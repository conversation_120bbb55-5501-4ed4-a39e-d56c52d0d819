<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class NalaChatMessage extends Model
{
    use HasFactory;

    protected $table = 'nala_chat_messages';
    
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'conversation_id',
        'sender',
        'content',
        'formatted_content',
        'metadata',
        'is_prohibited',
        'is_limit_reached',
        'membership_level',
        'processing_time_ms',
        'ai_model',
        'tokens_used',
        'status',
        'error_message',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_prohibited' => 'boolean',
        'is_limit_reached' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });

        static::created(function ($model) {
            // Update conversation stats when a new message is created
            $model->conversation->updateStats();
            
            // Generate title if this is the first user message
            if ($model->sender === 'user' && $model->conversation->message_count <= 1) {
                $model->conversation->generateTitle();
            }
        });
    }

    /**
     * Get the conversation that owns this message
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(NalaChatConversation::class, 'conversation_id');
    }

    /**
     * Format content for display (convert markdown to HTML)
     */
    public function getFormattedContentAttribute()
    {
        if ($this->sender === 'ai' && !empty($this->attributes['formatted_content'])) {
            return $this->attributes['formatted_content'];
        }

        // Format the content
        $content = $this->content;
        
        // Convert newlines to <br>
        $content = nl2br($content);
        
        // Convert **text** to <strong>text</strong> for bold
        $content = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $content);
        
        // Convert *text* to <em>text</em> for italic (but not if part of **)
        $content = preg_replace('/(?<!\*)\*(?!\*)([^*]+?)\*(?!\*)/s', '<em>$1</em>', $content);
        
        // Convert bullet points
        $content = str_replace('• ', '• ', $content);
        
        return $content;
    }

    /**
     * Get timestamp formatted for display
     */
    public function getFormattedTimestampAttribute()
    {
        // Format timestamp to Indonesian locale with hours and minutes only
        return $this->created_at->setTimezone('Asia/Jakarta')->format('H:i');
    }

    /**
     * Scope for messages by sender
     */
    public function scopeBySender($query, $sender)
    {
        return $query->where('sender', $sender);
    }

    /**
     * Scope for active messages (not deleted)
     */
    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'deleted');
    }

    /**
     * Soft delete this message
     */
    public function softDelete()
    {
        $this->update(['status' => 'deleted']);
        $this->conversation->updateStats();
    }
}
