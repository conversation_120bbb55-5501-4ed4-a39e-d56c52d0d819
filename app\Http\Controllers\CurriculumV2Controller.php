<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CurriculumV2Controller extends Controller
{
    /**
     * Display the courses page with enhanced design and features.
     */
    public function index(Request $request)
    {
        // Get all active categories for filter
        $categories = Category::active()->orderBy('sort_order')->get();

        // Start building the query with optimized eager loading
        $query = Course::with(['tutor:id,name', 'category:id,name,slug'])
            ->published();

        // Apply advanced sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'popular':
                $query->orderBy('total_students', 'desc')
                      ->orderBy('average_rating', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc')
                      ->orderBy('total_students', 'desc');
                break;
            case 'price_low':
                $query->orderBy('price', 'asc')
                      ->orderBy('is_featured', 'desc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc')
                      ->orderBy('is_featured', 'desc');
                break;
            case 'duration_short':
                $query->orderBy('total_duration_minutes', 'asc');
                break;
            case 'duration_long':
                $query->orderBy('total_duration_minutes', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('is_featured', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
        }

        // Apply filters
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('price_type')) {
            if ($request->price_type === 'free') {
                $query->where('price', 0);
            } elseif ($request->price_type === 'paid') {
                $query->where('price', '>', 0);
            }
        }

        // Advanced price range filter
        if ($request->filled('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->filled('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        // Rating filter
        if ($request->filled('rating')) {
            $query->where('average_rating', '>=', $request->rating);
        }

        // Duration filter
        if ($request->filled('duration')) {
            switch ($request->duration) {
                case 'short':
                    $query->where('total_duration_minutes', '<=', 120); // 2 hours or less
                    break;
                case 'medium':
                    $query->whereBetween('total_duration_minutes', [121, 360]); // 2-6 hours
                    break;
                case 'long':
                    $query->where('total_duration_minutes', '>', 360); // More than 6 hours
                    break;
            }
        }

        // Enhanced search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('long_description', 'like', "%{$search}%")
                  ->orWhereJsonContains('tags', $search)
                  ->orWhereHas('tutor', function ($tutorQuery) use ($search) {
                      $tutorQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('category', function ($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get courses with pagination (12 items per page for better grid layout)
        $courses = $query->paginate(12)->withQueryString();

        // Get enhanced course statistics with caching for better performance
        $stats = Cache::remember('course_stats_v2', 300, function () {
            return [
                'total_courses' => Course::published()->count(),
                'free_courses' => Course::published()->where('price', 0)->count(),
                'paid_courses' => Course::published()->where('price', '>', 0)->count(),
                'total_students' => Course::published()->sum('total_students'),
                'average_rating' => Course::published()->where('average_rating', '>', 0)->avg('average_rating'),
                'total_hours' => Course::published()->sum('total_duration_minutes') / 60,
                'categories_count' => Category::active()->count(),
                'featured_courses' => Course::published()->where('is_featured', true)->count(),
            ];
        });

        // Get price range for filter
        $priceRange = Cache::remember('course_price_range', 300, function () {
            $courses = Course::published()->where('price', '>', 0);
            return [
                'min' => $courses->min('price') ?: 0,
                'max' => $courses->max('price') ?: 1000000,
            ];
        });

        // Enhanced SEO data with dynamic content
        $seoData = [
            'title' => $this->generateSeoTitle($request),
            'description' => $this->generateSeoDescription($request, $stats),
            'keywords' => $this->generateSeoKeywords($request),
            'canonical' => request()->url(),
        ];

        // Get featured courses for hero section
        $featuredCourses = Course::with(['tutor:id,name', 'category:id,name,slug'])
            ->published()
            ->where('is_featured', true)
            ->orderBy('average_rating', 'desc')
            ->limit(3)
            ->get();

        return view('courses-v2', compact(
            'courses', 
            'categories', 
            'stats', 
            'seoData', 
            'priceRange', 
            'featuredCourses'
        ));
    }

    /**
     * Generate dynamic SEO title based on filters
     */
    private function generateSeoTitle(Request $request)
    {
        $title = 'Kursus Teknologi Terbaik';
        
        if ($request->filled('category')) {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $title = "Kursus {$category->name}";
            }
        }
        
        if ($request->filled('level')) {
            $levelMap = [
                'beginner' => 'Pemula',
                'intermediate' => 'Menengah', 
                'advanced' => 'Lanjutan'
            ];
            $title .= " untuk {$levelMap[$request->level]}";
        }
        
        if ($request->filled('price_type') && $request->price_type === 'free') {
            $title .= ' Gratis';
        }
        
        return $title . ' - Ngambiskuy';
    }

    /**
     * Generate dynamic SEO description
     */
    private function generateSeoDescription(Request $request, array $stats)
    {
        $description = "Temukan {$stats['total_courses']} kursus teknologi berkualitas tinggi";
        
        if ($stats['free_courses'] > 0) {
            $description .= " termasuk {$stats['free_courses']} kursus gratis";
        }
        
        $description .= ". Belajar dari instruktur berpengalaman dengan rating rata-rata " . 
                       number_format($stats['average_rating'], 1) . 
                       ". Bergabung dengan " . number_format($stats['total_students']) . 
                       " siswa yang telah mengembangkan karir mereka.";
        
        return $description;
    }

    /**
     * Generate dynamic SEO keywords
     */
    private function generateSeoKeywords(Request $request)
    {
        $keywords = ['kursus programming', 'belajar coding', 'kursus teknologi', 'online course Indonesia'];
        
        if ($request->filled('category')) {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $keywords[] = "kursus {$category->name}";
            }
        }
        
        if ($request->filled('search')) {
            $keywords[] = $request->search;
        }
        
        return implode(', ', $keywords);
    }
}
