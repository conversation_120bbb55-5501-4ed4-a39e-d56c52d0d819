<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\ExamEnrollment;
use App\Models\UserMembership;
use App\Models\MembershipPlan;
use App\Models\Payment;
use Carbon\Carbon;

class ExamCertificateTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating exam certificate test data...');

        // Get or create test user
        $user = User::where('email', '<EMAIL>')->first();
        if (!$user) {
            $user = User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
                'email_verified_at' => now(),
            ]);
        }

        // Get or create membership plan
        $membershipPlan = MembershipPlan::where('name', 'Basic')->first();
        if (!$membershipPlan) {
            $membershipPlan = MembershipPlan::create([
                'name' => 'Basic',
                'description' => 'Basic NALA membership with AI features',
                'price' => 89000,
                'duration_months' => 1,
                'features' => [
                    'AI Teaching Assistant',
                    'Course Certificates',
                    'Exam Certificates',
                    'Progress Tracking'
                ],
                'is_active' => true,
            ]);
        }

        // Create active membership for test user
        $membership = UserMembership::where('user_id', $user->id)->first();
        if (!$membership) {
            $membership = UserMembership::create([
                'user_id' => $user->id,
                'membership_plan_id' => $membershipPlan->id,
                'status' => 'active',
                'starts_at' => now()->subDays(5),
                'expires_at' => now()->addDays(25),
                'has_free_certifications' => true,
                'nala_prompts_allocated' => $membershipPlan->nala_prompts ?? 100,
                'nala_prompts_remaining' => $membershipPlan->nala_prompts ?? 100,
            ]);

            // Create payment record
            Payment::create([
                'user_id' => $user->id,
                'payment_type' => 'membership',
                'payable_type' => MembershipPlan::class,
                'payable_id' => $membershipPlan->id,
                'amount' => $membershipPlan->price,
                'currency' => 'IDR',
                'status' => 'completed',
                'payment_method' => 'bank_transfer',
                'transaction_id' => 'TXN-' . strtoupper(uniqid()),
                'paid_at' => now()->subDays(5),
            ]);
        }

        // Get published exams
        $exams = Exam::published()->limit(3)->get();

        if ($exams->isEmpty()) {
            $this->command->warn('No published exams found. Please run ExamSeeder first.');
            return;
        }

        foreach ($exams as $exam) {
            // Create enrollment
            $enrollment = ExamEnrollment::firstOrCreate([
                'user_id' => $user->id,
                'exam_id' => $exam->id,
            ], [
                'payment_status' => 'paid',
                'amount_paid' => $exam->price,
                'enrolled_at' => now()->subDays(3),
                'is_active' => true,
                'has_passed' => true,
                'passed_at' => now()->subDays(2),
            ]);

            // Create payment for enrollment if it's a paid exam
            if ($exam->price > 0) {
                Payment::firstOrCreate([
                    'user_id' => $user->id,
                    'payable_type' => Exam::class,
                    'payable_id' => $exam->id,
                ], [
                    'payment_type' => 'exam',
                    'amount' => $exam->price,
                    'currency' => 'IDR',
                    'status' => 'completed',
                    'payment_method' => 'bank_transfer',
                    'transaction_id' => 'TXN-' . strtoupper(uniqid()),
                    'paid_at' => now()->subDays(3),
                ]);
            }

            // Create passed exam attempt
            ExamAttempt::firstOrCreate([
                'user_id' => $user->id,
                'exam_id' => $exam->id,
                'attempt_number' => 1,
            ], [
                'started_at' => now()->subDays(2),
                'submitted_at' => now()->subDays(2)->addMinutes(30),
                'completed_at' => now()->subDays(2)->addMinutes(30),
                'time_taken' => 1800, // 30 minutes
                'total_questions' => $exam->questions->count() ?: 5,
                'answered_questions' => 5,
                'correct_answers' => 4,
                'score_percentage' => 80.00,
                'total_points' => 80,
                'max_points' => 100,
                'status' => 'completed',
                'is_passed' => true,
            ]);
        }

        $this->command->info('✨ Exam certificate test data created successfully!');
        $this->command->info('');
        $this->command->info('Test user details:');
        $this->command->info("- Email: {$user->email}");
        $this->command->info("- Password: password123");
        $this->command->info("- Membership: {$membershipPlan->name} (Active until {$membership->expires_at->format('Y-m-d')})");
        $this->command->info("- Passed exams: {$exams->count()}");
        $this->command->info('');
        $this->command->info('Now you can:');
        $this->command->info('1. Login with the test user');
        $this->command->info('2. Go to Certificates page');
        $this->command->info('3. See both course and exam certificates');
        $this->command->info('4. Download exam certificates (requires NALA membership)');
    }
}
