/* Real-time Validation Styles for Ngambiskuy */

/* Validation states for form fields */
.validation-field {
    transition: all 0.3s ease;
}

.validation-field.valid {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
    background-color: #f0fdf4 !important;
}

.validation-field.invalid {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    background-color: #fef2f2 !important;
}

/* Override Tailwind classes specifically */
input.validation-field.valid,
textarea.validation-field.valid,
select.validation-field.valid {
    border-color: #10b981 !important;
    background-color: #f0fdf4 !important;
}

input.validation-field.invalid,
textarea.validation-field.invalid,
select.validation-field.invalid {
    border-color: #ef4444 !important;
    background-color: #fef2f2 !important;
}

/* Enhanced validation feedback */
.validation-container {
    position: relative;
}

.validation-feedback {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.validation-feedback.success {
    background-color: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.validation-feedback.error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.validation-feedback.warning {
    background-color: #fefce8;
    border: 1px solid #fde68a;
    color: #92400e;
}

.validation-feedback-icon {
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.validation-requirements {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 0.8rem;
}

.validation-requirements ul {
    margin: 0;
    padding-left: 1rem;
    list-style: none;
}

.validation-requirements li {
    margin: 0.25rem 0;
    display: flex;
    align-items: center;
}

.validation-requirements li::before {
    content: "•";
    color: #64748b;
    margin-right: 0.5rem;
}

.validation-requirements li.valid::before {
    content: "✓";
    color: #10b981;
    font-weight: bold;
}

.validation-requirements li.invalid::before {
    content: "✗";
    color: #ef4444;
    font-weight: bold;
}

/* Validation icons */
.validation-icon {
    transition: opacity 0.3s ease;
}

.validation-icon.show {
    opacity: 1;
}

.validation-icon.hide {
    opacity: 0;
}

/* Validation messages */
.validation-message {
    transition: all 0.3s ease;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-message.success {
    color: #10b981;
}

.validation-message.error {
    color: #ef4444;
}

.validation-message.show {
    opacity: 1;
    transform: translateY(0);
}

.validation-message.hide {
    opacity: 0;
    transform: translateY(-10px);
}

/* Character counter styles */
.character-counter {
    font-size: 0.75rem;
    transition: color 0.3s ease;
}

.character-counter.normal {
    color: #6b7280;
}

.character-counter.warning {
    color: #f59e0b;
}

.character-counter.error {
    color: #ef4444;
}

.character-counter.success {
    color: #10b981;
}

/* Form validation summary */
.validation-summary {
    animation: slideDown 0.3s ease;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.validation-summary.error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.validation-summary.success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* Loading states */
.loading-button {
    position: relative;
    overflow: hidden;
}

.loading-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Focus states for better accessibility */
.validation-field:focus {
    outline: none;
    ring: 2px;
    ring-color: #10b981;
    ring-opacity: 0.5;
}

/* Radio button group validation */
.radio-group.invalid {
    border: 1px solid #ef4444;
    border-radius: 0.5rem;
    padding: 0.5rem;
    background-color: #fef2f2;
}

.radio-group.valid {
    border: 1px solid #10b981;
    border-radius: 0.5rem;
    padding: 0.5rem;
    background-color: #f0fdf4;
}

/* Tooltip styles for validation hints */
.validation-tooltip {
    position: absolute;
    z-index: 1000;
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    max-width: 200px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.validation-tooltip.show {
    opacity: 1;
}

/* Progress indicator for form completion */
.form-progress {
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.form-progress-bar {
    height: 100%;
    background-color: #10b981;
    transition: width 0.3s ease;
    border-radius: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .validation-message {
        font-size: 0.75rem;
    }

    .character-counter {
        font-size: 0.625rem;
    }

    .validation-summary {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
}

/* Dark mode support (if needed in the future) */
@media (prefers-color-scheme: dark) {
    .validation-field.valid {
        border-color: #059669;
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }

    .validation-field.invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    .validation-summary.error {
        background-color: #1f1f1f;
        border-color: #dc2626;
        color: #fca5a5;
    }

    .validation-summary.success {
        background-color: #1f1f1f;
        border-color: #059669;
        color: #86efac;
    }
}

/* Print styles */
@media print {
    .validation-icon,
    .validation-message,
    .character-counter,
    .validation-summary {
        display: none !important;
    }
}
