<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nala_usage_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            
            // Usage Context
            $table->string('usage_type'); // 'ice', 'teaching_assistant', 'career_predictor', 'course_recommendation'
            $table->uuid('related_id')->nullable(); // Course ID, lesson ID, etc.
            $table->string('related_type')->nullable(); // 'course', 'lesson', 'general'
            
            // Usage Details
            $table->text('prompt_text')->nullable(); // The user's prompt/question
            $table->text('response_text')->nullable(); // NALA's response
            $table->integer('tokens_used')->default(1); // Number of tokens/prompts used
            $table->json('metadata')->nullable(); // Additional context data
            
            // Source tracking
            $table->string('source_plan')->nullable(); // 'membership', 'course_tier', 'certification'
            $table->uuid('source_id')->nullable(); // ID of the plan/tier/certification
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // Indexes
            $table->index(['user_id', 'created_at']);
            $table->index(['usage_type', 'created_at']);
            $table->index(['related_id', 'related_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nala_usage_logs');
    }
};
