<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_memberships', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('membership_plan_id'); // Foreign key to membership_plans table
            $table->uuid('payment_id')->nullable(); // Foreign key to payments table

            // Membership Status
            $table->enum('status', ['pending', 'active', 'expired', 'cancelled'])->default('pending');
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();

            // Team Information (for team plans)
            $table->integer('team_size')->default(1);
            $table->json('team_members')->nullable(); // Array of user IDs for team members
            $table->boolean('is_team_leader')->default(false);
            $table->uuid('team_leader_id')->nullable(); // For team members, reference to team leader

            // NALA Usage Tracking
            $table->integer('nala_prompts_allocated')->default(0); // Daily allocation
            $table->integer('nala_prompts_used_today')->default(0); // Used today
            $table->integer('nala_prompts_remaining')->default(0); // Remaining today
            $table->date('nala_usage_reset_date')->nullable(); // Last reset date
            $table->boolean('has_unlimited_nala')->default(false);

            // Feature Access (copied from plan for performance)
            $table->boolean('has_ice_full')->default(false);
            $table->boolean('has_ai_teaching_assistants_courses')->default(false);
            $table->boolean('has_ai_teaching_assistants_tryout')->default(false);
            $table->boolean('has_free_certifications')->default(false);
            $table->boolean('has_blog_access')->default(false);
            $table->enum('career_path_predictor', ['none', 'basic', 'enhanced', 'enhanced_with_job_board'])->default('none');
            $table->boolean('has_priority_support')->default(false);

            // Usage Statistics
            $table->integer('total_nala_prompts_used')->default(0);
            $table->timestamp('last_nala_usage')->nullable();
            $table->timestamp('last_accessed_at')->nullable();

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('membership_plan_id')->references('id')->on('membership_plans')->onDelete('restrict');
            $table->foreign('team_leader_id')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['membership_plan_id', 'status']);
            $table->index('expires_at');
            $table->index('team_leader_id');
            $table->index('nala_usage_reset_date');
            $table->unique(['user_id', 'membership_plan_id', 'starts_at'], 'unique_user_membership_period');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_memberships');
    }
};
