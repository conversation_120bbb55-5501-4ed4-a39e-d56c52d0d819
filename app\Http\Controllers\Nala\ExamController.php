<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamEnrollment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExamController extends Controller
{
    private $geminiApiKey;
    private $geminiModel;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key');
        $this->geminiModel = config('services.gemini.model', 'gemini-2.0-flash-exp');
    }

    /**
     * Handle exam-related questions - Send data to Gemini AI
     */
    public function handleExamQuestion($message, $context, $userProfile, $membership, $examContext = [])
    {
        // Check membership access for exam data
        if ($membership === 'free') {
            return $this->handleFreeUserExamQuery($message, $userProfile);
        }

        try {
            // Build comprehensive exam data for Gemini
            $examData = $this->buildExamDataForGemini($examContext, $context, $userProfile, $membership);

            // Create system prompt with exam data
            $systemPrompt = $this->buildExamSystemPrompt($userProfile, $examData, $membership);

            // Create user prompt
            $userPrompt = $this->buildExamUserPrompt($message, $context, $examData);

            // Call Gemini AI with exam data
            return $this->callGeminiAPI($systemPrompt, $userPrompt);

        } catch (\Exception $e) {
            Log::error('Exam Engine Error: ' . $e->getMessage());

            // Fallback to simple exam response
            return $this->getFallbackExamResponse($message, $examContext, $userProfile, $membership);
        }
    }

    /**
     * Build comprehensive exam data for Gemini AI
     */
    private function buildExamDataForGemini($examContext, $context, $userProfile, $membership)
    {
        $examData = [
            'context_type' => $context,
            'user_profile' => $userProfile,
            'membership' => $membership,
            'available_exams' => [],
            'current_exam' => null,
            'user_enrollments' => []
        ];

        // Add current exam context if available
        if (!empty($examContext) && isset($examContext['exam'])) {
            $examData['current_exam'] = $examContext['exam'];
        }

        // Get available exams based on membership
        $examData['available_exams'] = $this->getAvailableExams($membership, 10);

        // Get user's exam enrollments/history
        if (isset($userProfile['basic_info']['user_id'])) {
            $examData['user_enrollments'] = $this->getUserExamHistory($userProfile['basic_info']['user_id']);
        }

        return $examData;
    }

    /**
     * Build system prompt for exam questions
     */
    private function buildExamSystemPrompt($userProfile, $examData, $membership)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';

        $prompt = "Anda adalah Nala, asisten belajar yang ramah di platform Ngambiskuy.

KARAKTERISTIK:
- Ramah, natural, dan supportif seperti teman
- Berikan jawaban praktis dan mudah dipahami
- Maksimal 150 kata untuk respons
- Gunakan emoji secukupnya untuk membuat friendly

PLATFORM NGAMBISKUY - UJIAN & SERTIFIKASI:
Platform sertifikasi teknologi dengan fokus pada:
- Programming Certification (Python, JavaScript, PHP, Java)
- Web Development Certification (Frontend, Backend, Full Stack)
- Mobile Development Certification (Android, iOS, React Native)
- Data Science Certification (Machine Learning, AI, Data Analysis)
- UI/UX Design Certification (Design Thinking, User Research)
- Digital Marketing Certification (SEO, Social Media, Content)
- Business Certification (Project Management, Leadership)
- Cybersecurity Certification (Network Security, Ethical Hacking)

MEMBERSHIP: {$membership}

DATA YANG TERSEDIA:";

        // Add current exam data if available
        if (!empty($examData['current_exam'])) {
            $exam = $examData['current_exam'];
            $prompt .= "\n\nUJIAN SAAT INI:
- Judul: " . ($exam['title'] ?? 'Tidak diketahui') . "
- Level: " . ($exam['difficulty_level'] ?? 'Tidak diketahui') . "
- Harga: " . ($exam['price'] ?? 'Tidak diketahui') . "
- Durasi: " . ($exam['duration_minutes'] ?? 'Tidak diketahui') . " menit
- Passing Score: " . ($exam['passing_score'] ?? 'Tidak diketahui') . "%
- Total Soal: " . ($exam['total_questions'] ?? 'Tidak diketahui') . "
- Deskripsi: " . ($exam['description'] ?? 'Tidak diketahui');
        }

        // Add available exams
        if (!empty($examData['available_exams'])) {
            $prompt .= "\n\nUJIAN TERSEDIA:";
            foreach (array_slice($examData['available_exams'], 0, 5) as $exam) {
                $price = $exam['price'] == 0 ? 'GRATIS' : 'Rp ' . number_format($exam['price'], 0, ',', '.');
                $prompt .= "\n- " . $exam['title'] . " (" . $exam['difficulty_level'] . ") - " . $price . " - " . $exam['duration_minutes'] . " menit";
            }
        }

        // Add user exam history if available
        if (!empty($examData['user_enrollments'])) {
            $prompt .= "\n\nRIWAYAT UJIAN USER:";
            foreach (array_slice($examData['user_enrollments'], 0, 3) as $enrollment) {
                $status = $enrollment['status'] ?? 'unknown';
                $score = isset($enrollment['score']) ? $enrollment['score'] . '%' : 'belum selesai';
                $prompt .= "\n- " . $enrollment['exam_title'] . " - Status: " . $status . " - Score: " . $score;
            }
        }

        $prompt .= "\n\nINSTRUKSI:
- Gunakan data di atas untuk memberikan respons yang akurat
- Jika ditanya tentang ujian spesifik, gunakan data yang tersedia
- Berikan rekomendasi berdasarkan profil user dan riwayat ujian
- Jangan buat-buat informasi yang tidak ada dalam data
- Fokus pada manfaat sertifikasi untuk karir user
- Berikan tips persiapan ujian yang praktis

Pengguna: {$name}";

        return $prompt;
    }

    /**
     * Build user prompt for exam questions
     */
    private function buildExamUserPrompt($message, $context, $examData)
    {
        $prompt = "Konteks: ";

        if ($context === 'exam_detail' && !empty($examData['current_exam'])) {
            $prompt .= "User sedang melihat halaman detail ujian '" . $examData['current_exam']['title'] . "'";
        } elseif ($context === 'exam_listing') {
            $prompt .= "User sedang melihat daftar ujian sertifikasi";
        } else {
            $prompt .= "User bertanya tentang ujian/sertifikasi";
        }

        $prompt .= "\n\nPertanyaan user: " . $message;

        $prompt .= "\n\nJawab dengan natural dan helpful berdasarkan data yang tersedia. Jika user bertanya tentang ujian tertentu, gunakan informasi yang akurat dari data.";

        return $prompt;
    }

    /**
     * Handle exam queries for free users
     */
    private function handleFreeUserExamQuery($message, $userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';

        return "Halo {$name}! Untuk mengakses informasi detail ujian dan sertifikasi, Anda perlu upgrade ke membership Basic atau lebih tinggi.

Dengan membership, Anda akan mendapatkan:
✅ Akses ke semua ujian sertifikasi
✅ Tips persiapan ujian yang detail
✅ Analisis hasil ujian
✅ Sertifikat resmi setelah lulus

Upgrade sekarang untuk mulai perjalanan sertifikasi Anda!";
    }




    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 512,
            ]
        ];

        $response = Http::timeout(15)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Fallback response when Gemini fails
     */
    private function getFallbackExamResponse($message, $examContext, $userProfile, $membership)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Anda';

        if ($membership === 'free') {
            return "Halo {$name}! Untuk mengakses ujian sertifikasi, upgrade ke membership Basic atau lebih tinggi. Sertifikasi akan sangat membantu karir Anda!";
        }

        if (!empty($examContext) && isset($examContext['exam'])) {
            $examTitle = $examContext['exam']['title'] ?? 'ujian ini';
            return "Halo {$name}! Saya siap membantu dengan pertanyaan tentang ujian \"{$examTitle}\". Ada yang ingin ditanyakan tentang persiapan, tips, atau manfaatnya?";
        }

        return "Halo {$name}! Saya siap membantu dengan pertanyaan tentang ujian sertifikasi. Ada bidang tertentu yang ingin disertifikasi?";
    }

    /**
     * Get available exams based on membership
     */
    private function getAvailableExams($membership, $limit = 5)
    {
        $query = Exam::where('is_published', true);

        // All memberships can see exams, but free users have limited access to details
        return $query->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get(['id', 'title', 'description', 'difficulty_level', 'price', 'duration_minutes', 'passing_score', 'total_questions'])
                    ->toArray();
    }

    /**
     * Get user's exam history/enrollments
     */
    private function getUserExamHistory($userId)
    {
        return ExamEnrollment::where('user_id', $userId)
            ->with('exam:id,title')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['id', 'exam_id', 'status', 'score', 'completed_at'])
            ->map(function ($enrollment) {
                return [
                    'exam_title' => $enrollment->exam->title ?? 'Unknown',
                    'status' => $enrollment->status,
                    'score' => $enrollment->score,
                    'completed_at' => $enrollment->completed_at
                ];
            })
            ->toArray();
    }

    /**
     * Get popular exams
     */
    private function getPopularExams($limit = 5)
    {
        return Exam::where('is_published', true)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get(['id', 'title', 'description', 'difficulty_level', 'price', 'duration_minutes', 'passing_score'])
            ->toArray();
    }
}
