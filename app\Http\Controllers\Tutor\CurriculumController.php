<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CurriculumController extends Controller
{
    /**
     * Show the curriculum management page for a course.
     */
    public function index(Course $course)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Load chapters with lessons
        $course->load(['chapters.lessons' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        return view('tutor.curriculum.index', compact('course'));
    }

    /**
     * Store a new chapter.
     */
    public function storeChapter(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_free' => 'boolean',
        ]);

        // Get the next sort order
        $nextSortOrder = $course->chapters()->max('sort_order') + 1;

        $chapter = CourseChapter::create([
            'course_id' => $course->id,
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'sort_order' => $nextSortOrder,
            'is_free' => $validated['is_free'] ?? false,
            'is_published' => false, // Default to draft
        ]);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil ditambahkan!');
    }

    /**
     * Show the form for creating a new material.
     */
    public function createMaterial(Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        return view('tutor.curriculum.create-material', compact('course', 'chapter'));
    }

    /**
     * Show the form for editing a material.
     */
    public function editMaterial(Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        return view('tutor.curriculum.edit-material', compact('course', 'chapter', 'lesson'));
    }

    /**
     * Store a new lesson.
     */
    public function storeLesson(Request $request, Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'uploaded_video_path' => 'nullable|string', // Path from separate upload
            'video_source' => 'nullable|in:url,upload',
            'duration_minutes' => 'required|integer|min:1|max:300',
            'type' => 'required|in:video,text,quiz,assignment',
            'is_preview' => 'boolean',
        ], [
            'duration_minutes.required' => 'Estimasi durasi harus diisi.',
            'duration_minutes.min' => 'Estimasi durasi minimal 1 menit.',
            'duration_minutes.max' => 'Estimasi durasi maksimal 300 menit.',
            'duration_minutes.integer' => 'Estimasi durasi harus berupa angka.',
        ]);

        // Use uploaded video path if available
        $videoFilePath = $validated['uploaded_video_path'] ?? null;

        // Get the next sort order within the chapter
        $nextSortOrder = $chapter->lessons()->max('sort_order') + 1;

        $lesson = CourseLesson::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'content' => $validated['content'] ?? null,
            'video_url' => $request->video_source === 'url' ? $validated['video_url'] : null,
            'video_file' => $videoFilePath,
            'duration_minutes' => $validated['duration_minutes'],
            'type' => $validated['type'],
            'sort_order' => $nextSortOrder,
            'is_free' => $course->is_free, // Inherit from course
            'is_preview' => $validated['is_preview'] ?? false,
            'is_published' => false, // Default to draft
        ]);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil ditambahkan!');
    }

    /**
     * Handle video upload with progress tracking.
     */
    public function uploadVideo(Request $request, Course $course)
    {
        // Set PHP runtime limits for large file uploads
        ini_set('upload_max_filesize', '150M');
        ini_set('post_max_size', '150M');
        ini_set('max_execution_time', '600');
        ini_set('max_input_time', '600');
        ini_set('memory_limit', '512M');

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to course.'
            ], 403);
        }

        // Check if file was uploaded
        if (!$request->hasFile('video_file')) {
            return response()->json([
                'success' => false,
                'message' => 'No file uploaded.'
            ], 422);
        }

        // Check for upload errors
        $uploadError = $request->file('video_file')->getError();
        if ($uploadError !== UPLOAD_ERR_OK) {
            $errorMessage = $this->getUploadErrorMessage($uploadError);
            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ], 422);
        }

        try {
            Log::info('Starting video upload validation', [
                'course_id' => $course->id,
                'file_present' => $request->hasFile('video_file'),
                'file_size' => $request->hasFile('video_file') ? $request->file('video_file')->getSize() : 'N/A'
            ]);

            $validated = $request->validate([
                'video_file' => 'required|file|mimes:mp4,mov,avi,quicktime|max:102400', // 100MB max
            ]);

            $videoFile = $request->file('video_file');
            Log::info('Video file validation passed', [
                'original_name' => $videoFile->getClientOriginalName(),
                'size' => $videoFile->getSize(),
                'mime_type' => $videoFile->getMimeType()
            ]);

            // Additional file size check
            $maxSize = 100 * 1024 * 1024; // 100MB in bytes
            if ($videoFile->getSize() > $maxSize) {
                return response()->json([
                    'success' => false,
                    'message' => 'File terlalu besar. Maksimal 100MB.'
                ], 422);
            }

            // Create folder structure based on course type
            $folderPath = "user/{$course->tutor_id}/course/{$course->id}/videos";

            // Generate unique filename
            $fileName = time() . '_' . \Illuminate\Support\Str::random(10) . '.' . $videoFile->getClientOriginalExtension();

            // Store in private storage for paid courses, public for free courses
            $disk = $course->is_free ? 'public' : 'local';

            Log::info('Attempting to store video file', [
                'folder_path' => $folderPath,
                'file_name' => $fileName,
                'disk' => $disk,
                'course_is_free' => $course->is_free
            ]);

            $videoFilePath = $videoFile->storeAs($folderPath, $fileName, $disk);

            Log::info('Video file stored successfully', [
                'stored_path' => $videoFilePath
            ]);

            return response()->json([
                'success' => true,
                'path' => $videoFilePath,
                'message' => 'Video berhasil diupload.',
                'file_size' => $videoFile->getSize(),
                'file_name' => $videoFile->getClientOriginalName()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'File tidak valid: ' . implode(', ', $e->validator->errors()->all()),
                'errors' => $e->validator->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Video upload failed: ' . $e->getMessage(), [
                'course_id' => $course->id,
                'tutor_id' => $course->tutor_id,
                'file_size' => $request->hasFile('video_file') ? $request->file('video_file')->getSize() : 'unknown',
                'file_name' => $request->hasFile('video_file') ? $request->file('video_file')->getClientOriginalName() : 'unknown',
                'stack_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Upload gagal: ' . $e->getMessage() . ' (Error Code: ' . $e->getCode() . ')'
            ], 500);
        }
    }

    /**
     * Get human readable upload error message.
     */
    private function getUploadErrorMessage($errorCode)
    {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File terlalu besar (melebihi upload_max_filesize di php.ini).';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File terlalu besar (melebihi MAX_FILE_SIZE di form).';
            case UPLOAD_ERR_PARTIAL:
                return 'File hanya terupload sebagian. Silakan coba lagi.';
            case UPLOAD_ERR_NO_FILE:
                return 'Tidak ada file yang diupload.';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Folder temporary tidak ditemukan.';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Gagal menulis file ke disk.';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload dihentikan oleh ekstensi PHP.';
            default:
                return 'Terjadi kesalahan upload yang tidak diketahui.';
        }
    }

    /**
     * Update chapter.
     */
    public function updateChapter(Request $request, Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_published' => 'boolean',
            'is_free' => 'boolean',
        ]);

        $chapter->update($validated);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil diperbarui!');
    }

    /**
     * Update lesson.
     */
    public function updateLesson(Request $request, Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'duration_minutes' => 'required|integer|min:1|max:300',
            'type' => 'required|in:video,text,quiz,assignment',
            'is_published' => 'boolean',
            'is_free' => 'boolean',
            'is_preview' => 'boolean',
        ], [
            'duration_minutes.required' => 'Estimasi durasi harus diisi.',
            'duration_minutes.min' => 'Estimasi durasi minimal 1 menit.',
            'duration_minutes.max' => 'Estimasi durasi maksimal 300 menit.',
            'duration_minutes.integer' => 'Estimasi durasi harus berupa angka.',
        ]);

        $lesson->update($validated);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil diperbarui!');
    }

    /**
     * Delete chapter.
     */
    public function deleteChapter(Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $chapter->delete();

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil dihapus!');
    }

    /**
     * Delete lesson.
     */
    public function deleteLesson(Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $lesson->delete();

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil dihapus!');
    }

    /**
     * Download CSV template for quiz questions
     */
    public function downloadQuizTemplate()
    {
        $csvContent = [
            '# TEMPLATE SOAL KUIS - PANDUAN PENGGUNAAN',
            '# Kolom yang WAJIB diisi: question, type, points',
            '# Tipe soal yang didukung:',
            '#   - multiple_choice: Pilihan ganda (isi option_a sampai option_d, correct_answer: A/B/C/D)',
            '#   - true_false: Benar/Salah (kosongkan option_c dan option_d, correct_answer: A untuk Benar, B untuk Salah)',
            '#   - short_answer: Jawaban singkat (kosongkan semua option, correct_answer boleh kosong)',
            '# Points: angka 1-100',
            '# Explanation: opsional, penjelasan jawaban yang benar',
            '# Sort_order: urutan soal (opsional, akan diurutkan otomatis jika kosong)',
            '',
            'question,type,option_a,option_b,option_c,option_d,correct_answer,points,explanation,sort_order',
            'Siapa presiden pertama Indonesia?,multiple_choice,Soekarno,Soeharto,Habibie,Megawati,A,10,Soekarno adalah presiden pertama Republik Indonesia yang memproklamirkan kemerdekaan,1',
            'Apakah Indonesia adalah negara kepulauan?,true_false,Benar,Salah,,,A,5,Indonesia memiliki lebih dari 17000 pulau sehingga disebut negara kepulauan,2',
            'Sebutkan ibu kota provinsi Jawa Barat,short_answer,,,,,,10,Bandung adalah ibu kota provinsi Jawa Barat,3'
        ];

        $filename = 'template_soal_kuis_' . date('Y-m-d') . '.csv';

        return response()->streamDownload(function () use ($csvContent) {
            echo implode("\n", $csvContent);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
