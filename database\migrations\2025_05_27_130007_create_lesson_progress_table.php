<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lesson_progress', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            $table->uuid('lesson_id'); // Foreign key to course_lessons table
            
            // Progress Information
            $table->enum('status', ['not_started', 'in_progress', 'completed'])->default('not_started');
            $table->integer('progress_percentage')->default(0); // Progress as percentage (0-100)
            $table->timestamp('started_at')->nullable(); // When the lesson was first accessed
            $table->timestamp('completed_at')->nullable(); // When the lesson was completed
            $table->timestamp('last_accessed_at')->nullable(); // Last time the lesson was accessed
            
            // Video Progress (for video lessons)
            $table->integer('video_progress_seconds')->default(0); // Video progress in seconds
            $table->integer('video_duration_seconds')->nullable(); // Total video duration
            
            // Quiz Progress (for quiz lessons)
            $table->integer('quiz_attempts')->default(0); // Number of quiz attempts
            $table->integer('best_quiz_score')->nullable(); // Best quiz score achieved
            $table->boolean('quiz_passed')->default(false); // Whether quiz was passed
            
            // Assignment Progress (for assignment lessons)
            $table->boolean('assignment_submitted')->default(false); // Whether assignment was submitted
            $table->integer('assignment_score')->nullable(); // Assignment score
            $table->boolean('assignment_passed')->nullable(); // Whether assignment was passed
            
            // Time Tracking
            $table->integer('time_spent_seconds')->default(0); // Total time spent on lesson
            $table->integer('visit_count')->default(0); // Number of times lesson was visited
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('lesson_id')->references('id')->on('course_lessons')->onDelete('cascade');
            
            // Indexes
            $table->index('user_id');
            $table->index('lesson_id');
            $table->index('status');
            $table->index('completed_at');
            $table->index('last_accessed_at');
            
            // Unique constraint to prevent duplicate progress records
            $table->unique(['user_id', 'lesson_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lesson_progress');
    }
};
