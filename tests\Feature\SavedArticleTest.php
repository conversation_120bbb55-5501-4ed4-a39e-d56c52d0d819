<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\BlogPost;
use App\Models\SavedArticle;
use App\Models\Category;

class SavedArticleTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'is_admin' => false,
            'is_tutor' => false,
        ]);

        // Create a category
        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category description',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create a test blog post
        $this->blogPost = BlogPost::create([
            'author_id' => $this->user->id,
            'category_id' => $this->category->id,
            'title' => 'Test Blog Post',
            'slug' => 'test-blog-post',
            'excerpt' => 'This is a test excerpt',
            'content' => 'This is test content for the blog post.',
            'status' => 'published',
            'published_at' => now(),
            'read_time' => 5,
        ]);
    }

    public function test_user_can_save_article()
    {
        $response = $this->actingAs($this->user)
            ->postJson("/blog/{$this->blogPost->slug}/save");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Artikel berhasil disimpan!'
            ]);

        $this->assertDatabaseHas('saved_articles', [
            'user_id' => $this->user->id,
            'blog_post_id' => $this->blogPost->id,
        ]);
    }

    public function test_user_cannot_save_same_article_twice()
    {
        // Save the article first
        SavedArticle::create([
            'user_id' => $this->user->id,
            'blog_post_id' => $this->blogPost->id,
            'saved_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->postJson("/blog/{$this->blogPost->slug}/save");

        $response->assertStatus(409)
            ->assertJson([
                'success' => false,
                'message' => 'Artikel sudah disimpan sebelumnya.'
            ]);
    }

    public function test_user_can_unsave_article()
    {
        // Save the article first
        SavedArticle::create([
            'user_id' => $this->user->id,
            'blog_post_id' => $this->blogPost->id,
            'saved_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/blog/{$this->blogPost->slug}/save");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Artikel berhasil dihapus dari daftar simpan!'
            ]);

        $this->assertDatabaseMissing('saved_articles', [
            'user_id' => $this->user->id,
            'blog_post_id' => $this->blogPost->id,
        ]);
    }

    public function test_user_can_check_if_article_is_saved()
    {
        // Save the article first
        SavedArticle::create([
            'user_id' => $this->user->id,
            'blog_post_id' => $this->blogPost->id,
            'saved_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/blog/{$this->blogPost->slug}/check-saved");

        $response->assertStatus(200)
            ->assertJson([
                'is_saved' => true
            ]);
    }

    public function test_guest_cannot_save_article()
    {
        $response = $this->postJson("/blog/{$this->blogPost->slug}/save");

        $response->assertStatus(401);
    }
}
