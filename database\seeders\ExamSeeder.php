<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\ExamQuestionOption;
use App\Models\User;
use App\Models\Category;

class ExamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get a tutor user
        $tutor = User::where('is_tutor', true)->first();
        if (!$tutor) {
            $tutor = User::factory()->create([
                'is_tutor' => true,
                'tutor_status' => 'approved',
                'name' => 'Sample Tutor',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
            ]);
        }

        // Get a category
        $category = Category::first();
        if (!$category) {
            $category = Category::create([
                'name' => 'Programming',
                'slug' => 'programming',
                'description' => 'Programming and software development courses',
            ]);
        }

        // Create sample exams
        $exams = [
            [
                'title' => 'Ujian Dasar PHP',
                'description' => 'Ujian untuk menguji pemahaman dasar tentang bahasa pemrograman PHP',
                'difficulty_level' => 'beginner',
                'price' => 50000,
                'time_limit' => 60,
                'max_attempts' => 3,
                'passing_score' => 70,
                'is_published' => true,
                'shuffle_questions' => true,
                'show_results_immediately' => true,
                'certificate_enabled' => true,
                'instructions' => 'Bacalah setiap soal dengan teliti. Pilih jawaban yang paling tepat. Waktu ujian adalah 60 menit.',
                'questions' => [
                    [
                        'question' => 'Apa kepanjangan dari PHP?',
                        'type' => 'multiple_choice',
                        'points' => 10,
                        'explanation' => 'PHP adalah singkatan dari PHP: Hypertext Preprocessor, yang merupakan recursive acronym.',
                        'options' => [
                            ['text' => 'PHP: Hypertext Preprocessor', 'is_correct' => true],
                            ['text' => 'Personal Home Page', 'is_correct' => false],
                            ['text' => 'Private Home Page', 'is_correct' => false],
                            ['text' => 'Professional Hypertext Processor', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'PHP adalah bahasa pemrograman yang bersifat server-side.',
                        'type' => 'true_false',
                        'points' => 5,
                        'explanation' => 'Benar. PHP dijalankan di server dan menghasilkan HTML yang dikirim ke browser client.',
                        'options' => [
                            ['text' => 'Benar', 'is_correct' => true],
                            ['text' => 'Salah', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'Bagaimana cara mendeklarasikan variabel dalam PHP?',
                        'type' => 'multiple_choice',
                        'points' => 10,
                        'explanation' => 'Dalam PHP, variabel dideklarasikan dengan menggunakan tanda $ diikuti nama variabel.',
                        'options' => [
                            ['text' => 'var $variableName', 'is_correct' => false],
                            ['text' => '$variableName', 'is_correct' => true],
                            ['text' => 'variableName', 'is_correct' => false],
                            ['text' => 'declare $variableName', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'Fungsi apa yang digunakan untuk menampilkan output dalam PHP?',
                        'type' => 'multiple_choice',
                        'points' => 10,
                        'explanation' => 'echo adalah fungsi yang paling umum digunakan untuk menampilkan output dalam PHP.',
                        'options' => [
                            ['text' => 'print()', 'is_correct' => false],
                            ['text' => 'echo', 'is_correct' => true],
                            ['text' => 'display()', 'is_correct' => false],
                            ['text' => 'show()', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'PHP case-sensitive untuk nama variabel.',
                        'type' => 'true_false',
                        'points' => 5,
                        'explanation' => 'Benar. PHP membedakan huruf besar dan kecil untuk nama variabel. $name dan $Name adalah variabel yang berbeda.',
                        'options' => [
                            ['text' => 'Benar', 'is_correct' => true],
                            ['text' => 'Salah', 'is_correct' => false],
                        ]
                    ],
                ]
            ],
            [
                'title' => 'Ujian JavaScript Fundamental',
                'description' => 'Ujian untuk menguji pemahaman fundamental tentang JavaScript',
                'difficulty_level' => 'intermediate',
                'price' => 75000,
                'time_limit' => 90,
                'max_attempts' => 2,
                'passing_score' => 75,
                'is_published' => true,
                'shuffle_questions' => false,
                'show_results_immediately' => false,
                'certificate_enabled' => true,
                'instructions' => 'Ujian ini menguji pemahaman Anda tentang konsep fundamental JavaScript. Waktu ujian adalah 90 menit.',
                'questions' => [
                    [
                        'question' => 'Apa yang dimaksud dengan hoisting dalam JavaScript?',
                        'type' => 'multiple_choice',
                        'points' => 15,
                        'explanation' => 'Hoisting adalah mekanisme JavaScript dimana deklarasi variabel dan fungsi dipindahkan ke atas scope mereka.',
                        'options' => [
                            ['text' => 'Proses mengangkat deklarasi ke atas scope', 'is_correct' => true],
                            ['text' => 'Proses menghapus variabel dari memori', 'is_correct' => false],
                            ['text' => 'Proses mengoptimalkan kode JavaScript', 'is_correct' => false],
                            ['text' => 'Proses mengkonversi tipe data', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'JavaScript adalah bahasa yang strongly typed.',
                        'type' => 'true_false',
                        'points' => 10,
                        'explanation' => 'Salah. JavaScript adalah bahasa yang weakly typed atau dynamically typed.',
                        'options' => [
                            ['text' => 'Benar', 'is_correct' => false],
                            ['text' => 'Salah', 'is_correct' => true],
                        ]
                    ],
                    [
                        'question' => 'Apa perbedaan antara == dan === dalam JavaScript?',
                        'type' => 'multiple_choice',
                        'points' => 15,
                        'explanation' => '== melakukan type coercion, sedangkan === membandingkan nilai dan tipe data tanpa konversi.',
                        'options' => [
                            ['text' => '== membandingkan nilai, === membandingkan referensi', 'is_correct' => false],
                            ['text' => '== dengan type coercion, === tanpa type coercion', 'is_correct' => true],
                            ['text' => 'Tidak ada perbedaan', 'is_correct' => false],
                            ['text' => '== untuk string, === untuk number', 'is_correct' => false],
                        ]
                    ],
                ]
            ],
            [
                'title' => 'Ujian Database MySQL',
                'description' => 'Ujian untuk menguji pemahaman tentang database MySQL dan SQL queries',
                'difficulty_level' => 'advanced',
                'price' => 100000,
                'time_limit' => 120,
                'max_attempts' => 1,
                'passing_score' => 80,
                'is_published' => false,
                'shuffle_questions' => true,
                'show_results_immediately' => true,
                'certificate_enabled' => true,
                'instructions' => 'Ujian tingkat lanjut tentang MySQL. Pastikan Anda memahami konsep normalisasi, indexing, dan query optimization.',
                'questions' => [
                    [
                        'question' => 'Apa yang dimaksud dengan normalisasi database?',
                        'type' => 'multiple_choice',
                        'points' => 20,
                        'explanation' => 'Normalisasi adalah proses mengorganisir data dalam database untuk mengurangi redundansi dan meningkatkan integritas data.',
                        'options' => [
                            ['text' => 'Proses mengorganisir data untuk mengurangi redundansi', 'is_correct' => true],
                            ['text' => 'Proses mengoptimalkan query database', 'is_correct' => false],
                            ['text' => 'Proses membuat backup database', 'is_correct' => false],
                            ['text' => 'Proses mengenkripsi data sensitif', 'is_correct' => false],
                        ]
                    ],
                    [
                        'question' => 'PRIMARY KEY dapat memiliki nilai NULL.',
                        'type' => 'true_false',
                        'points' => 10,
                        'explanation' => 'Salah. PRIMARY KEY tidak boleh memiliki nilai NULL dan harus unik.',
                        'options' => [
                            ['text' => 'Benar', 'is_correct' => false],
                            ['text' => 'Salah', 'is_correct' => true],
                        ]
                    ],
                ]
            ]
        ];

        foreach ($exams as $examData) {
            $questions = $examData['questions'];
            unset($examData['questions']);

            $exam = Exam::create([
                'tutor_id' => $tutor->id,
                'category_id' => $category->id,
                ...$examData
            ]);

            foreach ($questions as $questionData) {
                $options = $questionData['options'];
                unset($questionData['options']);

                $question = ExamQuestion::create([
                    'exam_id' => $exam->id,
                    ...$questionData
                ]);

                foreach ($options as $index => $optionData) {
                    ExamQuestionOption::create([
                        'question_id' => $question->id,
                        'option_text' => $optionData['text'],
                        'is_correct' => $optionData['is_correct'],
                        'sort_order' => $index + 1,
                    ]);
                }
            }
        }

        $this->command->info('Exam seeder completed successfully!');
        $this->command->info('Created ' . count($exams) . ' sample exams with questions and options.');
    }
}
