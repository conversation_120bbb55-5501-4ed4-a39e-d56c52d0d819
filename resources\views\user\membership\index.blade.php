@extends('layouts.user')

@section('title', 'My Membership - Ngambiskuy')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">NALA Membership</h1>
        <p class="text-gray-600 mt-2">Kelola membership dan akses fitur AI-powered learning Anda</p>
    </div>

    @if($activeMembership)
    <!-- Current Membership -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Current Membership</h2>
            <span class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">
                {{ ucfirst($activeMembership->status) }}
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Plan Info -->
            <div class="md:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    {{ $activeMembership->membershipPlan->name }} Plan
                </h3>
                <p class="text-gray-600 mb-4">{{ $activeMembership->membershipPlan->description }}</p>

                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-500">Started:</span>
                        <span class="font-medium">{{ $activeMembership->starts_at->format('d M Y') }}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">Expires:</span>
                        <span class="font-medium">{{ $activeMembership->formatted_expiration }}</span>
                    </div>
                    @if($activeMembership->days_remaining)
                    <div>
                        <span class="text-gray-500">Days Remaining:</span>
                        <span class="font-medium text-emerald-600">{{ $activeMembership->days_remaining }} days</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- NALA Usage -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 mb-3">NALA Usage Today</h4>

                @if($activeMembership->has_unlimited_nala)
                <div class="text-center">
                    <div class="text-2xl font-bold text-emerald-600">∞</div>
                    <p class="text-sm text-gray-600">Unlimited Prompts</p>
                </div>
                @else
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">
                        {{ $activeMembership->nala_prompts_remaining }}
                    </div>
                    <p class="text-sm text-gray-600">
                        of {{ $activeMembership->nala_prompts_allocated }} remaining
                    </p>

                    <!-- Progress Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        @php
                            $percentage = $activeMembership->nala_prompts_allocated > 0
                                ? ($activeMembership->nala_prompts_remaining / $activeMembership->nala_prompts_allocated) * 100
                                : 0;
                        @endphp
                        <div class="bg-emerald-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Features -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-3">Your Features</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 {{ $activeMembership->has_ice_full ? 'text-emerald-500' : 'text-gray-400' }} mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm {{ $activeMembership->has_ice_full ? 'text-gray-900' : 'text-gray-500' }}">
                        {{ $activeMembership->has_ice_full ? 'Full' : 'Limited' }} ICE
                    </span>
                </div>

                <div class="flex items-center">
                    <svg class="w-5 h-5 {{ $activeMembership->has_ai_teaching_assistants_courses ? 'text-emerald-500' : 'text-gray-400' }} mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm {{ $activeMembership->has_ai_teaching_assistants_courses ? 'text-gray-900' : 'text-gray-500' }}">
                        AI Teaching Assistant
                    </span>
                </div>

                <div class="flex items-center">
                    <svg class="w-5 h-5 {{ $activeMembership->has_free_certifications ? 'text-emerald-500' : 'text-gray-400' }} mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm {{ $activeMembership->has_free_certifications ? 'text-gray-900' : 'text-gray-500' }}">
                        Free Certifications
                    </span>
                </div>

                <div class="flex items-center">
                    <svg class="w-5 h-5 {{ $activeMembership->has_priority_support ? 'text-emerald-500' : 'text-gray-400' }} mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm {{ $activeMembership->has_priority_support ? 'text-gray-900' : 'text-gray-500' }}">
                        Priority Support
                    </span>
                </div>
            </div>
        </div>
    </div>
    @else
    <!-- No Active Membership -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-yellow-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div>
                <h3 class="text-lg font-semibold text-yellow-800">No Active Membership</h3>
                <p class="text-yellow-700">You're currently on the Free plan with limited access to NALA features.</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('payment.pricing') }}"
               class="bg-emerald-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200">
                Upgrade to Premium
            </a>
        </div>
    </div>
    @endif

    <!-- AI Features Highlight -->
    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg shadow-md p-6 mb-8 border border-purple-200">
        <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <h2 class="text-xl font-semibold text-purple-900 mb-2">Unlock AI-Powered Learning</h2>
                <p class="text-purple-700 mb-4">
                    Dapatkan akses ke NALA AI Assistant, Intelligent Course Engine (ICE), dan fitur AI canggih lainnya
                    yang akan mempercepat perjalanan belajar Anda.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-sm text-purple-800">NALA AI Teaching Assistant</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-sm text-purple-800">Intelligent Course Engine</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-sm text-purple-800">Free Course Certifications</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-sm text-purple-800">Career Path Predictor</span>
                    </div>
                </div>
                @if(!auth()->user()->hasActiveMembership())
                    <div class="mt-4">
                        <a href="#pricing" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            Upgrade Sekarang
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Membership History -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Membership History</h2>

        @if($membershipHistory->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($membershipHistory as $membership)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $membership->membershipPlan->name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $membership->starts_at->format('d M Y') }} - {{ $membership->formatted_expiration }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $membership->status === 'active' ? 'bg-green-100 text-green-800' :
                                   ($membership->status === 'expired' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800') }}">
                                {{ ucfirst($membership->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            @if($membership->payment)
                                {{ $membership->payment->formatted_amount }}
                            @else
                                Free
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <p class="text-gray-600">No membership history found.</p>
        @endif
    </div>
</div>


@endsection
