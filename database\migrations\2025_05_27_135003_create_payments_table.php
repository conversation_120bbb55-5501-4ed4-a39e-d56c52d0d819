<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            
            // Payment Type and Related Item
            $table->enum('payment_type', ['membership', 'course', 'certification', 'exam']);
            $table->uuid('payable_id'); // ID of the item being purchased
            $table->string('payable_type'); // Model class of the item being purchased
            
            // Transaction Details
            $table->string('transaction_id')->unique(); // Internal transaction ID
            $table->string('external_transaction_id')->nullable(); // Payment gateway transaction ID
            $table->decimal('amount', 10, 2); // Total amount paid
            $table->decimal('platform_fee', 10, 2)->default(0); // Platform fee (5%)
            $table->decimal('tutor_earnings', 10, 2)->default(0); // Tutor earnings
            $table->string('currency', 3)->default('IDR');
            
            // Payment Status
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            
            // Payment Method and Gateway
            $table->string('payment_method')->nullable(); // bank_transfer, credit_card, e_wallet, etc.
            $table->string('payment_gateway')->nullable(); // midtrans, xendit, etc.
            $table->json('gateway_response')->nullable(); // Raw response from payment gateway
            
            // Additional Information
            $table->text('notes')->nullable(); // Admin notes or payment notes
            $table->string('receipt_url')->nullable(); // URL to payment receipt
            $table->json('metadata')->nullable(); // Additional payment metadata
            
            // Referral Information
            $table->uuid('referrer_id')->nullable(); // User who referred the buyer
            $table->decimal('referral_bonus', 10, 2)->default(0); // Bonus for referrer
            $table->boolean('has_referral_bonus')->default(false);
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('referrer_id')->references('id')->on('users')->onDelete('set null');
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['payment_type', 'status']);
            $table->index(['payable_id', 'payable_type']);
            $table->index('transaction_id');
            $table->index('external_transaction_id');
            $table->index('paid_at');
            $table->index('referrer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
