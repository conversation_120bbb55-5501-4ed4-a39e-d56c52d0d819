<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nala_chat_conversations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // Foreign key to users table
            
            // Conversation metadata
            $table->string('title')->nullable(); // Auto-generated title from first message
            $table->string('session_id')->nullable(); // Browser session ID for guest users
            $table->enum('status', ['active', 'archived', 'deleted'])->default('active');
            
            // Context information
            $table->string('started_route')->nullable(); // Route where conversation started
            $table->string('started_context')->nullable(); // Context where conversation started
            $table->json('context_data')->nullable(); // Additional context (course info, etc.)
            
            // Statistics
            $table->integer('message_count')->default(0);
            $table->timestamp('last_message_at')->nullable();
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'last_message_at']);
            $table->index('session_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nala_chat_conversations');
    }
};
