@extends('layouts.app')

@section('title', 'Blog ' . $category->name . ' - Ngambiskuy')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-5xl font-bold mb-4">{{ $category->name }}</h1>
                @if($category->description)
                <p class="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
                    {{ $category->description }}
                </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Breadcrumb -->
    <nav class="bg-white border-b py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
                <a href="{{ route('home') }}" class="hover:text-blue-600">Home</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('blog.index') }}" class="hover:text-blue-600">Blog</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900">{{ $category->name }}</span>
            </div>
        </div>
    </nav>

    <!-- Search & Filter Section -->
    <section class="py-8 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
                <!-- Search -->
                <form method="GET" class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}" 
                               placeholder="Cari artikel dalam {{ $category->name }}..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </form>

                <!-- Categories Filter -->
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('blog.index') }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors bg-gray-100 text-gray-700 hover:bg-gray-200">
                        Semua Kategori
                    </a>
                    @foreach($categories as $cat)
                    <a href="{{ route('blog.category', $cat->slug) }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors {{ $cat->id === $category->id ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        {{ $cat->name }}
                    </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Posts Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($blogPosts->count() > 0)
                <div class="mb-6">
                    <p class="text-gray-600">
                        Menampilkan {{ $blogPosts->count() }} dari {{ $blogPosts->total() }} artikel dalam kategori <strong>{{ $category->name }}</strong>
                        @if(request('search'))
                            untuk pencarian "<strong>{{ request('search') }}</strong>"
                        @endif
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogPosts as $post)
                    <article class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                        <div class="relative">
                            <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg') }}" 
                                 alt="{{ $post->title }}" class="w-full h-48 object-cover">
                            @if($post->category)
                            <span class="absolute top-3 left-3 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $post->category->name }}</span>
                            @endif
                        </div>

                        <div class="p-6">
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold line-clamp-2">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 transition-colors">
                                        {{ $post->title }}
                                    </a>
                                </h3>

                                <p class="text-gray-600 text-sm line-clamp-3">{{ $post->excerpt }}</p>

                                <div class="flex items-center space-x-3">
                                    <img src="{{ $post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg') }}" 
                                         alt="{{ $post->author->name }}" class="w-8 h-8 rounded-full">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium">{{ $post->author->name }}</p>
                                        <div class="flex items-center space-x-2 text-xs text-gray-500">
                                            <span>{{ $post->formatted_published_date }}</span>
                                            <span>•</span>
                                            <span>{{ $post->read_time_text }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $blogPosts->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada artikel</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        @if(request('search'))
                            Tidak ada artikel dalam kategori {{ $category->name }} yang sesuai dengan pencarian "{{ request('search') }}".
                        @else
                            Belum ada artikel yang dipublikasikan dalam kategori {{ $category->name }}.
                        @endif
                    </p>
                    <div class="mt-6">
                        <a href="{{ route('blog.index') }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            Lihat Semua Artikel
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>
@endsection
