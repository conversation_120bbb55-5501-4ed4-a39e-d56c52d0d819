@extends('layouts.app')

@section('title', 'Blog Ngambiskuy - Tips Karir & Tren Tech Indonesia')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-5xl font-bold mb-4">Blog Ngambiskuy</h1>
                <p class="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
                    Tetap update dengan tren tech terbaru, tips karir, dan kisah sukses dari komunitas tech Indonesia
                </p>
            </div>
        </div>
    </section>

    <!-- Search & Filter Section -->
    <section class="py-8 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
                <!-- Search -->
                <form method="GET" class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="Cari artikel..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </form>

                <!-- Categories Filter -->
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('blog.index') }}"
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors {{ !request('category') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        Semua
                    </a>
                    @foreach($categories as $category)
                    <a href="{{ route('blog.index', ['category' => $category->id]) }}"
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors {{ request('category') == $category->id ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        {{ $category->name }}
                    </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Post -->
    @if($featuredPost && !request('search') && !request('category'))
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl overflow-hidden">
                <div class="grid lg:grid-cols-2 gap-8 items-center">
                    <div class="p-8 lg:p-12">
                        <div class="flex items-center space-x-2 mb-4">
                            <span class="bg-blue-600 text-white text-xs px-3 py-1 rounded-full">Featured</span>
                            @if($featuredPost->category)
                            <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">{{ $featuredPost->category->name }}</span>
                            @endif
                        </div>

                        <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                            <a href="{{ route('blog.show', $featuredPost->slug) }}" class="hover:text-blue-600 transition-colors">
                                {{ $featuredPost->title }}
                            </a>
                        </h2>

                        <p class="text-gray-600 mb-6 line-clamp-3">{{ $featuredPost->excerpt }}</p>

                        <div class="flex items-center space-x-4 mb-6">
                            <div class="flex items-center space-x-2">
                                <img src="{{ $featuredPost->author->profile_picture ? asset('storage/' . $featuredPost->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                     alt="{{ $featuredPost->author->name }}" class="w-10 h-10 rounded-full">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $featuredPost->author->name }}</p>
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        <span>{{ $featuredPost->formatted_published_date }}</span>
                                        <span>•</span>
                                        <span>{{ $featuredPost->read_time_text }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('blog.show', $featuredPost->slug) }}"
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            Baca Selengkapnya
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <div class="relative h-64 lg:h-full">
                        <img src="{{ $featuredPost->featured_image ? asset('storage/' . $featuredPost->featured_image) : asset('images/blog/placeholder.svg') }}"
                             alt="{{ $featuredPost->title }}"
                             class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Blog Posts Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($blogPosts->count() > 0)
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogPosts as $post)
                    <article class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                        <div class="relative">
                            <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg') }}"
                                 alt="{{ $post->title }}" class="w-full h-48 object-cover">
                            @if($post->category)
                            <span class="absolute top-3 left-3 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $post->category->name }}</span>
                            @endif
                        </div>

                        <div class="p-6">
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold line-clamp-2">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 transition-colors">
                                        {{ $post->title }}
                                    </a>
                                </h3>

                                <p class="text-gray-600 text-sm line-clamp-3">{{ $post->excerpt }}</p>

                                <div class="flex items-center space-x-3">
                                    <img src="{{ $post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                         alt="{{ $post->author->name }}" class="w-8 h-8 rounded-full">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium">{{ $post->author->name }}</p>
                                        <div class="flex items-center space-x-2 text-xs text-gray-500">
                                            <span>{{ $post->formatted_published_date }}</span>
                                            <span>•</span>
                                            <span>{{ $post->read_time_text }}</span>
                                        </div>
                                    </div>
                                </div>

                                @auth
                                <div class="flex items-center space-x-2 pt-3 border-t border-gray-100">
                                    @php
                                        $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                    @endphp
                                    @if($isSaved)
                                        <button onclick="unsaveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Tersimpan
                                        </button>
                                    @else
                                        <button onclick="saveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                            </svg>
                                            Simpan
                                        </button>
                                    @endif
                                    <a href="{{ route('blog.show', $post->slug) }}" class="flex-1 text-center px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                                        Baca
                                    </a>
                                </div>
                                @else
                                <div class="pt-3 border-t border-gray-100">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="block w-full text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                        Baca Artikel
                                    </a>
                                </div>
                                @endauth
                            </div>
                        </div>
                    </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $blogPosts->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada artikel</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        {{ request('search') ? 'Tidak ada artikel yang sesuai dengan pencarian Anda.' : 'Belum ada artikel yang dipublikasikan.' }}
                    </p>
                </div>
            @endif
        </div>
    </section>
</div>

@auth
<script>
async function saveArticle(articleSlug) {
    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Artikel berhasil disimpan!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menyimpan artikel', 'error');
        }
    } catch (error) {
        console.error('Error saving article:', error);
        showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
    }
}

async function unsaveArticle(articleSlug) {
    if (!confirm('Apakah Anda yakin ingin menghapus artikel ini dari daftar simpan?')) {
        return;
    }

    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Artikel berhasil dihapus dari daftar simpan!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menghapus artikel', 'error');
        }
    } catch (error) {
        console.error('Error unsaving article:', error);
        showNotification('Terjadi kesalahan saat menghapus artikel', 'error');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}
</script>
@endauth

@endsection
