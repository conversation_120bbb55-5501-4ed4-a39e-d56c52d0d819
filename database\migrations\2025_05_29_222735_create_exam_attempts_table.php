<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_attempts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('exam_id'); // Foreign key to exams table
            $table->uuid('user_id'); // Foreign key to users table

            // Attempt Information
            $table->integer('attempt_number')->default(1); // Which attempt this is (1, 2, 3, etc.)
            $table->timestamp('started_at')->nullable(); // When the attempt was started
            $table->timestamp('completed_at')->nullable(); // When the attempt was completed
            $table->timestamp('submitted_at')->nullable(); // When the attempt was submitted
            $table->integer('time_taken')->nullable(); // Time taken in seconds

            // Scoring
            $table->integer('total_questions')->default(0); // Total number of questions
            $table->integer('answered_questions')->default(0); // Number of questions answered
            $table->integer('correct_answers')->default(0); // Number of correct answers
            $table->decimal('score_percentage', 5, 2)->default(0); // Score as percentage
            $table->integer('total_points')->default(0); // Total points earned
            $table->integer('max_points')->default(0); // Maximum possible points
            $table->boolean('is_passed')->default(false); // Whether the attempt passed

            // Status
            $table->enum('status', ['in_progress', 'completed', 'abandoned', 'expired'])->default('in_progress');
            $table->json('answers_data')->nullable(); // Store answers as JSON for backup

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('exam_id')->references('id')->on('exams')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index('exam_id');
            $table->index('user_id');
            $table->index('status');
            $table->index('is_passed');
            $table->index(['exam_id', 'user_id', 'attempt_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_attempts');
    }
};
