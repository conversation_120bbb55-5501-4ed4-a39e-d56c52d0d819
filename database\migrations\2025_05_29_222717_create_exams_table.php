<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exams', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tutor_id'); // Foreign key to users table
            $table->uuid('category_id')->nullable(); // Foreign key to categories table

            // Basic Information
            $table->string('title');
            $table->text('description');
            $table->decimal('price', 10, 2)->default(0); // Price in IDR
            $table->enum('difficulty_level', ['beginner', 'intermediate', 'advanced'])->default('beginner');
            $table->text('instructions')->nullable(); // Special instructions for exam takers

            // Exam Settings
            $table->integer('time_limit')->default(60); // Time limit in minutes
            $table->integer('max_attempts')->default(3); // Maximum attempts allowed
            $table->integer('passing_score')->default(70); // Passing score percentage
            $table->boolean('shuffle_questions')->default(false); // Randomize question order
            $table->boolean('show_results_immediately')->default(true); // Show results after completion
            $table->boolean('certificate_enabled')->default(false); // Enable certificate generation

            // Status
            $table->boolean('is_published')->default(false);

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');

            // Indexes
            $table->index('tutor_id');
            $table->index('category_id');
            $table->index('is_published');
            $table->index('difficulty_level');
            $table->index('price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exams');
    }
};
