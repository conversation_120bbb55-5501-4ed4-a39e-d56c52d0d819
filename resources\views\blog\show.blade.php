@extends('layouts.app')

@section('title', $blogPost->meta_title ?: $blogPost->title)
@section('meta_description', $blogPost->meta_description ?: $blogPost->excerpt)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumb -->
    <nav class="bg-white border-b py-4">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
                <a href="{{ route('home') }}" class="hover:text-blue-600">Home</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('blog.index') }}" class="hover:text-blue-600">Blog</a>
                @if($blogPost->category)
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('blog.category', $blogPost->category->slug) }}" class="hover:text-blue-600">{{ $blogPost->category->name }}</a>
                @endif
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900">{{ Str::limit($blogPost->title, 50) }}</span>
            </div>
        </div>
    </nav>

    <!-- Article Header -->
    <article class="py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <header class="mb-8">
                @if($blogPost->category)
                <div class="mb-4">
                    <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">{{ $blogPost->category->name }}</span>
                </div>
                @endif

                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">{{ $blogPost->title }}</h1>

                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4">
                        <img src="{{ $blogPost->author->profile_picture ? asset('storage/' . $blogPost->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                             alt="{{ $blogPost->author->name }}" class="w-12 h-12 rounded-full">
                        <div>
                            <p class="font-medium text-gray-900">{{ $blogPost->author->name }}</p>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <span>{{ $blogPost->formatted_published_date }}</span>
                                <span>•</span>
                                <span>{{ $blogPost->read_time_text }}</span>
                                <span>•</span>
                                <span>{{ number_format($blogPost->views_count) }} views</span>
                            </div>
                        </div>
                    </div>

                    @auth
                    <div class="flex items-center space-x-3">
                        <button id="saveArticleBtn"
                                data-blog-slug="{{ $blogPost->slug }}"
                                class="save-article-btn flex items-center px-4 py-2 {{ $isSaved ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700' }} text-white rounded-lg transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($isSaved)
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                @endif
                            </svg>
                            <span class="save-text">{{ $isSaved ? 'Tersimpan' : 'Simpan' }}</span>
                        </button>

                        <a href="{{ route('user.blog') }}"
                           class="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0l-4 4m4-4l-4-4"></path>
                            </svg>
                            Lihat Artikel Tersimpan
                        </a>
                    </div>
                    @endauth
                </div>

                @if($blogPost->featured_image)
                <div class="mb-8">
                    <img src="{{ asset('storage/' . $blogPost->featured_image) }}"
                         alt="{{ $blogPost->title }}"
                         class="w-full h-64 lg:h-96 object-cover rounded-lg">
                </div>
                @endif
            </header>

            <!-- Article Content -->
            <div class="prose prose-lg max-w-none">
                <div class="text-xl text-gray-600 mb-8 font-medium">{{ $blogPost->excerpt }}</div>

                <div class="blog-content">
                    {!! nl2br(e($blogPost->content)) !!}
                </div>
            </div>

            <!-- Tags -->
            @if($blogPost->tags && count($blogPost->tags) > 0)
            <div class="mt-8 pt-8 border-t">
                <h3 class="text-lg font-semibold mb-4">Tags</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach($blogPost->tags as $tag)
                    <span class="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">#{{ $tag }}</span>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Share Section -->
            <div class="mt-8 pt-8 border-t">
                <h3 class="text-lg font-semibold mb-4">Bagikan Artikel</h3>
                <div class="flex space-x-4">
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}"
                       target="_blank"
                       class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </a>

                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($blogPost->title) }}"
                       target="_blank"
                       class="flex items-center px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </a>

                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}"
                       target="_blank"
                       class="flex items-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                    </a>
                </div>
            </div>
        </div>
    </article>

    <!-- Related Posts -->
    @if($relatedPosts->count() > 0)
    <section class="py-12 bg-white border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Artikel Terkait</h2>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedPosts as $post)
                <article class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                    <div class="relative">
                        <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg') }}"
                             alt="{{ $post->title }}" class="w-full h-48 object-cover">
                        @if($post->category)
                        <span class="absolute top-3 left-3 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $post->category->name }}</span>
                        @endif
                    </div>

                    <div class="p-6">
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold line-clamp-2">
                                <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 transition-colors">
                                    {{ $post->title }}
                                </a>
                            </h3>

                            <p class="text-gray-600 text-sm line-clamp-3">{{ $post->excerpt }}</p>

                            <div class="flex items-center space-x-3">
                                <img src="{{ $post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                     alt="{{ $post->author->name }}" class="w-8 h-8 rounded-full">
                                <div class="flex-1">
                                    <p class="text-sm font-medium">{{ $post->author->name }}</p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>{{ $post->formatted_published_date }}</span>
                                        <span>•</span>
                                        <span>{{ $post->read_time_text }}</span>
                                    </div>
                                </div>
                            </div>

                            @auth
                            <div class="flex items-center space-x-2 pt-3 border-t border-gray-100">
                                @php
                                    $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                @endphp
                                @if($isSaved)
                                    <button onclick="unsaveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Tersimpan
                                    </button>
                                @else
                                    <button onclick="saveArticle('{{ $post->slug }}')" class="flex-1 text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                        </svg>
                                        Simpan
                                    </button>
                                @endif
                                <a href="{{ route('blog.show', $post->slug) }}" class="flex-1 text-center px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                                    Baca
                                </a>
                            </div>
                            @else
                            <div class="pt-3 border-t border-gray-100">
                                <a href="{{ route('blog.show', $post->slug) }}" class="block w-full text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                    Baca Artikel
                                </a>
                            </div>
                            @endauth
                        </div>
                    </div>
                </article>
                @endforeach
            </div>
        </div>
    </section>
    @endif
</div>

@auth
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const saveBtn = document.getElementById('saveArticleBtn');
    if (saveBtn) {
        const blogSlug = saveBtn.getAttribute('data-blog-slug');
        const saveText = saveBtn.querySelector('.save-text');

        saveBtn.addEventListener('click', function() {
            toggleSaveArticle(blogSlug, saveBtn, saveText);
        });
    }

    function toggleSaveArticle(blogSlug, btn, textEl) {
        const isSaved = btn.classList.contains('bg-green-600');
        const url = `/blog/${blogSlug}/save`;
        const method = isSaved ? 'DELETE' : 'POST';

        btn.disabled = true;

        fetch(url, {
            method: method,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (isSaved) {
                    // Unsaved
                    btn.classList.remove('bg-green-600', 'hover:bg-green-700');
                    btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    textEl.textContent = 'Simpan';
                    btn.querySelector('svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>';
                } else {
                    // Saved
                    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    btn.classList.add('bg-green-600', 'hover:bg-green-700');
                    textEl.textContent = 'Tersimpan';
                    btn.querySelector('svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
                }

                // Show success message
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Global functions for related articles
    window.saveArticle = async function(articleSlug) {
        try {
            const response = await fetch(`/blog/${articleSlug}/save`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                showNotification('Artikel berhasil disimpan!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Gagal menyimpan artikel', 'error');
            }
        } catch (error) {
            console.error('Error saving article:', error);
            showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
        }
    };

    window.unsaveArticle = async function(articleSlug) {
        if (!confirm('Apakah Anda yakin ingin menghapus artikel ini dari daftar simpan?')) {
            return;
        }

        try {
            const response = await fetch(`/blog/${articleSlug}/save`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                showNotification('Artikel berhasil dihapus dari daftar simpan!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Gagal menghapus artikel', 'error');
            }
        } catch (error) {
            console.error('Error unsaving article:', error);
            showNotification('Terjadi kesalahan saat menghapus artikel', 'error');
        }
    };
});
</script>
@endpush
@endauth
@endsection
