<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\MembershipPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MembershipPlanBlogAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /** @test */
    public function free_plan_does_not_have_blog_access()
    {
        $freePlan = MembershipPlan::where('slug', 'free')->first();

        $this->assertNotNull($freePlan);
        $this->assertFalse($freePlan->has_blog_access);
        $this->assertNotContains('Blog Access & Creation', $freePlan->all_features);
    }

    /** @test */
    public function basic_plan_has_blog_access()
    {
        $basicPlan = MembershipPlan::where('slug', 'basic')->first();

        $this->assertNotNull($basicPlan);
        $this->assertTrue($basicPlan->has_blog_access);
        $this->assertContains('Blog Access & Creation', $basicPlan->all_features);
    }

    /** @test */
    public function standard_plan_has_blog_access()
    {
        $standardPlan = MembershipPlan::where('slug', 'standard')->first();

        $this->assertNotNull($standardPlan);
        $this->assertTrue($standardPlan->has_blog_access);
        $this->assertContains('Blog Access & Creation', $standardPlan->all_features);
    }

    /** @test */
    public function pro_plan_has_blog_access()
    {
        $proPlan = MembershipPlan::where('slug', 'pro')->first();

        $this->assertNotNull($proPlan);
        $this->assertTrue($proPlan->has_blog_access);
        $this->assertContains('Blog Access & Creation', $proPlan->all_features);
    }

    /** @test */
    public function membership_plan_seeder_includes_blog_access()
    {
        $plans = MembershipPlan::all();

        $this->assertCount(4, $plans);

        // Free plan should not have blog access
        $freePlan = $plans->where('slug', 'free')->first();
        $this->assertFalse($freePlan->has_blog_access);

        // All paid plans should have blog access
        $paidPlans = $plans->whereIn('slug', ['basic', 'standard', 'pro']);
        foreach ($paidPlans as $plan) {
            $this->assertTrue($plan->has_blog_access, "Plan {$plan->name} should have blog access");
        }
    }
}
