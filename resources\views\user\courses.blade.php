@extends('layouts.user')

@section('title', 'Kurs<PERSON>')

@section('content')
<div class="p-6">
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
                <p class="text-gray-600 mt-1"><PERSON><PERSON><PERSON> dan lan<PERSON><PERSON> per<PERSON>lanan belajar <PERSON></p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('courses.index') }}" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <PERSON><PERSON><PERSON><PERSON>
                </a>
                @if(!auth()->user()->hasActiveMembership())
                    <a href="{{ route('user.membership') }}" class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        Upgrade Membership
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="border-primary text-primary whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" id="enrolled-tab">
                    Kursus Diikuti
                    <span class="bg-primary text-white ml-2 py-0.5 px-2.5 rounded-full text-xs">{{ count($inProgressCourses) }}</span>
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" id="available-tab">
                    Kursus Tersedia
                    <span class="bg-gray-100 text-gray-900 ml-2 py-0.5 px-2.5 rounded-full text-xs">{{ count($availableCourses) }}</span>
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" id="completed-tab">
                    Selesai
                    <span class="bg-gray-100 text-gray-900 ml-2 py-0.5 px-2.5 rounded-full text-xs">{{ count($completedCourses) }}</span>
                </button>
            </nav>
        </div>
    </div>

    <!-- Enrolled Courses Tab -->
    <div id="enrolled-content" class="tab-content">
        @if(count($inProgressCourses) > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($inProgressCourses as $enrollmentData)
                    @php
                        $course = $enrollmentData['course'];
                        $enrollment = $enrollmentData['enrollment'];
                        $progress = $enrollmentData['progress'];
                    @endphp
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                        <div class="h-40 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-4 left-4 text-white">
                                <h3 class="text-lg font-bold">{{ $course->title }}</h3>
                                <p class="text-sm opacity-90">{{ $course->tutor->name }}</p>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                    {{ $progress }}% Selesai
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full" style="width: {{ $progress }}%"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                                <span>{{ $course->lessons->count() }} Pelajaran</span>
                                <span>{{ $course->level_indonesian }}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <a href="{{ route('course.learn', $course) }}" class="btn btn-primary">
                                    Lanjutkan Belajar
                                </a>
                                @if(auth()->user()->hasActiveMembership())
                                    <button onclick="openNALA()" class="btn btn-outline">
                                        Chat NALA
                                    </button>
                                @else
                                    <a href="{{ route('user.membership') }}" class="btn btn-outline">
                                        Tanya NALA
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm p-12 text-center border border-gray-200">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">Belum Ada Kursus</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">
                    Anda belum mendaftar ke kursus apapun. Mulai perjalanan belajar Anda dengan memilih kursus yang menarik.
                </p>
                <div class="space-y-3">
                    <a href="{{ route('courses.index') }}" class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Jelajahi Kursus
                    </a>
                    @if(!auth()->user()->isTutor() && !auth()->user()->hasTutorProfile())
                        <div class="text-sm text-gray-500">atau</div>
                        <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Jadi Pengajar
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <!-- Available Courses Tab -->
    <div id="available-content" class="tab-content hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($availableCourses as $course)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                    <div class="h-40 bg-gradient-to-br from-indigo-500 to-purple-600 relative">
                        @if($course->thumbnail)
                            <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                        @endif
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-lg font-bold">{{ $course->title }}</h3>
                            <p class="text-sm opacity-90">{{ $course->tutor->name }}</p>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                                {{ $course->level_indonesian }}
                            </span>
                        </div>
                        @if($course->price == 0)
                            <span class="absolute top-4 left-4 bg-green-600 text-white text-xs px-2 py-1 rounded">GRATIS</span>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span>{{ number_format($course->average_rating, 1) }}</span>
                            </div>
                            <span>{{ number_format($course->total_students) }} siswa</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                            <span>{{ $course->duration }}</span>
                            <span class="font-bold text-primary">
                                @if($course->price == 0)
                                    Gratis
                                @else
                                    {{ $course->formatted_price }}
                                @endif
                            </span>
                        </div>
                        <a href="{{ route('course.show', $course) }}" class="w-full btn btn-primary">
                            Lihat Detail
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Completed Courses Tab -->
    <div id="completed-content" class="tab-content hidden">
        @if(count($completedCourses) > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($completedCourses as $enrollmentData)
                    @php
                        $course = $enrollmentData['course'];
                        $enrollment = $enrollmentData['enrollment'];
                        $progress = $enrollmentData['progress'];
                    @endphp
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                        <div class="h-40 bg-gradient-to-br from-green-500 to-emerald-600 relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-4 left-4 text-white">
                                <h3 class="text-lg font-bold">{{ $course->title }}</h3>
                                <p class="text-sm opacity-90">{{ $course->tutor->name }}</p>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Selesai
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                                <span>{{ $course->lessons->count() }} Pelajaran</span>
                                <span>{{ $course->level_indonesian }}</span>
                            </div>
                            <div class="grid grid-cols-1 gap-3">
                                <a href="{{ route('course.certificate.download', $course) }}" class="btn btn-primary">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Download Sertifikat
                                </a>
                                <div class="grid grid-cols-2 gap-2">
                                    <a href="{{ route('course.certificate.preview', $course) }}" target="_blank" class="btn btn-outline text-xs">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Preview
                                    </a>
                                    <a href="{{ route('course.learn', $course) }}" class="btn btn-outline text-xs">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Review
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm p-12 text-center border border-gray-200">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">Belum Ada Kursus Selesai</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">
                    Anda belum menyelesaikan kursus apapun. Lanjutkan belajar untuk mendapatkan sertifikat pertama Anda!
                </p>
                <a href="{{ route('user.dashboard') }}" class="btn btn-primary">
                    Lanjutkan Belajar
                </a>
            </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('[id$="-tab"]');
    const contents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-primary', 'text-primary');
                t.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active classes to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-primary', 'text-primary');

            // Hide all content
            contents.forEach(content => content.classList.add('hidden'));

            // Show corresponding content
            const contentId = this.id.replace('-tab', '-content');
            document.getElementById(contentId).classList.remove('hidden');
        });
    });
});

function openNALA() {
    // This would open the NALA AI chat interface
    // For now, we'll show an alert - replace with actual NALA integration
    alert('NALA AI Assistant akan segera tersedia! Fitur ini akan membantu Anda dengan pertanyaan tentang kursus.');

    // Future implementation could be:
    // window.open('/nala-chat', 'nala', 'width=400,height=600');
    // or integrate with a chat widget
}
</script>
@endsection
