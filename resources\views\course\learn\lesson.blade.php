@extends('layouts.app')

@section('title', $lesson->title . ' - ' . $course->title . ' - Ngambiskuy')

@push('styles')
<style>
    .lesson-content {
        transition: all 0.3s ease;
    }
    .lesson-navigation:hover {
        /* Removed transform effect */
    }
</style>
@endpush

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Enhanced <PERSON><PERSON>er -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-start space-x-4">
                    <a href="{{ route('course.learn', $course) }}"
                       class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Kembali ke Kursus
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <!-- Lesson Type Icon -->
                            @if($lesson->type === 'video')
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                            @elseif($lesson->type === 'text')
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                            @elseif($lesson->type === 'quiz')
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                    </svg>
                                </div>
                            @endif
                            <h1 class="text-2xl font-bold text-gray-900">{{ $lesson->title }}</h1>
                        </div>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                {{ $lesson->chapter->title }}
                            </span>
                            <span class="text-gray-400">•</span>
                            <span>{{ $course->title }}</span>
                            @if($lesson->duration_minutes)
                                <span class="text-gray-400">•</span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    {{ $lesson->duration_minutes }} menit
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Enhanced Navigation -->
                <div class="flex items-center space-x-3">
                    @if($navigation['previous'])
                        <a href="{{ route('course.lesson', [$course, $navigation['previous']]) }}"
                           class="lesson-navigation flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Sebelumnya
                        </a>
                    @endif

                    @if($navigation['next'])
                        <a href="{{ route('course.lesson', [$course, $navigation['next']]) }}"
                           class="lesson-navigation flex items-center px-6 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary/90 transition-colors duration-200">
                            Selanjutnya
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex gap-8">
            <!-- Lesson Content -->
            <div class="flex-1">
                <div class="lesson-content bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100">
                    @if($lesson->type === 'video')
                        <!-- Video Content -->
                        <div class="aspect-video bg-black">
                            @if($lesson->video_url)
                                <!-- External Video (YouTube, Vimeo, etc.) -->
                                <iframe
                                    src="{{ $lesson->video_url }}"
                                    class="w-full h-full"
                                    frameborder="0"
                                    allowfullscreen>
                                </iframe>
                            @elseif($lesson->video_file)
                                <!-- Uploaded Video -->
                                <video
                                    class="w-full h-full"
                                    controls
                                    preload="metadata"
                                    id="lesson-video">
                                    <source src="{{ $lesson->secure_video_url }}" type="video/mp4">
                                    Browser Anda tidak mendukung pemutar video.
                                </video>
                            @else
                                <div class="flex items-center justify-center h-full text-white">
                                    <div class="text-center">
                                        <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                        <p class="opacity-75">Video tidak tersedia</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @elseif($lesson->type === 'text')
                        <!-- Text Content -->
                        <div class="p-6">
                            <div class="prose max-w-none">
                                {!! $lesson->content ?: '<p>Konten teks tidak tersedia.</p>' !!}
                            </div>
                        </div>
                    @elseif($lesson->type === 'quiz')
                        <!-- Quiz Content -->
                        <div class="p-6">
                            @if($lesson->quiz)
                                <div class="mb-6">
                                    <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->quiz->title }}</h2>
                                    @if($lesson->quiz->description)
                                        <p class="text-gray-600 mb-4">{{ $lesson->quiz->description }}</p>
                                    @endif

                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                        <h3 class="font-semibold text-blue-900 mb-2">Informasi Kuis</h3>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <span class="text-blue-700">Jumlah Soal:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->total_questions }}</div>
                                            </div>
                                            <div>
                                                <span class="text-blue-700">Total Poin:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->total_points }}</div>
                                            </div>
                                            @if($lesson->quiz->time_limit)
                                                <div>
                                                    <span class="text-blue-700">Waktu:</span>
                                                    <div class="font-semibold">{{ $lesson->quiz->time_limit }} menit</div>
                                                </div>
                                            @endif
                                            @if($lesson->quiz->passing_score)
                                                <div>
                                                    <span class="text-blue-700">Nilai Lulus:</span>
                                                    <div class="font-semibold">{{ $lesson->quiz->passing_score }}%</div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <button class="btn btn-primary btn-lg" onclick="startQuiz()">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Mulai Kuis
                                    </button>
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <p class="text-gray-500">Kuis tidak tersedia.</p>
                                </div>
                            @endif
                        </div>
                    @elseif($lesson->type === 'assignment')
                        <!-- Assignment Content -->
                        <div class="p-6">
                            @if($lesson->assignment)
                                <div class="mb-6">
                                    <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->assignment->title }}</h2>
                                    @if($lesson->assignment->description)
                                        <div class="prose max-w-none mb-6">
                                            {!! $lesson->assignment->description !!}
                                        </div>
                                    @endif

                                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                                        <h3 class="font-semibold text-orange-900 mb-2">Informasi Tugas</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span class="text-orange-700">Maksimal Poin:</span>
                                                <div class="font-semibold">{{ $lesson->assignment->max_points }}</div>
                                            </div>
                                            @if($lesson->assignment->due_date)
                                                <div>
                                                    <span class="text-orange-700">Deadline:</span>
                                                    <div class="font-semibold">{{ $lesson->assignment->formatted_due_date }}</div>
                                                </div>
                                            @endif
                                            <div>
                                                <span class="text-orange-700">File Maksimal:</span>
                                                <div class="font-semibold">{{ $lesson->assignment->max_files }} file</div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($lesson->assignment->requirements)
                                        <div class="mb-6">
                                            <h3 class="font-semibold text-gray-900 mb-2">Persyaratan Tugas</h3>
                                            <div class="prose max-w-none">
                                                {!! $lesson->assignment->requirements !!}
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="text-center">
                                    <button class="btn btn-primary btn-lg" onclick="startAssignment()">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Mulai Mengerjakan
                                    </button>
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <p class="text-gray-500">Tugas tidak tersedia.</p>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Lesson Description -->
                @if($lesson->description)
                    <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Deskripsi Materi</h3>
                        <div class="prose max-w-none text-gray-600">
                            <p>{{ $lesson->description }}</p>
                        </div>
                    </div>
                @endif

                <!-- Mark as Complete -->
                <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Progress Materi</h3>
                            <p class="text-sm text-gray-600">Tandai sebagai selesai setelah Anda menyelesaikan materi ini</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            @if($progress->status === 'completed')
                                <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Selesai
                                </span>
                            @else
                                <button onclick="markAsComplete()" class="btn btn-primary" id="complete-btn">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Tandai Selesai
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="w-80 flex-shrink-0">
                <!-- Lesson Info -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-100">
                    <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Informasi Materi
                    </h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tipe:</span>
                            <span class="font-medium capitalize">{{ $lesson->type_indonesian }}</span>
                        </div>
                        @if($lesson->duration_minutes)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Durasi:</span>
                                <span class="font-medium">{{ $lesson->duration_minutes }} menit</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            @if($progress->status === 'completed')
                                <span class="font-medium text-green-600">Selesai</span>
                            @elseif($progress->status === 'in_progress')
                                <span class="font-medium text-yellow-600">Sedang Belajar</span>
                            @else
                                <span class="font-medium text-gray-600">Belum Dimulai</span>
                            @endif
                        </div>
                        @if($progress->completed_at)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Selesai pada:</span>
                                <span class="font-medium">{{ $progress->completed_at->format('d M Y') }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Chapter Navigation -->
                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        {{ $lesson->chapter->title }}
                    </h3>
                    <div class="space-y-2">
                        @foreach($lesson->chapter->lessons as $chapterLesson)
                            <div class="flex items-center space-x-2 p-2 rounded {{ $chapterLesson->id === $lesson->id ? 'bg-primary/10 border border-primary/20' : 'hover:bg-gray-50' }}">
                                @if($chapterLesson->type === 'video')
                                    <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                @elseif($chapterLesson->type === 'text')
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @elseif($chapterLesson->type === 'quiz')
                                    <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                @elseif($chapterLesson->type === 'assignment')
                                    <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                    </svg>
                                @endif
                                <a href="{{ route('course.lesson', [$course, $chapterLesson]) }}"
                                   class="flex-1 text-sm {{ $chapterLesson->id === $lesson->id ? 'font-semibold text-primary' : 'text-gray-700 hover:text-primary' }}">
                                    {{ $chapterLesson->title }}
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function markAsComplete() {
    const btn = document.getElementById('complete-btn');
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Memproses...';

    fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            status: 'completed',
            progress_percentage: 100
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Gagal memperbarui progress. Silakan coba lagi.');
            btn.disabled = false;
            btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan. Silakan coba lagi.');
        btn.disabled = false;
        btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
    });
}

function startQuiz() {
    alert('Fitur kuis akan segera tersedia!');
}

function startAssignment() {
    alert('Fitur tugas akan segera tersedia!');
}

// Auto-mark video lessons as in progress when video starts playing
@if($lesson->type === 'video')
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('lesson-video');
    if (video) {
        video.addEventListener('play', function() {
            fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: 'in_progress',
                    progress_percentage: 50
                })
            });
        });
    }
});
@endif
</script>
@endpush
@endsection
