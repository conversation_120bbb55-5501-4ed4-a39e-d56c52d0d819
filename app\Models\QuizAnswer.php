<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuizAnswer extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'attempt_id',
        'question_id',
        'selected_option_id',
        'answer_text',
        'is_correct',
        'points_earned',
        'max_points',
        'answered_at',
        'time_taken',
        'manual_score',
        'feedback',
        'graded_by',
        'graded_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_correct' => 'boolean',
        'points_earned' => 'integer',
        'max_points' => 'integer',
        'answered_at' => 'datetime',
        'time_taken' => 'integer',
        'manual_score' => 'integer',
        'graded_at' => 'datetime',
    ];

    /**
     * Get the quiz attempt that owns this answer.
     */
    public function attempt(): BelongsTo
    {
        return $this->belongsTo(QuizAttempt::class, 'attempt_id');
    }

    /**
     * Get the question that this answer belongs to.
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(QuizQuestion::class, 'question_id');
    }

    /**
     * Get the selected option (for multiple choice questions).
     */
    public function selectedOption(): BelongsTo
    {
        return $this->belongsTo(QuizQuestionOption::class, 'selected_option_id');
    }

    /**
     * Get the user who graded this answer.
     */
    public function gradedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    /**
     * Check if this answer has been graded manually.
     */
    public function isManuallyGraded(): bool
    {
        return !is_null($this->manual_score);
    }

    /**
     * Check if this answer needs manual grading.
     */
    public function needsManualGrading(): bool
    {
        return in_array($this->question->type, ['short_answer', 'essay']) && is_null($this->manual_score);
    }

    /**
     * Get the final score for this answer.
     */
    public function getFinalScore(): int
    {
        if ($this->isManuallyGraded()) {
            return $this->manual_score;
        }

        return $this->points_earned;
    }

    /**
     * Auto-grade the answer based on question type.
     */
    public function autoGrade(): void
    {
        $question = $this->question;

        switch ($question->type) {
            case 'multiple_choice':
                $this->gradeMultipleChoice();
                break;
            case 'true_false':
                $this->gradeTrueFalse();
                break;
            case 'short_answer':
            case 'essay':
                // These need manual grading
                $this->update([
                    'is_correct' => false,
                    'points_earned' => 0,
                ]);
                break;
        }
    }

    /**
     * Grade multiple choice answer.
     */
    private function gradeMultipleChoice(): void
    {
        $isCorrect = $this->selectedOption && $this->selectedOption->is_correct;
        
        $this->update([
            'is_correct' => $isCorrect,
            'points_earned' => $isCorrect ? $this->max_points : 0,
        ]);
    }

    /**
     * Grade true/false answer.
     */
    private function gradeTrueFalse(): void
    {
        $correctOption = $this->question->options()->where('is_correct', true)->first();
        $isCorrect = $this->selectedOption && $this->selectedOption->id === $correctOption->id;
        
        $this->update([
            'is_correct' => $isCorrect,
            'points_earned' => $isCorrect ? $this->max_points : 0,
        ]);
    }
}
