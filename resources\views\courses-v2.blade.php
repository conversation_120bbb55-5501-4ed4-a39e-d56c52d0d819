@extends('layouts.app')

@section('title', $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy')

@push('styles')
<meta name="description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta name="keywords" content="{{ $seoData['keywords'] ?? 'kursus programming, belajar coding, kursus teknologi' }}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{ $seoData['canonical'] ?? request()->url() }}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ request()->url() }}">
<meta property="og:title" content="{{ $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy' }}">
<meta property="og:description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta property="og:image" content="{{ asset('images/og-courses.jpg') }}">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ request()->url() }}">
<meta property="twitter:title" content="{{ $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy' }}">
<meta property="twitter:description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta property="twitter:image" content="{{ asset('images/og-courses.jpg') }}">

<!-- Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "EducationalOrganization",
  "name": "Ngambiskuy",
  "description": "Platform pembelajaran teknologi bertenaga AI untuk mengembangkan skill programming dan teknologi",
  "url": "{{ url('/') }}",
  "logo": "{{ asset('images/logo.png') }}",
  "courseMode": "online",
  "numberOfCourses": {{ $stats['total_courses'] }},
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": {{ number_format($stats['average_rating'], 1) }},
    "ratingCount": {{ $stats['total_students'] }}
  }
}
</script>

<style>
.filter-sidebar {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-right: 1px solid #e2e8f0;
}

.course-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e2e8f0;
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #FF6B35;
}

.price-range-slider {
    background: linear-gradient(to right, #FF6B35, #E55A2B);
}

.filter-chip {
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    color: white;
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 14px;
    font-weight: 500;
}

.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.hero-gradient {
    background: linear-gradient(135deg, #FF6B35 0%, #E55A2B 50%, #FF8C42 100%);
}

.search-input {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #FF6B35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

@media (max-width: 768px) {
    .filter-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 50;
        transition: left 0.3s ease;
    }
    
    .filter-sidebar.active {
        left: 0;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-gradient py-16 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-6">
                Kursus Teknologi
                <span class="text-yellow-200">Terdepan</span>
            </h1>
            <p class="text-xl text-white/90 leading-relaxed max-w-3xl mx-auto mb-8">
                Kuasai skill teknologi masa depan dengan {{ number_format($stats['total_courses']) }} kursus berkualitas tinggi.
                Bergabung dengan {{ number_format($stats['total_students']) }} siswa yang telah mengembangkan karir mereka.
            </p>
            
            <!-- Quick Stats -->
            {{-- <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="stats-card bg-white/10 backdrop-blur-sm border-white/20 text-white">
                    <div class="text-2xl font-bold mb-1">{{ number_format($stats['total_courses']) }}</div>
                    <div class="text-sm text-white/80">Kursus Berkualitas</div>
                </div>
                <div class="stats-card bg-white/10 backdrop-blur-sm border-white/20 text-white">
                    <div class="text-2xl font-bold mb-1">{{ number_format($stats['free_courses']) }}</div>
                    <div class="text-sm text-white/80">Kursus Gratis</div>
                </div>
                <div class="stats-card bg-white/10 backdrop-blur-sm border-white/20 text-white">
                    <div class="text-2xl font-bold mb-1">{{ number_format($stats['total_students']) }}</div>
                    <div class="text-sm text-white/80">Siswa Aktif</div>
                </div>
                <div class="stats-card bg-white/10 backdrop-blur-sm border-white/20 text-white">
                    <div class="text-2xl font-bold mb-1">{{ number_format($stats['average_rating'], 1) }}</div>
                    <div class="text-sm text-white/80">Rating Rata-rata</div>
                </div>
            </div> --}}
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <aside class="lg:w-80 filter-sidebar p-6 rounded-xl h-fit lg:sticky lg:top-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-900">Filter Kursus</h3>
                    <button class="lg:hidden text-gray-500 hover:text-gray-700" onclick="toggleMobileFilters()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form method="GET" action="{{ route('courses.index') }}" class="space-y-6">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Cari Kursus</label>
                        <div class="relative">
                            <input type="text" 
                                   name="search" 
                                   value="{{ request('search') }}"
                                   placeholder="Masukkan kata kunci..."
                                   class="search-input w-full pl-10 pr-4 py-3 text-sm focus:outline-none">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Kategori</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="category" value="" {{ !request('category') ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Kategori</span>
                            </label>
                            @foreach($categories as $category)
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm text-gray-700">{{ $category->name }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <!-- Level Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Level Kesulitan</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="level" value="" {{ !request('level') ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="beginner" {{ request('level') == 'beginner' ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🟢 Pemula</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="intermediate" {{ request('level') == 'intermediate' ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🟡 Menengah</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="advanced" {{ request('level') == 'advanced' ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🔴 Lanjutan</span>
                            </label>
                        </div>
                    </div>

                    <!-- Price Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Harga</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="" {{ !request('price_type') ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Harga</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="free" {{ request('price_type') == 'free' ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">💚 Gratis</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="paid" {{ request('price_type') == 'paid' ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">💎 Premium</span>
                            </label>
                        </div>
                    </div>

                    <!-- Rating Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Rating Minimum</label>
                        <div class="space-y-2">
                            @for($i = 4; $i >= 3; $i--)
                                <label class="flex items-center">
                                    <input type="radio" name="rating" value="{{ $i }}" {{ request('rating') == $i ? 'checked' : '' }} class="text-primary focus:ring-primary">
                                    <span class="ml-2 flex items-center">
                                        @for($j = 1; $j <= 5; $j++)
                                            <svg class="w-4 h-4 {{ $j <= $i ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endfor
                                        <span class="ml-1 text-sm text-gray-600">& ke atas</span>
                                    </span>
                                </label>
                            @endfor
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3 pt-4 border-t border-gray-200">
                        <button type="submit" class="w-full btn btn-primary py-3 text-sm font-semibold">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Terapkan Filter
                        </button>
                        @if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating']))
                            <a href="{{ route('courses.index') }}" class="w-full btn btn-outline py-3 text-sm font-semibold text-center block">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Reset Filter
                            </a>
                        @endif
                    </div>
                </form>
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1">
                <!-- Mobile Filter Toggle -->
                <div class="lg:hidden mb-6">
                    <button onclick="toggleMobileFilters()" class="flex items-center justify-center w-full py-3 px-4 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                        </svg>
                        Filter & Urutkan
                    </button>
                </div>

                @if($courses->count() > 0)
                    <!-- Results Header -->
                    <div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-200">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                                    {{ number_format($courses->total()) }} Kursus Ditemukan
                                </h2>
                                <p class="text-gray-600">
                                    @if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating']))
                                        Hasil pencarian untuk filter yang dipilih
                                    @else
                                        Semua kursus tersedia di platform kami
                                    @endif
                                </p>

                                <!-- Active Filters -->
                                @if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating']))
                                    <div class="flex flex-wrap gap-2 mt-3">
                                        @if(request('search'))
                                            <span class="filter-chip">
                                                Pencarian: "{{ request('search') }}"
                                                <a href="{{ request()->fullUrlWithQuery(['search' => null]) }}" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        @endif
                                        @if(request('category'))
                                            @php $category = $categories->where('slug', request('category'))->first(); @endphp
                                            @if($category)
                                                <span class="filter-chip">
                                                    {{ $category->name }}
                                                    <a href="{{ request()->fullUrlWithQuery(['category' => null]) }}" class="ml-2 text-white/80 hover:text-white">×</a>
                                                </span>
                                            @endif
                                        @endif
                                        @if(request('level'))
                                            <span class="filter-chip">
                                                Level: {{ ucfirst(request('level')) }}
                                                <a href="{{ request()->fullUrlWithQuery(['level' => null]) }}" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        @endif
                                        @if(request('price_type'))
                                            <span class="filter-chip">
                                                {{ request('price_type') == 'free' ? 'Gratis' : 'Premium' }}
                                                <a href="{{ request()->fullUrlWithQuery(['price_type' => null]) }}" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        @endif
                                        @if(request('rating'))
                                            <span class="filter-chip">
                                                Rating {{ request('rating') }}+ ⭐
                                                <a href="{{ request()->fullUrlWithQuery(['rating' => null]) }}" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        @endif
                                    </div>
                                @endif
                            </div>

                            <!-- Sort Options -->
                            <div class="flex items-center gap-3">
                                <label class="text-sm font-medium text-gray-700">Urutkan:</label>
                                <select onchange="window.location.href=this.value" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'newest']) }}" {{ request('sort') == 'newest' || !request('sort') ? 'selected' : '' }}>Terbaru</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'popular']) }}" {{ request('sort') == 'popular' ? 'selected' : '' }}>Terpopuler</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'rating']) }}" {{ request('sort') == 'rating' ? 'selected' : '' }}>Rating Tertinggi</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_low']) }}" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Harga Terendah</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_high']) }}" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Harga Tertinggi</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'duration_short']) }}" {{ request('sort') == 'duration_short' ? 'selected' : '' }}>Durasi Terpendek</option>
                                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'duration_long']) }}" {{ request('sort') == 'duration_long' ? 'selected' : '' }}>Durasi Terpanjang</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Courses Grid -->
                    <div class="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach($courses as $course)
                            <article class="course-card bg-white rounded-xl overflow-hidden group">
                                <div class="relative">
                                    @if($course->thumbnail)
                                        <img src="{{ asset('storage/' . $course->thumbnail) }}"
                                             alt="{{ $course->title }}"
                                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                    @else
                                        <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                            <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    @endif

                                    <!-- Badges -->
                                    <div class="absolute top-3 left-3 flex flex-col gap-2">
                                        @if($course->is_featured)
                                            <span class="bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                                                ⭐ FEATURED
                                            </span>
                                        @endif
                                        @if($course->price == 0)
                                            <span class="bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                                                💚 GRATIS
                                            </span>
                                        @endif
                                    </div>

                                    <!-- Price Badge -->
                                    @if($course->price > 0)
                                        <div class="absolute top-3 right-3 bg-primary text-white text-sm font-bold px-3 py-2 rounded-lg shadow-lg">
                                            {{ $course->formatted_price }}
                                        </div>
                                    @endif

                                    <!-- Completion Badge (if user is enrolled and completed) -->
                                    @auth
                                        @if(auth()->user()->hasCompletedCourse($course->id ?? 0))
                                            <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                                <div class="bg-green-600 text-white rounded-full p-4">
                                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        @endif
                                    @endauth
                                </div>

                                <div class="p-6">
                                    <!-- Category & Level -->
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="inline-block bg-primary/10 text-primary text-xs font-semibold px-3 py-1 rounded-full">
                                            {{ $course->category->name }}
                                        </span>
                                        <span class="text-xs font-semibold px-2 py-1 rounded-full
                                            {{ $course->level == 'beginner' ? 'bg-green-100 text-green-800' : '' }}
                                            {{ $course->level == 'intermediate' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                            {{ $course->level == 'advanced' ? 'bg-red-100 text-red-800' : '' }}">
                                            {{ $course->level_indonesian }}
                                        </span>
                                    </div>

                                    <!-- Title -->
                                    <h3 class="text-lg font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors">
                                        {{ $course->title }}
                                    </h3>

                                    <!-- Description -->
                                    <p class="text-gray-600 text-sm line-clamp-2 leading-relaxed mb-4">
                                        {{ $course->description }}
                                    </p>

                                    <!-- Instructor -->
                                    <div class="flex items-center space-x-3 mb-4">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-semibold text-gray-900">{{ $course->tutor->name }}</p>
                                            <p class="text-xs text-gray-500">Instruktur</p>
                                        </div>
                                    </div>

                                    <!-- Course Stats -->
                                    <div class="grid grid-cols-2 gap-4 py-3 mb-4 border-t border-gray-100">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-600">{{ $course->duration ?? '4 jam' }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-600">{{ number_format($course->total_students ?? 0) }} siswa</span>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    @if(($course->average_rating ?? 0) > 0)
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-2">
                                                <div class="flex items-center">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <svg class="w-4 h-4 {{ $i <= ($course->average_rating ?? 0) ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    @endfor
                                                </div>
                                                <span class="text-sm font-semibold text-gray-900">{{ number_format($course->average_rating ?? 0, 1) }}</span>
                                            </div>
                                            <span class="text-xs text-gray-500">({{ number_format($course->total_reviews ?? 0) }} ulasan)</span>
                                        </div>
                                    @endif

                                    <!-- Action Button -->
                                    <div class="pt-2">
                                        <a href="{{ route('course.show', $course) }}" class="btn btn-primary w-full group-hover:bg-primary-dark transition-colors py-3 font-semibold">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-12">
                        <div class="flex justify-center">
                            {{ $courses->links() }}
                        </div>
                    </div>
                @else
                    <!-- No Courses Found -->
                    <div class="bg-white rounded-xl p-12 text-center shadow-sm border border-gray-200">
                        <div class="max-w-md mx-auto">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">Tidak Ada Kursus Ditemukan</h3>
                            <p class="text-gray-600 mb-8 leading-relaxed">
                                Maaf, tidak ada kursus yang sesuai dengan kriteria pencarian Anda.
                                Coba ubah filter atau kata kunci untuk menemukan kursus yang tepat.
                            </p>
                            <div class="space-y-3">
                                <a href="{{ route('courses.index') }}" class="btn btn-primary btn-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Lihat Semua Kursus
                                </a>
                                <div class="text-sm text-gray-500">
                                    atau <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="text-primary hover:underline">coba kursus gratis</a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </main>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="hero-gradient py-20 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="max-w-3xl mx-auto">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                Siap Memulai Perjalanan Belajar Anda?
            </h2>
            <p class="text-xl text-white/90 mb-8 leading-relaxed">
                Bergabunglah dengan {{ number_format($stats['total_students']) }} profesional yang telah mengembangkan karir mereka bersama Ngambiskuy.
                Mulai hari ini dan raih masa depan yang lebih cerah!
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="btn btn-lg bg-white text-primary hover:bg-gray-100 font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Daftar Sekarang
                </a>
                <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-primary font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Coba Gratis
                </a>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function toggleMobileFilters() {
    const sidebar = document.querySelector('.filter-sidebar');
    sidebar.classList.toggle('active');
}

// Close mobile filters when clicking outside
document.addEventListener('click', function(event) {
    const sidebar = document.querySelector('.filter-sidebar');
    const toggleButton = document.querySelector('[onclick="toggleMobileFilters()"]');

    if (window.innerWidth < 1024 &&
        !sidebar.contains(event.target) &&
        !toggleButton.contains(event.target) &&
        sidebar.classList.contains('active')) {
        sidebar.classList.remove('active');
    }
});

// Auto-submit form when radio buttons change
document.querySelectorAll('input[type="radio"]').forEach(radio => {
    radio.addEventListener('change', function() {
        // Add a small delay to improve UX
        setTimeout(() => {
            this.closest('form').submit();
        }, 100);
    });
});

// Smooth scroll to courses section when clicking hero CTA
document.addEventListener('DOMContentLoaded', function() {
    const ctaButtons = document.querySelectorAll('a[href="#courses"]');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector('#courses') || document.querySelector('main');
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});

// Add loading state to filter form
document.querySelector('form').addEventListener('submit', function() {
    const submitButton = this.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Mencari...';
        submitButton.disabled = true;
    }
});
</script>
@endpush
@endsection
