@extends('layouts.app')

@section('title', $exam->title . ' - <PERSON><PERSON>an <PERSON>')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
    <!-- Breadcrumb -->
    <div class="bg-white border-b border-orange-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-700 hover:text-primary">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('exams.index') }}" class="ml-1 text-gray-700 hover:text-primary md:ml-2">Ujian</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-500 md:ml-2">{{ $exam->title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Exam Header -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-4">
                                @if($exam->price == 0)
                                    <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">GRATIS</span>
                                @else
                                    <span class="bg-primary/10 text-primary text-sm px-3 py-1 rounded-full font-medium">PREMIUM</span>
                                @endif
                                @if($exam->category)
                                    <span class="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">{{ $exam->category->name }}</span>
                                @endif
                            </div>
                            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">{{ $exam->title }}</h1>
                            <p class="text-lg text-gray-600 leading-relaxed">{{ $exam->description }}</p>
                        </div>
                    </div>

                    <!-- Exam Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 p-6 bg-gray-50 rounded-xl">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary">{{ $stats['total_questions'] }}</div>
                            <div class="text-sm text-gray-600">Soal</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $exam->time_limit }}</div>
                            <div class="text-sm text-gray-600">Menit</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ $exam->passing_score }}%</div>
                            <div class="text-sm text-gray-600">Batas Lulus</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $stats['total_enrollments'] }}</div>
                            <div class="text-sm text-gray-600">Peserta</div>
                        </div>
                    </div>
                </div>

                <!-- Exam Details -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Detail Ujian</h2>
                    
                    <div class="space-y-6">
                        <!-- What You'll Learn -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Yang Akan Diuji</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600">{{ $exam->description }}</p>
                            </div>
                        </div>

                        <!-- Requirements -->
                        @if($exam->requirements)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Persyaratan</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600">{{ $exam->requirements }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Exam Rules -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Aturan Ujian</h3>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <ul class="space-y-2 text-sm text-blue-800">
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Waktu ujian: {{ $exam->time_limit }} menit
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Batas kelulusan: {{ $exam->passing_score }}%
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Maksimal {{ $exam->max_attempts }} kali percobaan
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Jawaban tersimpan otomatis
                                    </li>
                                    @if($exam->certificate_enabled)
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Sertifikat digital untuk yang lulus
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tutor Info -->
                @if($exam->tutor)
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Pembuat Ujian</h2>
                    <div class="flex items-center gap-4">
                        <img src="{{ $exam->tutor->profile_picture ? asset('storage/' . $exam->tutor->profile_picture) : asset('images/avatars/placeholder.svg') }}" 
                             alt="{{ $exam->tutor->name }}" 
                             class="w-16 h-16 rounded-full object-cover">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $exam->tutor->name }}</h3>
                            <p class="text-gray-600">{{ $exam->tutor->title ?? 'Professional Instructor' }}</p>
                            @if($exam->tutor->bio)
                                <p class="text-sm text-gray-500 mt-1">{{ Str::limit($exam->tutor->bio, 100) }}</p>
                            @endif
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Enrollment Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100 sticky top-6">
                    <div class="text-center mb-6">
                        @if($exam->price == 0)
                            <div class="text-3xl font-bold text-green-600 mb-2">GRATIS</div>
                            <p class="text-gray-600">Ujian gratis untuk semua</p>
                        @else
                            <div class="text-3xl font-bold text-gray-900 mb-2">Rp {{ number_format($exam->price, 0, ',', '.') }}</div>
                            <p class="text-gray-600">Investasi untuk sertifikasi</p>
                        @endif
                    </div>

                    <!-- Status Message -->
                    @if($statusMessage)
                        <div class="mb-4 p-4 rounded-lg border {{
                            $examStatus === 'can_take' ? 'bg-green-50 border-green-200 text-green-800' :
                            ($examStatus === 'has_active_attempt' ? 'bg-blue-50 border-blue-200 text-blue-800' :
                            ($examStatus === 'max_attempts_reached' ? 'bg-red-50 border-red-200 text-red-800' :
                            ($examStatus === 'payment_pending' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
                            'bg-gray-50 border-gray-200 text-gray-800'))) }}">
                            <div class="flex items-start gap-3">
                                @if($examStatus === 'can_take')
                                    <svg class="w-5 h-5 mt-0.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                @elseif($examStatus === 'has_active_attempt')
                                    <svg class="w-5 h-5 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                @elseif($examStatus === 'max_attempts_reached')
                                    <svg class="w-5 h-5 mt-0.5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                @elseif($examStatus === 'payment_pending')
                                    <svg class="w-5 h-5 mt-0.5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 mt-0.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                @endif
                                <div>
                                    <p class="font-medium">{{ $statusMessage }}</p>
                                    @if($examStatus === 'max_attempts_reached' && $userAttempts->count() > 0)
                                        <p class="text-sm mt-1">
                                            Percobaan terakhir: {{ $userAttempts->first()->created_at->format('d M Y, H:i') }}
                                            @if($userAttempts->first()->is_passed)
                                                <span class="text-green-600 font-medium">(Lulus - {{ number_format($userAttempts->first()->score_percentage, 1) }}%)</span>
                                            @else
                                                <span class="text-red-600 font-medium">(Tidak Lulus - {{ number_format($userAttempts->first()->score_percentage, 1) }}%)</span>
                                            @endif
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    @auth
                        @if($examStatus === 'has_active_attempt')
                            <a href="{{ route('exams.take', $exam) }}"
                               class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                                🔄 Lanjutkan Ujian
                            </a>
                        @elseif($examStatus === 'can_take')
                            <a href="{{ route('exams.take', $exam) }}"
                               class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                                🚀 Mulai Ujian Professional
                            </a>
                        @elseif($examStatus === 'not_enrolled')
                            <form action="{{ route('exams.enroll', $exam) }}" method="POST">
                                @csrf
                                <button type="submit"
                                        class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 mb-4 shadow-lg">
                                    {{ $exam->price == 0 ? '📝 Daftar Gratis' : '💳 Daftar Ujian' }}
                                </button>
                            </form>
                        @elseif($examStatus === 'payment_pending')
                            <button disabled
                                    class="w-full bg-gray-400 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 cursor-not-allowed">
                                💳 Menunggu Pembayaran
                            </button>
                        @elseif($examStatus === 'max_attempts_reached')
                            <button disabled
                                    class="w-full bg-gray-400 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 cursor-not-allowed">
                                ❌ Batas Percobaan Tercapai
                            </button>
                        @endif
                    @else
                        <a href="{{ route('login') }}"
                           class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                            🔐 Login untuk Mengikuti Ujian
                        </a>
                    @endauth

                    <!-- Exam Features -->
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>{{ $stats['total_questions'] }} soal berkualitas</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Auto-save jawaban</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Hasil langsung</span>
                        </div>
                        @if($exam->certificate_enabled)
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Sertifikat digital</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Info -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Ujian</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tingkat:</span>
                            <span class="font-medium text-gray-900">{{ ucfirst($exam->difficulty_level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Durasi:</span>
                            <span class="font-medium text-gray-900">{{ $exam->time_limit }} menit</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Percobaan:</span>
                            <span class="font-medium text-gray-900">{{ $exam->max_attempts }}x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Peserta:</span>
                            <span class="font-medium text-gray-900">{{ $stats['total_enrollments'] }}</span>
                        </div>
                        @if($stats['average_score'] > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Rata-rata Skor:</span>
                            <span class="font-medium text-gray-900">{{ number_format($stats['average_score'], 1) }}%</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- User Attempt History -->
                @auth
                    @if($userAttempts->count() > 0)
                        <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Riwayat Percobaan Anda</h3>
                            <div class="space-y-3">
                                @foreach($userAttempts as $attempt)
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold {{ $attempt->is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $attempt->attempt_number }}
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-900">
                                                    Percobaan {{ $attempt->attempt_number }}
                                                    @if($attempt->status === 'in_progress')
                                                        <span class="text-blue-600 text-sm">(Sedang Berlangsung)</span>
                                                    @endif
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    {{ $attempt->created_at->format('d M Y, H:i') }}
                                                    @if($attempt->completed_at)
                                                        - Selesai: {{ $attempt->completed_at->format('H:i') }}
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            @if($attempt->status === 'completed')
                                                <div class="text-lg font-bold {{ $attempt->is_passed ? 'text-green-600' : 'text-red-600' }}">
                                                    {{ number_format($attempt->score_percentage, 1) }}%
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    {{ $attempt->is_passed ? 'Lulus' : 'Tidak Lulus' }}
                                                </div>
                                            @elseif($attempt->status === 'in_progress')
                                                <div class="text-sm text-blue-600 font-medium">
                                                    Berlangsung
                                                </div>
                                            @elseif($attempt->status === 'expired')
                                                <div class="text-sm text-orange-600 font-medium">
                                                    Waktu Habis
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="text-sm text-blue-800">
                                    <strong>Percobaan:</strong> {{ $userAttempts->count() }} dari {{ $exam->max_attempts }} kali
                                    @if($userAttempts->where('is_passed', true)->count() > 0)
                                        <br><strong>Status:</strong> <span class="text-green-600 font-medium">Sudah Lulus</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                @endauth
            </div>
        </div>
    </div>
</div>
@endsection
