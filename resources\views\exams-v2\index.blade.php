@extends('layouts.app')

@section('title', 'Ujian Professional - Ngambiskuy')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-orange-50 via-white to-orange-100 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
                🎯 Sistem Ujian Professional
            </div>
            <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                <PERSON><PERSON><PERSON>
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-orange-500">
                    Professional
                </span>
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Uji kemampuan Anda dengan ujian sertifikasi professional yang dirancang oleh para ahli industri
            </p>
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-primary">{{ $stats['total_exams'] ?? 0 }}+</div>
                    <div class="text-sm text-gray-600">Ujian Tersedia</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-green-600">{{ $stats['total_participants'] ?? 0 }}+</div>
                    <div class="text-sm text-gray-600">Peserta</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-blue-600">{{ $stats['certificates_issued'] ?? 0 }}+</div>
                    <div class="text-sm text-gray-600">Sertifikat</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-purple-600">{{ $stats['success_rate'] ?? 85 }}%</div>
                    <div class="text-sm text-gray-600">Tingkat Kelulusan</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<div class="bg-white shadow-sm border-b border-orange-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="GET" action="{{ route('exams.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Ujian</label>
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Nama ujian atau deskripsi...">
                    </div>
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                    <select name="category" id="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Kategori</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Difficulty -->
                <div>
                    <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Tingkat Kesulitan</label>
                    <select name="difficulty" id="difficulty" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Tingkat</option>
                        <option value="beginner" {{ request('difficulty') == 'beginner' ? 'selected' : '' }}>Pemula</option>
                        <option value="intermediate" {{ request('difficulty') == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                        <option value="advanced" {{ request('difficulty') == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                    </select>
                </div>

                <!-- Price Filter -->
                <div>
                    <label for="price_filter" class="block text-sm font-medium text-gray-700 mb-1">Harga</label>
                    <select name="price_filter" id="price_filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Harga</option>
                        <option value="free" {{ request('price_filter') == 'free' ? 'selected' : '' }}>Gratis</option>
                        <option value="paid" {{ request('price_filter') == 'paid' ? 'selected' : '' }}>Berbayar</option>
                    </select>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari Ujian
                </button>
                @if(request()->hasAny(['search', 'category', 'difficulty', 'price_filter']))
                    <a href="{{ route('exams.index') }}" class="btn btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Reset Filter
                    </a>
                @endif
            </div>
        </form>
    </div>
</div>

<!-- Exams Grid -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($exams->count() > 0)
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($exams as $exam)
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100">
                        <!-- Exam Header -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="flex justify-between items-start mb-4">
                                @if($exam->price == 0)
                                    <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">GRATIS</span>
                                @else
                                    <span class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full font-medium">PREMIUM</span>
                                @endif
                                <span class="text-gray-500 text-sm">{{ $exam->questions_count }} soal</span>
                            </div>
                            
                            <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{{ $exam->title }}</h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $exam->description }}</p>
                            
                            @if($exam->category)
                                <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $exam->category->name }}</span>
                            @endif
                        </div>

                        <!-- Exam Details -->
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-6">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>{{ $exam->time_limit }} menit</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>{{ $exam->passing_score }}% lulus</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span>{{ ucfirst($exam->difficulty_level) }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <span>{{ $exam->enrollments_count }} peserta</span>
                                </div>
                            </div>

                            <!-- Price and Action -->
                            <div class="flex items-center justify-between">
                                <div>
                                    @if($exam->price == 0)
                                        <span class="text-2xl font-bold text-green-600">GRATIS</span>
                                    @else
                                        <span class="text-2xl font-bold text-gray-900">Rp {{ number_format($exam->price, 0, ',', '.') }}</span>
                                    @endif
                                </div>
                                <a href="{{ route('exams.show', $exam) }}" class="btn btn-primary">
                                    {{ $exam->price == 0 ? 'Mulai Gratis' : 'Daftar Ujian' }}
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($exams->hasPages())
                <div class="mt-12">
                    {{ $exams->links() }}
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Ujian</h3>
                <p class="text-gray-600 mb-6">Tidak ada ujian yang sesuai dengan kriteria pencarian Anda.</p>
                <a href="{{ route('exams.index') }}" class="btn btn-primary">
                    Lihat Semua Ujian
                </a>
            </div>
        @endif
    </div>
</section>
@endsection
